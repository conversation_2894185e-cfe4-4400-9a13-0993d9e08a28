export const colorList = ['gray', 'blue', 'cyan', 'orange', 'green', 'purple'];
export const unitsMap = new Map([
  ['ms', '毫秒'],
  ['fps', 'FPS帧率'],
  ['kb', 'KB'],
  ['mb', 'MB'],
  ['b', 'B'],
  ['kbps', 'KB/s'],
  ['mbps', 'MB/s'],
  ['bps', 'B/s'],
  ['cpu_normalized_total_usage', 'CPU标准化总使用率百分比 %'],
  ['cpu_normalized_proc_usage', 'CPU标准化APP使用率百分比 %'],
  ['cpu_total_usage', 'CPU总使用率百分比'],
  ['cpu_proc_usage', 'CPU进程使用率百分比'],
  ['mem_total_footprint', 'Footprint内存总占用 KB'],
  ['mem_footprint', 'Footprint内存APP占用 KB'],
  ['mem_pss', 'PSS内存  KB'],
  ['mem_uss', 'USS内存  KB'],
  ['battery_power', '功耗 μW'],
  ['frame_rate_sent', '编码帧率'],
  ['energy_cpu_cost', 'CPU功耗'],
  ['mem_dalvik_heap', '虚拟机堆内存 KB'],
  ['mem_dalvik_other', '虚拟机其他内存 KB'],
  ['mem_resident', '驻留内存 KB'],
  ['battery_current', '电池电流current mAh'],
  ['battery_amperage', '电池电流amperage mAh'],
  ['battery_voltage', '电池电压voltage V'],
  ['battery_temp', '电池温度 ℃'],
  ['energy_cost', '总功耗 μW'],
  ['energy_location_cost', '位置传感器功耗'],
  ['energy_overhead_cost', '其他系统硬件功耗'],
  ['energy_thermal_cost', '系统温度功耗'],
  ['gpu_device', 'GPU设备使用率 %'],
  ['gpu_usage', 'GPU使用率 %'],
  ['gpu_load', 'GPU负载'],
  ['gpu_render', 'GPU渲染使用率 %'],
  ['gpu_tiler', 'GPUtiler使用率 %'],
  ['energy_gpu_cost', 'GPU功耗'],
  ['fd_count', '文件描述符数量 个'],
  ['thread_count', '线程数量 个'],
  ['net_proc_receive', 'APP的接收流量 Byte/s'],
  ['net_proc_sent', 'APP的发送流量 Byte/s'],
  ['net_total_receive', '网络总接收量 Byte/s'],
  ['net_total_sent', '网络总发送量 Byte/s'],
  ['energy_net_cost', '网络功耗'],
  ['disk_read', '磁盘读取 Byte/s'],
  ['disk_written', '磁盘写入 Byte/s'],
  ['ctx_switch', '上下文切换次数 次'],
  ['int_wakeups', '中断唤醒次 次'],
]);
export const defaultCountrys = {
  "phone_area_code":"86",
  "country_code_alpha2":"CN",
  "country":"中国大陆"
}

export const business_map =  new Map(
  [
    [1, '业务1'],
    [2, '业务2'],
    [3, '业务3'],
    [4, '业务4'],
    [5, '业务5'],
    [6, '业务6'],
  ]
)
export const platform_map =  new Map(
  [
    [1, 'Android'],
    [2, 'iOS'],
    [3, 'other'],
  ]
)
export const app_type_map =  new Map(
  [
    [1, '性能包'],
    [2, '基准包'],
    [3, '辅助包']
  ]
)

export const developer_list=[
  // "yinyanting.2022",
  "hejiabei.oxep",
  "chenli.gogo"
]

export const comparison_type_list =
  [
    {key:1,value:1,"label":"增加"},
    {key:2,value:2,"label":"减少"},
  ]
export const INCREASE = 1
export const DECREASE = 2

export const value_type_list = 
  [
    {key:1,value:1,"label":"百分比"},
    {key:2,value:2,"label":"绝对值"},
  ]
export const PERCENTAGE = 1
export const ABSOLUTE = 2

export const android_fields = [
  'cpu_normalized_total_usage',
  'cpu_normalized_proc_usage',
  'cpu_total_usage',
  'cpu_proc_usage',
]

export const ios_fields = [
  'cpu_normalized_total_usage',
  'cpu_normalized_proc_usage',
  'cpu_total_usage',
  'cpu_proc_usage',
  'mem_total_footprint',
  'mem_footprint',
  'mem_pss',
]

// 实验组和对照组
export const EXPERIMENT_GROUP = 1
export const CONTROL_GROUP = 2
export const version_type_map = new Map([
  [CONTROL_GROUP, '对照组'],
  [EXPERIMENT_GROUP, '实验组'],
])
export const version_type_dict = {
  CONTROL_GROUP: "对照组",
  EXPERIMENT_GROUP: "实验组"
}
// 任务类型，分别是版本回归和libra实验任务
export const TASK_TYPE_1 = 1
export const TASK_TYPE_2 = 2

export const libraHitTypeList = [
  { id: 1, name: 'did命中' },
  { id: 2, name: 'uid命中' }
]
export const libraHitTypeMap = new Map([
  [1, 'did命中'],
  [2, 'uid命中']
])

export const constants = {
  EXPERIMENT_GROUP: 1,
  CONTROL_GROUP: 2,
  version_type_map: new Map([
    [CONTROL_GROUP, '对照组'],
    [EXPERIMENT_GROUP, '实验组'],
  ]),
  version_type_dict: {
  CONTROL_GROUP: "对照组",
  EXPERIMENT_GROUP: "实验组"
},
// 任务类型，分别是版本回归和libra实验任务
TASK_TYPE_1:1,
TASK_TYPE_2:2,

libraHitTypeList: [
  { id: 1, name: 'did命中' },
  { id: 2, name: 'uid命中' }
],
libraHitTypeMap:new Map([
  [1, 'did命中'],
  [2, 'uid命中']
]),
metricTypeMap: new Map([
  [1, '公共字段'],
  [2, '安卓字段'],
  [3, 'iOS字段'],
]),
  TASK_STATUS_NOT_EXECUTE : 0,
  TASK_STATUS_WAITING : 1,
  TASK_STATUS_RUNNING : 2,
  TASK_STATUS_SUCCESS : 3,
  TASK_STATUS_ERROR : 4,
  TASK_STATUS_RETRY_PENDING : 5,
  TASK_STATUS_RETRY_RUNNING : 6,
  TASK_STATUS_CANCELED : 7,
  TASK_STATUS_TIMEOUT : 8,

// 性能采集工具类型
  PERF_TOOL_TYPE_DS : 1,
  PERF_TOOL_TYPE_GAMEPERF : 2,




}
