/**
 * URL格式兼容性工具函数
 * 用于处理字段从字符串格式到JSON格式的变更
 */

/**
 * 获取日志URL列表（兼容新旧格式）
 * @param data 数据对象
 * @returns 返回 {filename: url} 格式的对象，如果没有数据返回 null
 */
export function getLogUrls(data: any): Record<string, string> | null {
  // 新格式：case_log_tos_urls (JSON)
  if (data.case_log_tos_urls) {
    return data.case_log_tos_urls;
  }
  
  // 兼容旧格式：case_log_tos_url (字符串)
  if (data.case_log_tos_url) {
    return { "case.log": data.case_log_tos_url };
  }
  
  return null;
}

/**
 * 获取性能数据URL列表（兼容新旧格式）
 * @param data 数据对象
 * @returns 返回 {filename: url} 格式的对象，如果没有数据返回 null
 */
export function getPerfDataUrls(data: any): Record<string, string> | null {
  // 新格式：perf_data_tos_urls (JSON)
  if (data.perf_data_tos_urls) {
    return data.perf_data_tos_urls;
  }
  
  // 兼容旧格式：perf_data_tos_url (字符串)
  if (data.perf_data_tos_url) {
    return { "perf_data.json": data.perf_data_tos_url };
  }
  
  return null;
}

/**
 * 获取第一个性能数据URL（用于向后兼容）
 * @param data 数据对象
 * @returns 返回第一个URL字符串，如果没有数据返回 null
 */
export function getFirstPerfDataUrl(data: any): string | null {
  const urls = getPerfDataUrls(data);
  if (!urls) return null;
  
  const urlValues = Object.values(urls);
  return urlValues.length > 0 ? urlValues[0] : null;
}

/**
 * 获取第一个日志URL（用于向后兼容）
 * @param data 数据对象
 * @returns 返回第一个URL字符串，如果没有数据返回 null
 */
export function getFirstLogUrl(data: any): string | null {
  const urls = getLogUrls(data);
  if (!urls) return null;
  
  const urlValues = Object.values(urls);
  return urlValues.length > 0 ? urlValues[0] : null;
}

/**
 * 渲染多个URL链接的JSX元素
 * @param urls URL对象 {filename: url}
 * @param linkText 链接文本前缀，默认为空
 * @returns JSX元素数组
 */
export function renderUrlLinks(urls: Record<string, string> | null, linkText: string = ''): JSX.Element[] {
  if (!urls) return [];
  
  return Object.entries(urls).map(([filename, url]) => (
    <a 
      key={filename} 
      href={url} 
      target="_blank" 
      rel="noopener noreferrer"
      style={{ marginRight: '8px', color: '#4080ff' }}
    >
      {linkText ? `${linkText}${filename}` : filename}
    </a>
  ));
}

/**
 * 检查是否有可用的URL数据
 * @param data 数据对象
 * @param type URL类型：'log' | 'perf'
 * @returns 是否有可用数据
 */
export function hasUrlData(data: any, type: 'log' | 'perf'): boolean {
  if (type === 'log') {
    return !!(data.case_log_tos_urls || data.case_log_tos_url);
  } else if (type === 'perf') {
    return !!(data.perf_data_tos_urls || data.perf_data_tos_url);
  }
  return false;
}

/**
 * 获取URL数据的统计信息
 * @param data 数据对象
 * @returns 统计信息对象
 */
export function getUrlStats(data: any) {
  const logUrls = getLogUrls(data);
  const perfUrls = getPerfDataUrls(data);
  
  return {
    logCount: logUrls ? Object.keys(logUrls).length : 0,
    perfCount: perfUrls ? Object.keys(perfUrls).length : 0,
    hasLogs: !!logUrls,
    hasPerf: !!perfUrls
  };
}
