import { createProxy } from '@devsre/request';
import dayjs from 'dayjs';

const proxy = createProxy({
  consul: {
    psm: 'devsre.app.proxy',
  },
});

function random(min, max) {
  return Math.floor(Math.random() * (max - min)) + min;
}

export const Apis = {
  getBnpmDownPackageBulk: async ({ payload }) => {
    const res = await proxy.get('/api/getBnpmDownPackageBulk', {
      params: payload,
    });
    return res.data;
  },

  getBnpmDownPackage: async ({ payload }) => {
    const res = await proxy.get('/api/getBnpmDownPackage', {
      params: payload,
    });
    return res.data;
  },

  getProjectList: async ({ payload }) => {
    const res = await proxy.get('api/getProjectList', {
      params: payload,
    });
    return res.data;
  },

  getContentPercentage: async () => {
    return {
      data: [
        {
          type: 'Text',
          count: 148564,
          percent: 0.16,
        },
        {
          type: 'Image',
          count: 334271,
          percent: 0.36,
        },
        {
          type: 'Video',
          count: 445695,
          percent: 0.48,
        },
      ],
    };
  },

  getAnnouncement: async () => {
    const data = [
      {
        type: 'activity',
        key: '1',
        tag: 'Activity',
        content: 'ContentLatest Promotions',
      },
      {
        type: 'info',
        key: '2',
        tag: 'Info',
        content: 'The new content has not passed the review, please click to view for details.',
      },
      {
        type: 'notice',
        key: '3',
        tag: 'Notice',
        content:
          'The current product trial period is about to end, if you need to renew, please click to view.',
      },
      {
        type: 'notice',
        key: '4',
        tag: 'Messages',
        content: 'Notice of new system upgrade plan in January',
      },
      {
        type: 'info',
        key: '5',
        tag: 'Notice',
        content: 'The new content has been reviewed. For details, please click to view.',
      },
      {
        type: 'notice',
        key: '6',
        tag: 'Activity',
        content: 'The new content has been reviewed. For details, please click to view.',
      },
    ];
    return {
      data,
    };
  },

  // table api
  getList: async () => {
    const qualityCategory = ['Video', 'Image', 'Text'];
    const qualityName = [
      'Import history',
      'Content copyright',
      'Sensitive content',
      'Commercial brand',
    ];
    const serviceName = [
      'Funnel analysis',
      'User distribution',
      'Resource distribution',
      'portrait analysis',
      'Event analysis',
    ];
    const serviceDescriptions = [
      'User behavior analysis funnel analysis model is very important.',
      'Quickly diagnose user scope, regional division, and understand the concentration of data distribution.',
      'Mobile dynamic resource distribution solution.',
      'User portrait is to annotate typical user information and build an annotated user model.',
      'Event analysis can perform flexible multidimensional data analysis of filtering, grouping, and aggregation. Click on the card for details.',
    ];
    const rulesName = [
      'Content blocking rules',
      'Content sticking rules',
      'Content Weighting Rules',
      'Content Distribution Rules',
      'Multilingual text symbol recognition',
    ];
    const rulesDescription = [
      'When users perform specific content distribution tasks, content blocking rules can be used to filter content collections based on specific tags.',
      'This rule allows users to pin a few fixed pieces of content to the top when performing specific content distribution tasks.',
      'After selecting content weighting rules, you can customize the probability of obtaining content from different content collections.',
      'When content is distributed, some content needs to be fixed in the position displayed on the C-side.',
      'Accurately identify English, Uighur, Tibetan, Mongolian, Korean and other languages ​​as well as semantic recognition of emoji expressions.',
    ];
    const quality = new Array(10).fill({
      title: `${qualityCategory[random(0, 2)]}-${qualityName[random(0, 3)]}`,
      time: dayjs().subtract(random(0, 30), 'days').format('YYYY-MM-DD HH:mm:ss'),
      qualityCount: random(100, 500),
      randomCount: random(0, 100),
      duration: random(0, 200),
    });

    const service = new Array(9).fill({
      icon: random(0, serviceName.length - 1),
      title: serviceName[random(0, serviceName.length - 1)],
      description: serviceDescriptions[random(0, serviceName.length - 1)],
      status: random(0, 2),
    });
    const rules = new Array(8).fill({
      index: random(0, rulesName.length - 1),
      title: rulesName[random(0, rulesName.length - 1)],
      description: rulesDescription[random(0, rulesName.length - 1)],
      status: random(0, 1),
    });

    return {
      data: {
        quality,
        service,
        rules,
      },
    };
  },

  // data-analysis
  getChartOverview: async () => {
    const legend = ['Active users', 'Content production', 'Content clicks', 'Content exposure'];
    const count = [0, 600, 1000, 2000, 4000];
    const overviewData = new Array(4).fill('').map(() => random(0, 10000));
    const getLineData = (name, index) => {
      const list = new Array(10)
        .fill({})
        .map(() => ({ count: random(count[index], count[index + 1]), name: name, time: '' }));

      return list.map((item, index) => {
        item.time = dayjs()
          .subtract(index + 1, 'days')
          .format('MM-DD');
        return item;
      });
    };

    let list = [];
    legend.forEach((name, index) => (list = list.concat(getLineData(name, index))));
    const data = {
      overviewData,
      chartData: list,
    };

    return {
      data,
    };
  },

  getChartInterval: async () => {
    const interval = ['Share', 'Comment', 'Likes'];
    const data = new Array(3).fill({}).map((item, index) => {
      return {
        name: interval[index],
        count: random(1000, 10000),
      };
    });
    return {
      data,
    };
  },

  getChartPlor: async () => {
    const items = [
      'Internationality',
      'Entertainment',
      'Physical Education',
      'Finance',
      'Technology',
      'Other',
    ];
    const category = ['Text', 'Image', 'Video'];

    const getCategoryCount = () => {
      const result = {};
      category.forEach((name) => {
        result[name] = random(0, 100);
      });

      return result;
    };

    const data = {
      list: items.map((item) => ({
        item,
        ...getCategoryCount(),
      })),
      fields: category,
    };

    return {
      data,
    };
  },

  getChartCard: async ({ payload }) => {
    const { type } = payload;
    const mockLine = (name) => {
      const result = new Array(12).fill(0).map(() => ({
        y: random(1000, 10000),
      }));
      return result
        .sort((a, b) => a.y - b.y)
        .map((item, index) => ({
          ...item,
          x: index,
          name,
        }));
    };

    const data = {
      count: random(1000, 10000),
      increment: random(1, 10) > 5 ? true : false,
      diff: random(100, 1000),
      chartType: type,
      chartData: mockLine('Category 1'),
    };

    return {
      data,
    };
  },

  getChartMultiPie: async () => {
    const category = ['Text', 'Image', 'Video'];
    const getContentSource = (name) => {
      const typeList = [
        'UGC Original',
        'Foreign website',
        'Reprinted articles',
        'Industry Reports',
        'Other',
      ];
      const result = [];
      typeList.forEach((type) => {
        result.push({
          type,
          value: random(100, 10000),
          name,
        });
      });
      const total = result.reduce((a, b) => a + b.value, 0);
      return result.map((item) => ({
        ...item,
        value: Number((item.value / total).toFixed(2)),
      }));
    };

    const allList = category.map((name) => {
      return getContentSource(name).map((item) => ({
        ...item,
        category: name,
      }));
    });

    return {
      data: allList.flat(),
    };
  },
};
