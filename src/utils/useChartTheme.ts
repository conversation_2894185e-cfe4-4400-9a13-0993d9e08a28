import { getTheme, registerTheme } from 'bizcharts/es/g2-all';
import { useEffect, useState } from 'react';
import { useTheme } from '@devsre/react-utils';

const defaultDarkTheme = getTheme('dark');

registerTheme('darkTheme', {
  ...defaultDarkTheme,
  background: 'transparent',
});

function useBizTheme() {
  const [theme] = useTheme();
  const themeName = theme === 'dark' ? 'darkTheme' : 'light';
  const [themeObj, setThemeObj] = useState(getTheme(themeName));

  useEffect(() => {
    const themeName = theme === 'dark' ? 'darkTheme' : 'light';
    const newTheme = getTheme(themeName);
    setThemeObj(newTheme);
  }, [theme]);

  return themeObj;
}

export default useBizTheme;
