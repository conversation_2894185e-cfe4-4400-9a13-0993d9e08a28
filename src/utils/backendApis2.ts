import { createProxy, writeProxyInstance } from '@devsre/request';
import { Message } from '@arco-design/web-react';

const proxy = createProxy('test_platform', {
  consul: {
    psm: 'global.rtc.test_platform',
  },
});

// proxy.interceptors.request.use(async (config) => {

//   const domainID = await getCurrentDomainID();
//   config.headers['X-Jwt-Token'] = await getJwt(domainID);
//   return config;
// });

// const proxy = createDomainProxy({
//   // proxyDomain 要和在控制中心填写的 domain 白名单一模一样（包括协议，路径，端口）
//   // proxyDomain: "http://100.84.195.188:8000"
//   proxyDomain: "http://10.74.76.80:8001"
// });

const CI_DAILY_API = '/api/test_manager/ci_daily';
const VERSION_LIST_API = '/api/version_manager/list';
const VERSION_DETAIL_API = '/api/version_manager/detail';
const UPLOAD_PERF_UI_AUTO_API = '/api/perf_ui_auto';
const PERF_TASK_API = '/perf_task';
const PERF_APP_API = '/perf_app';
const PERF_CASE_API = '/perf_case';

const LIBRA_EXPERIMENT_API = '/api/libra_experiment';
const BUSINESS_API = '/api/business';

writeProxyInstance('test_platform', (instance) => {
  instance.interceptors.request.use(
    (config) => {
      console.log('config');
      console.log(config);
      return config;
    },
    (error) => {
      console.log('request error');
      console.log(error);
      // Message.error(error)
      return Promise.reject(error);
    }
  );
  instance.interceptors.response.use(
    (response) => {
      console.log('response');
      console.log(response);
      return response;
    },
    (error) => {
      console.log('response error');
      console.log(error);
      Message.error(
        `${error.config.url} response error: code=[${error.response.status}]  msg=[${error.response.statusText}, ${error.response.data.message}]`
      );
      return Promise.reject(error);
    }
  );
});

export const BackendApis2 = {
  // 性能UI自动化相关
  getReportData: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_report/report_data`, body);
    return res.data.data;
  },
  setVersionList: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + '/perf_report/version_list', {
      params: payload,
    });
    return res.data.data.version_list;
  },
  getClientList: async () => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + '/perf_client/client_list');
    return res.data.data;
  },
  updateClientDetail: async ({ body }) => {
    const res = await proxy.put(
      UPLOAD_PERF_UI_AUTO_API + '/perf_client/update_client',
      body
    );
    return res.data.data;
  },

  deleteClient: async ({ payload }) => {
    const res = await proxy.delete(UPLOAD_PERF_UI_AUTO_API + '/perf_client/del_client', {
      params: payload,
    });
    return res.data.data;
  },
  getDeviceList: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + '/perf_device/device_list', {
      params: payload,
    });
    return res.data.data;
  },
// 删除客户端的设备
  deleteDevice: async ({ payload }) => {
    const res = await proxy.delete(UPLOAD_PERF_UI_AUTO_API + '/perf_device/delete_device', {
      params: payload,
    });
    return res.data;
  },


  // 性能UI自动化相关-用例相关
  getCaseList: async ({ payload, body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_case/case_list?page=${payload.page}&page_size=${payload.page_size}`,
      body);
    return res.data.data.cases;
  },
  updateCaseList: async () => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + '/perf_case/update_case',{});
    return res.data.data.cases;
  },
  getPerfCaseDetail: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + '/perf_case/case_detail?case_id=' + payload.case_id);
    return res.data.data;
  },
  // 性能UI自动化相关-app管理
  getAppVersionList: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/app_version_list', {
      params: payload,
    });
    return res.data.data.version_list;
  },
  getAppGroupList: async ({ payload, body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + `/app_group_list?page=${payload.page}&page_size=${payload.page_size}`, body);
    return res.data.data;
  },
  getAppGroupDetail: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/app_group_detail', {
      params: payload,
    });
    return res.data.data;
  },
  deleteAppGroup: async ({ payload }) => {
    const res = await proxy.delete(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/delete_app_group', {
      params: payload,
    });
    return res.data;
  },
  addApp: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/add_app', body);
    return res.data;
  },
  updateApp: async ({ body }) => {
    const res = await proxy.put(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/update_app', body);
    return res.data;
  },
  getAppList: async ({ body,payload }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + `/app_list?page=${payload.page}&page_size=${payload.page_size}`, body);
    return res.data.data;
  },
  // 性能UI自动化相关-app管理-删除app
  deleteApp: async ({ payload }) => {
    const res = await proxy.delete(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/delete_app', {
      params: payload,
    });
    return res.data;
  },
  // 性能UI自动化相关-app管理-添加app组
  addAppGroup: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/add_app_group', body);
    return res.data;
  },
  updateAppGroup: async ({ body }) => {
    const res = await proxy.put(UPLOAD_PERF_UI_AUTO_API + PERF_APP_API + '/update_app_group', body);
    return res.data;

  },
  
  // 性能UI自动化相关-任务管理
  getPerfTaskList: async ({ payload, body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/task_list?page=${payload.page}&page_size=${payload.page_size}`,
     body);
    return res.data.data;
  },
  createPerfTask: async ({ body }) => {
    const res = await proxy.put(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/create_task`, body);
    return res.data;
  },
  updatePerfTask: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/update_task`, body);
    return res.data;
  },
  copyPerfTask: async ({ payload }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/copy_task?task_id=${payload.task_id}&operator=${payload.operator}`);
    return res.data;
  },
  deletePerfTask: async ({ payload }) => {
    const res = await proxy.delete(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/delete_task?task_id=${payload.task_id}`);
    return res.data;
  },
  executePerfTask: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/start_task?task_id=${payload.task_id}`);
    return res.data;
  },
  retryPerfTask: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/retry_task?task_id=${payload.task_id}`);
    return res.data;
  },
  cancelPerfTask: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/cancel_task`, body);
    return res.data;
  },

  getPerfTaskDetail: async ({task_id}) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/task_detail?task_id=${task_id}`);
    return res.data.data;
  },
  getPerfTaskDetailAll: async ({task_id}) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/task_detail_all?task_id=${task_id}`);
    return res.data;
  },
  getPerfSubTaskCaseRunDetail: async ({payload}) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + PERF_TASK_API + `/get_sub_task_case_run_detail?page=${payload.page}&page_size=${payload.page_size}&sub_task_id=${payload.sub_task_id}`);
    return res.data.data;
  },
  // 性能UI自动化相关-任务报告
  getPerfDataList: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_data/data_list`,body);
    return res.data.data;
  },
  // 性能UI自动化相关-任务报告-性能详情
  getPerfDataDetail: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_data/perf_data_tos_url`, body);
    return res.data;
  },


  // 性能UI自动化相关-账号管理 账号列表
  getAccountList: async ({body}) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_account/account_list`, body);
    // page=${payload.page}&page_size=${payload.page_size}&business_id=${payload.business_id}&is_occupied=${payload.is_occupied}&`
      // 
    return res.data.data;
  },

  // 性能UI自动化相关-账号管理 创建账号
  createAccount: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_account/add_account`, body);
    return res.data;
  },

  // 性能UI自动化相关-账号管理 更新账号
  updateAccount: async ({ body }) => {
    const res = await proxy.put(UPLOAD_PERF_UI_AUTO_API + `/perf_account/update_account`, body);
    return res.data;
  },
  // 性能UI自动化相关-账号管理 删除账号
  deletePerfAccount: async ({ payload }) => {
    const res = await proxy.delete(UPLOAD_PERF_UI_AUTO_API + `/perf_account/delete_account?account_id=${payload.account_id}`);
    return res.data;
  },
  // 性能UI自动化相关-配置管理-采集字段管理-获取配置列表
  configList: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_config/config_list`, body);
    return res.data.data;
  }
,
  // 性能UI自动化相关-配置管理-采集字段管理-获取配置详情
  configDetail: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + `/perf_config/config_detail?config_id=${payload.config_id}`);
    return res.data.data;
  },
  // 性能UI自动化相关-配置管理-采集字段管理-创建配置
  createConfig: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_config/add_config`, body);
    return res.data.data;
  },
//  性能UI自动化相关-配置管理-采集字段管理-更新配置
  updateConfig: async ({ body }) => {
    const res = await proxy.put(UPLOAD_PERF_UI_AUTO_API + `/perf_config/update_config`, body);
    return res.data.data;
  },
  // 性能UI自动化相关-配置管理-采集字段管理-删除配置
  deleteConfig: async ({ payload }) => {
    const res = await proxy.delete(UPLOAD_PERF_UI_AUTO_API + `/perf_config/delete_config?config_id=${payload.config_id}`);
    return res.data.data;
  },
  // 性能UI自动化相关-配置管理-采集字段管理-获取metric列表
  metricList: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/perf_metric/metric_list`, body);
    return res.data.data;
  },
  // 性能UI自动化相关-实验接口-增加实验
  createExperiment: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/experiment/create`, body);
    return res.data;
  },
  // 性能UI自动化相关-实验接口-获取实验列表
  getExperimentList: async ({ body }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + `/experiment/list`, body);
    return res.data.data;
  },
  // 性能UI自动化相关-实验接口-获取实验详情
  getExperiment: async ({ payload }) => {
    const res = await proxy.get(UPLOAD_PERF_UI_AUTO_API + `/experiment/detail?id=${payload.experiment_id}`);
    return res.data.data;
  },
  // 性能UI自动化相关-实验接口-更新实验
  updateExperiment: async ({ body }) => {
    const res = await proxy.put(UPLOAD_PERF_UI_AUTO_API + `/experiment/update`, body);
    return res.data;
  },
  // 性能UI自动化相关-实验接口-根据实验vid获取实验信息
  viewFlightByVid: async ({ body }) => {
    const res = await proxy.post(UPLOAD_PERF_UI_AUTO_API + `/experiment/view_flight_by_vid`, body);
    return res.data.data;
  },

  // ci & daily 相关
  getBranchs: async ({ payload }) => {
    const res = await proxy.get(CI_DAILY_API + '/branch_list', {
      params: payload,
    });
    return res.data.data.branch_list;
  },
  getResultCodes: async ({ payload }) => {
    const res = await proxy.get(CI_DAILY_API + '/result_code_list', {
      params: payload,
    });
    return res.data.data.result_code_list;
  },
  getCaseData: async ({ payload, body }) => {
    const res = await proxy.post(
      CI_DAILY_API + `/case_list?page=${payload.page}&page_size=${payload.page_size}`,
      body
    );
    if (res.data.code != 200) {
      Message.error(
        `${res.config.url} response error: code=[${res.data.code}]  msg=[${res.data.msg}]`
      );
    }
    return res.data.data;
  },
  getCaseDetail: async ({ payload }) => {
    const res = await proxy.get(CI_DAILY_API + '/case_detail', {
      params: payload,
    });
    return res.data.data;
  },
  putUpdateCaseDetail: async ({ body }) => {
    const res = await proxy.put(CI_DAILY_API + '/update_case_detail', body);
    return res.data.data;
  },

  // 版本管理相关
  getVersionList: async ({ payload }) => {
    const res = await proxy.get(
      VERSION_LIST_API + `/version_list?page=${payload.page}&page_size=${payload.page_size}`
    );
    return res.data.data;
  },

  getVersionDetail: async ({ payload }) => {
    const res = await proxy.get(VERSION_DETAIL_API + '/version_detail', {
      params: payload,
    });
    return res.data.data;
  },

  getVersionChecklist: async ({ payload }) => {
    const res = await proxy.get(VERSION_DETAIL_API + '/version_checklist', {
      params: payload,
    });
    return res.data.data.items;
  },

  updateVersionChecklist: async ({ body }) => {
    const res = await proxy.put(VERSION_DETAIL_API + '/update_version_checklist', body);
    return res.data;
  },

  // 业务相关
  fetchBusinessList: async () => {
    const res = await proxy.get(BUSINESS_API + '/fetch_all');
    return res.data.data;
  },

  // 实验相关
  fetchLatestCheckRuleByBusinessId: async (payload) => {
    const res = await proxy.get(LIBRA_EXPERIMENT_API + `/find_latest_check_rule_by_business_id?business_id=${payload.business_id}`);
    return res.data.data;
  },

};
