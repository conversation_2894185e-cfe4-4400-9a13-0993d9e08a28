import React, { useState, useEffect } from 'react';
import { Typography, Card, Grid, Space } from '@arco-design/web-react';
import { HorizontalInterval } from 'src/components/chart/horizontal-interval';
import { AreaPolar } from 'src/components/chart/area-polar';
import { FactMultiPie } from 'src/components/chart/fact-multi-pie';
import { AnalysisCardList, AnalysisDataOverview, Powered } from 'src/components';
import styles from 'src/components/style/index.module.less';
import { Apis } from 'src/utils/apis';

const { Row, Col } = Grid;
const { Title } = Typography;

function DataAnalysis() {
  const [loading, setLoading] = useState(true);
  const [interval, setInterval] = useState([]);
  const [polarLoading, setPolarLoading] = useState(true);
  const [polar, setPolar] = useState({ list: [], fields: [] });
  const [multiPieLoading, setMultiPieLoading] = useState(false);
  const [multiPie, setMultiPie] = useState([]);

  const fetchInterval = async () => {
    setLoading(true);
    const { data } = await Apis.getChartInterval().finally(() => {
      setLoading(false);
    });
    setInterval(data);
  };

  const fetchPolar = async () => {
    setPolarLoading(true);
    const { data } = await Apis.getChartPlor().finally(() => {
      setPolarLoading(false);
    });

    setPolar(data);
  };

  const fetchMultiPie = async () => {
    setMultiPieLoading(true);
    const { data } = await Apis.getChartMultiPie().finally(() => {
      setMultiPieLoading(false);
    });
    setMultiPie(data);
  };

  useEffect(() => {
    fetchInterval();
    fetchPolar();
    fetchMultiPie();
  }, []);

  return (
    <>
      <div className={styles.wrapper}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Row>
            <Col span={16}>
              <Card style={{ margin: '20px 10px 0px 20px' }}>
                <Title heading={6} style={{ marginTop: 0 }}>
                  Overview
                </Title>
                <AnalysisDataOverview />
              </Card>
            </Col>
            <Col span={8}>
              <Card style={{ margin: '20px 20px 20px 10px' }}>
                <Title heading={6}>{`Today's Likes and Comments Statistics`}</Title>
                <HorizontalInterval data={interval} loading={loading} height={160} />
              </Card>
              <Card style={{ margin: '0px 20px 10px 10px' }}>
                <Title heading={6}>Content theme distribution</Title>
                <AreaPolar
                  data={polar.list}
                  fields={polar.fields}
                  height={197}
                  loading={polarLoading}
                />
              </Card>
            </Col>
          </Row>
          <Row style={{ margin: '0 20px 10px 20px' }}>
            <Col span={24}>
              <AnalysisCardList />
            </Col>
          </Row>
          <Row style={{ margin: '0px 20px 10px 20px' }}>
            <Col span={24}>
              <Card style={{ margin: '' }}>
                <Title heading={6}>Content publishing source</Title>
                <FactMultiPie loading={multiPieLoading} data={multiPie} height={240} />
              </Card>
            </Col>
          </Row>
        </Space>
      </div>
      <Powered />
    </>
  );
}

export default DataAnalysis;
