import React, { useEffect, useState } from 'react';
import { Tabs, Card, Typography, Grid } from '@arco-design/web-react';
import styles from 'src/components/style/card.module.less';
import { CardBlock, Powered } from 'src/components';
import { Apis } from 'src/utils/apis';
import type { BasicCard, QualityInspection } from 'src/components/card-block';

const { Title } = Typography;
const { Row, Col } = Grid;

const cardList = {
  quality: 'Content quality',
  service: 'Service opening',
  rules: 'Rule presets',
};

const defaultList = new Array(10).fill({});

export default function ListCard() {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState({
    quality: defaultList,
    service: defaultList,
    rules: defaultList,
  });
  const [activeKey, setActiveKey] = useState('all');

  const fetchData = async () => {
    const { data } = await Apis.getList().finally(() => setLoading(false));
    setData(data);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const getCardList = (list: Array<BasicCard & QualityInspection>, type: keyof typeof data) => {
    return (
      <Row gutter={24} className={styles.cardContent}>
        {list.map((item, index) => (
          <Col xs={24} sm={12} md={8} lg={6} xl={6} xxl={6} key={index}>
            <CardBlock card={item} type={type} loading={loading} />
          </Col>
        ))}
      </Row>
    );
  };

  return (
    <>
      <Card bordered={false}>
        <Title heading={6} style={{ marginTop: 0 }}>
          Card List
        </Title>
        <Tabs activeTab={activeKey} type="rounded" onChange={setActiveKey}>
          <Tabs.TabPane key="all" title="All" />
          <Tabs.TabPane key="quality" title="Content quality" />
          <Tabs.TabPane key="service" title="Service opening" />
          <Tabs.TabPane key="rules" title="Rule presets" />
        </Tabs>
        <div className={styles.container}>
          {activeKey === 'all' ? (
            Object.entries(data).map(([key, list]) => {
              return (
                <div key={key}>
                  <Title heading={6}>{cardList[key]}</Title>
                  {getCardList(list, key as keyof typeof data)}
                </div>
              );
            })
          ) : (
            <div className={styles.singleContent}>
              {getCardList(data[activeKey], activeKey as keyof typeof data)}
            </div>
          )}
        </div>
      </Card>
      {activeKey === 'all' ? <Powered /> : null}
    </>
  );
}
