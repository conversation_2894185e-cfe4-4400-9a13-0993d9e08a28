import React, { FC, useState, useEffect } from 'react';
import { Card, Form, Input, Select, Table, Grid, Space, Image, Tooltip, Tag, Modal, Button, Message, Popconfirm,Tabs, Badge, Divider } from '@arco-design/web-react';
import type { PaginationProps } from '@arco-design/web-react';
import {developer_list,constants} from 'src/utils/const';
import { useUser } from '@devsre/react-utils';
import { BackendApis2 } from 'src/utils/backendApis2';
import { IconTag, IconPlus } from '@arco-design/web-react/icon';
import { useLocation } from 'react-router-dom';
import { MetricConfigTab } from './MetricConfigTab';

const TabPane = Tabs.TabPane;

const { Row, Col } = Grid;


// 添加自定义样式 - 优化高度和单行显示
const customStyles = `
  .custom-select-option {
    transition: all 0.2s ease;
    white-space: nowrap !important;
    overflow: hidden !important;
    height: 40px !important;
    line-height: 40px !important;
    display: flex !important;
    align-items: center !important;
  }
  .custom-select-option:hover {
    background-color: #f2f3f5;
  }
  .arco-select-option-selected {
    background-color: #e8f4ff !important;
    font-weight: 500;
  }
  .arco-select-popup {
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  .arco-select-option {
    border-radius: 4px;
    margin: 2px 4px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    padding: 0 !important;
    height: 40px !important;
    line-height: 40px !important;
    display: flex !important;
    align-items: center !important;
  }
  .arco-select-option-content {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
  }
  .metric-field-select {
    height: 40px !important;
  }
  .metric-field-select .arco-select-view {
    height: 40px !important;
    line-height: 38px !important;
  }
  .metric-field-select .arco-select-view-value {
    line-height: 38px !important;
  }

  /* 按钮样式优化 */
  .arco-btn-outline:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(64, 128, 255, 0.2) !important;
  }

  .arco-btn-outline[style*="border-color: #00b42a"]:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0, 180, 42, 0.2) !important;
  }

  .arco-btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(64, 128, 255, 0.4) !important;
    background: linear-gradient(135deg, #165dff 0%, #0e42d2 100%) !important;
  }

  .arco-btn:active {
    transform: translateY(0px) !important;
  }

  /* 按钮禁用状态 */
  .arco-btn:disabled {
    transform: none !important;
    box-shadow: none !important;
  }
`;



export const CollectionFieldManage: FC = () => {
  const location = useLocation();
    const { name } = useUser();
  const [configData, setConfigData] = useState();
  const [loading, setLoading] = useState<boolean>(false);
  const [businessList, setBusinessList] = useState([]);
  const [metricList, setMetricList] = useState([]);
  const [searchFormValues, setSearchFormValues ] = useState({}); //查询配置的条件
  const [configListForm] = Form.useForm(); //查询配置条件的form

  // 获取已选择的指标ID列表 - 用于过滤重复选择
  const getSelectedMetricIds = (currentFieldMap: any, currentIndex?: string) => {
    const selectedIds: number[] = [];
    Object.keys(currentFieldMap).forEach(key => {
      // 排除当前正在编辑的字段，允许其保持当前选择
      if (key !== currentIndex && currentFieldMap[key].metric_id) {
        selectedIds.push(currentFieldMap[key].metric_id);
      }
    });
    return selectedIds;
  };

  // 获取可用的指标选项 - 过滤掉已选择的指标
  const getAvailableMetricOptions = (metricCategory: number, currentFieldMap: any, currentIndex?: string) => {
    const selectedIds = getSelectedMetricIds(currentFieldMap, currentIndex);
    return metricList
      .filter((item) => item.metric_category === metricCategory)
      .filter((item) => !selectedIds.includes(item.id));
  };

  // 美化的字段选项渲染函数 - 优化高度和单行显示版本
  const renderMetricOption = (item: any) => {
    const metricTypeText = constants.metricTypeMap.get(item.metric_type) || '未知类型';

    return (
      <div style={{
        padding: '8px 12px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        width: '100%',
        height: '40px',
        boxSizing: 'border-box'
      }}>
        {/* 图标 */}
        <IconTag style={{
          color: '#4080ff',
          fontSize: '14px',
          flexShrink: 0
        }} />

        {/* 主要字段信息 */}
        <span style={{
          color: '#4080ff',
          fontWeight: '600',
          fontSize: '14px',
          flexShrink: 0
        }}>
          {item.metric_key}
        </span>

        {/* 分隔符 */}
        <Divider type="vertical" style={{
          margin: '0 4px',
          backgroundColor: '#e5e6eb',
          flexShrink: 0,
          height: '16px'
        }} />

        {/* 字段名称 */}
        <span style={{
          fontSize: '14px',
          fontWeight: '500',
          color: '#1d2129',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          minWidth: 0,
          flex: '1'
        }}>
          {item.metric_name}
        </span>

        {/* 单位信息 */}
        {item.metric_unit && (
          <>
            <Divider type="vertical" style={{
              margin: '0 4px',
              backgroundColor: '#e5e6eb',
              flexShrink: 0,
              height: '16px'
            }} />
            <span style={{
              fontSize: '12px',
              color: '#86909c',
              flexShrink: 0
            }}>
              {item.metric_unit}
            </span>
          </>
        )}

        {/* 类型标签 */}
        <Tag
          size="small"
          color={item.metric_type === 1 ? 'blue' : item.metric_type === 2 ? 'green' : 'orange'}
          style={{
            fontSize: '11px',
            padding: '2px 6px',
            borderRadius: '10px',
            border: 'none',
            marginLeft: '8px',
            flexShrink: 0,
            height: '20px',
            lineHeight: '16px'
          }}
        >
          {metricTypeText}
        </Tag>
      </div>
    );
  };
  
  const [activeTabKey, setActiveTabKey] = useState('filed_manage');
  

  //  弹窗相关的东西
  const [dailogVisible, setDailogVisible] = useState(false); // 弹窗是否显示
   // 弹窗类型分类
   const DIALOG_TYPE_ADD = 1
   const DIALOG_TYPE_EDIT = 2
   const DIALOG_TYPE_DETAIL = 3
  const [dialogType, setDialogType] = useState(DIALOG_TYPE_ADD); // 弹窗类型，1：添加，2：编辑, 3：详情
  const [dialogForm] = Form.useForm(); //弹窗的form
 
  // 采集字段 分多个采集字段
   const [configFieldAutoId, setConfigFieldAutoId] = useState(0);
   const [gameperfConfigFieldAutoId, setGameperfConfigFieldAutoId] = useState(0);
   const [traceConfigFieldAutoId, setTraceConfigFieldAutoId] = useState(0);
   const [byteioConfigFieldAutoId, setByteioConfigFieldAutoId] = useState(0);
  //  初始的采集字段
   const startConfigField = {
      "metric_id": null,
      "is_important": false,
      "comparison_type": null,
      "value_type": null,
      "threshold_value": null
  }
  const startGameperfConfigField = {
    "metric_id": null,
    "is_important": false,
    "comparison_type": null,
    "value_type": null,
    "threshold_value": null
  }
  const startTraceConfigField = {
    "metric_id": null,
    "is_important": false,
    "comparison_type": null,
    "value_type": null,
    "threshold_value": null
  }
  const startByteioConfigField = {
    "metric_id": null,
    "is_important": false,
    "comparison_type": null,
    "value_type": null,
    "threshold_value": null
  }
    const [configFieldMap, setConfigFieldMap] = useState({
      [configFieldAutoId]: startConfigField,
    });
    const [gameperfConfigFieldMap, setGameperfConfigFieldMap] = useState({
      [gameperfConfigFieldAutoId]: startGameperfConfigField,
    });
    const [traceConfigFieldMap, setTraceConfigFieldMap] = useState({
      [traceConfigFieldAutoId]: startTraceConfigField,
    });
    const [byteioConfigFieldMap, setByteioConfigFieldMap] = useState({
      [byteioConfigFieldAutoId]: startByteioConfigField,
    });

  //  分页器
  const [pagination, setPagination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    // total: 0,
    pageSize: 10,
    current: 1,
    sizeOptions: [10, 20, 50, 100],
  });

  const columns = [
    {
      title: 'ID', dataIndex: 'id', width: '5%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    },
    {
      title: '业务', dataIndex: 'business_name', width: '6%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    },
    {
      title: '配置名称', dataIndex: 'config_name', width: '9%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    },
    {
      title: '配置采集参数', dataIndex: 'metrics', width: '35%', ellipsis: true,

      render: (_col: any, record: any, _index: number) => {
        // 定义一个函数来获取指标名称和类别
        const getMetricInfo = (metricId: number) => {
          const metric = metricList.find(item => item.id === metricId);
          return {
            name: metric?.metric_name || '未知指标',
            category: metric?.metric_category || 0
          };
        };

        // 按指标类别分组
        const dsMetrics = record.metrics.filter((metric: any) => {
          const metricInfo = getMetricInfo(metric.metric_id);
          return metricInfo.category === 1; // DS指标
        });

        const gameperfMetrics = record.metrics.filter((metric: any) => {
          const metricInfo = getMetricInfo(metric.metric_id);
          return metricInfo.category === 2; // GamePerf指标
        });

        const traceMetrics = record.metrics.filter((metric: any) => {
          const metricInfo = getMetricInfo(metric.metric_id);
          return metricInfo.category === 3; // VQoS Trace指标
        });

        const byteioMetrics = record.metrics.filter((metric: any) => {
          const metricInfo = getMetricInfo(metric.metric_id);
          return metricInfo.category === 4; // ByteIO指标
        });

        // 渲染指标标签的函数
        const renderMetricTags = (metrics: any[], categoryName: string, categoryColor: string) => {
          if (metrics.length === 0) return null;

          return (
            <div style={{ marginBottom: '8px' }}>
              {/* 类别标题 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '6px',
                fontSize: '12px',
                fontWeight: '500',
                color: '#86909c'
              }}>
                <span style={{
                  padding: '2px 6px',
                  backgroundColor: categoryColor,
                  color: '#fff',
                  borderRadius: '10px',
                  fontSize: '11px',
                  marginRight: '6px'
                }}>
                  {categoryName}
                </span>
                <span>{metrics.length}个指标</span>
              </div>

              {/* 指标标签 */}
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px' }}>
                {metrics.map((metric: any, idx: number) => (
                  <Tag
                    key={idx}
                    style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      marginRight: 0,
                      backgroundColor: metric.is_important ? '#fff3e8' : '#e8f4ff',
                      color: metric.is_important ? '#ff9a45' : '#4080ff',
                      border: `1px solid ${metric.is_important ? '#ffd4b3' : '#b3d9ff'}`
                    }}
                  >
                    <span style={{
                      maxWidth: '200px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      fontWeight: metric.is_important ? '500' : '400'
                    }}>
                      {getMetricInfo(metric.metric_id).name}
                    </span>
                    {metric.is_important && (
                      <Badge
                        count="重要"
                        dot={false}
                        style={{
                          backgroundColor: '#ff7875',
                          marginLeft: '4px',
                          fontSize: '12px',
                          padding: '0 4px',
                          borderRadius: '10px',
                          color: '#fff'
                        }}
                      />
                    )}
                  </Tag>
                ))}
              </div>
            </div>
          );
        };

        return (
          <div style={{ padding: '4px 0' }}>
            {/* DS指标 */}
            {renderMetricTags(dsMetrics, 'DS', '#165DFF')}

            {/* 分割线 - DS和其他指标之间 */}
            {dsMetrics.length > 0 && (gameperfMetrics.length > 0 || traceMetrics.length > 0 || byteioMetrics.length > 0) && (
              <Divider
                style={{
                  margin: '8px 0',
                  borderColor: '#e5e6eb'
                }}
              />
            )}

            {/* GamePerf指标 */}
            {renderMetricTags(gameperfMetrics, 'GamePerf', '#00B42A')}

            {/* 分割线 - GamePerf和其他指标之间 */}
            {gameperfMetrics.length > 0 && (traceMetrics.length > 0 || byteioMetrics.length > 0) && (
              <Divider
                style={{
                  margin: '8px 0',
                  borderColor: '#e5e6eb'
                }}
              />
            )}

            {/* VQoS Trace指标 */}
            {renderMetricTags(traceMetrics, 'Trace', '#FF7D00')}

            {/* 分割线 - Trace和ByteIO指标之间 */}
            {traceMetrics.length > 0 && byteioMetrics.length > 0 && (
              <Divider
                style={{
                  margin: '8px 0',
                  borderColor: '#e5e6eb'
                }}
              />
            )}

            {/* ByteIO指标 */}
            {renderMetricTags(byteioMetrics, 'ByteIO', '#722ED1')}
          </div>
        );
      }
    },
    {
      title: '创建人', dataIndex: 'creator', width: '8%', ellipsis: true,
            render: (_col, record, _index) => (
              <div  style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}> 
              <Space>
                <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.creator}?format=40x40.png`}></Image>
                <Tooltip mini content={record.creator}>
                  {record.creator.length > 18 ? record.creator.substring(0, 18) + '...' : record.creator}
                </Tooltip>
              </Space>
              </div>
            )
    },
  
    {
      title: '创建时间', dataIndex: 'create_time', width: '9%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    }, 
     {
      title: '操作', dataIndex: 'operation', width: developer_list.includes(name)?'12%':'10%', ellipsis: true,
      render: (_col, record, _index) => (

        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>
           <Popconfirm
                        focusLock
                        title='复制确认'
                        content={`再次是否复制配置 "${record.config_name}" ？`}
                        onOk={() => {
                          onCopyConfig(record)
                        }}
                        onCancel={() => {
                          Message.info({
                            content: '取消复制',
                          });
                        }}
                      >
                    <Button type="text"
            // icon={<IconEdit />}
            >
            复制
          </Button>   
                      </Popconfirm>
          
         {(record.creator == name||developer_list.includes(name))?
          <Button
            type="text"
            // icon={<IconEdit />}
            onClick={() => onClickEditConfig(record)}>
            编辑
          </Button>:null}
          {(record.creator == name||developer_list.includes(name))?
           <Popconfirm
                        focusLock
                        title='删除确认'
                        content={`删除任务后无法恢复，请再次确认删除配置 "${record.config_name}" ？`}
                        onOk={() => {
                          onClickDeleteConfig(record)
                        }}
                        onCancel={() => {
                          Message.info({
                            content: '取消删除',
                          });
                        }}
                      >
           <Button
            type="text"
            // icon={<IconList />}
            // onClick={() => onClickDeleteAccount(record)}
            >删除
           </Button>
          </Popconfirm>
          :null}
          
          
        </div>
      ),
    },
  ];
 // 获取业务列表
  const fetchBusinessList =  async() => {
    const response = await BackendApis2.fetchBusinessList().finally(() => { });
    setBusinessList(response.items);
  };
  // 获取metric列表
  const fetchMetricList = async () => {
    let body = {
      "page": 1,
      "page_size": 500,
      // "metric_category":1
    }
    const response = await BackendApis2.metricList({ body }).finally(() => { });
    setMetricList(response.items);
    // console.log("response.metrics")
    // console.log(response.metrics)
  };

  // 获取配置列表
   const fetchConfigList = async (pagination,payload) => {
     setLoading(true)
     const body= { page: pagination.current, page_size: pagination.pageSize, ...payload};
     const response = await BackendApis2.configList({body}).finally(() => {
       setLoading(false)
     });
       // 获取业务列表
     const response_bu = await BackendApis2.fetchBusinessList().finally(() => { });
     const businessList1 = response_bu.items;
  
     // 处理，把每个congfig的business id 替换成business_name
     const final_items = response.items.map(item => {
       const business_name = businessList1.find(business => business.id === item.business_id)?.business_name;
       return {
         ...item,
         business_name,
       };
     });
     setConfigData(final_items);
     pagination.current = response.page;
     pagination.pageSize = response.page_size;
     pagination.total = response.total;
     setPagination(pagination);
   };

   // 根据条件搜索配置
  const handleConfigListSearch = (value, fieldName) => {
    const updatedFormValues = {
      ...searchFormValues,
      [fieldName]: value,
    };
    setSearchFormValues(updatedFormValues)
    localStorage.setItem('configListSearchFormValues', JSON.stringify(updatedFormValues))
    fetchConfigList(pagination, updatedFormValues);
  };

  // 新增配置
  const onClickCreateConfig = () => {
    setActiveTabKey("filed_manage");

    // 在这里处理新增操作
    // console.log('新增操作');
    setDailogVisible(true)
    setDialogType(DIALOG_TYPE_ADD)
  }

// 编辑配置
  const onClickEditConfig = (record) => {
    // 在这里处理编辑操作
    // 先把configFieldMap 清空
    setConfigFieldAutoId(0);
    setGameperfConfigFieldAutoId(0);
    setTraceConfigFieldAutoId(0);
    setByteioConfigFieldAutoId(0);
    // 然后把record记录中的metrics 赋值给configFieldMap
    const newConfigFieldMap = {};
    const newGameperfConfigFieldMap = {};
    const newTraceConfigFieldMap = {};
    const newByteioConfigFieldMap = {};
    let max_index = 0;
    let max_index_gameperf = 0;
    let max_index_trace = 0;
    let max_index_byteio = 0;
    record.metrics.map((item) => {
      const metricCategory = metricList.find((metric) => metric.id == item.metric_id)?.metric_category;
      if (metricCategory == 1) {
        newConfigFieldMap[max_index] = item;
        max_index += 1;
      } else if (metricCategory == 2) {
        newGameperfConfigFieldMap[max_index_gameperf] = item;
        max_index_gameperf += 1;
      } else if (metricCategory == 3) {
        newTraceConfigFieldMap[max_index_trace] = item;
        max_index_trace += 1;
      } else if (metricCategory == 4) {
        newByteioConfigFieldMap[max_index_byteio] = item;
        max_index_byteio += 1;
      }
    });
    // 需要设置ConfigFieldAutoId
    setConfigFieldAutoId(max_index + 1);
    setConfigFieldMap(newConfigFieldMap);
    setGameperfConfigFieldAutoId(max_index_gameperf + 1);
    setGameperfConfigFieldMap(newGameperfConfigFieldMap);
    setTraceConfigFieldAutoId(max_index_trace + 1);
    setTraceConfigFieldMap(newTraceConfigFieldMap);
    setByteioConfigFieldAutoId(max_index_byteio + 1);
    setByteioConfigFieldMap(newByteioConfigFieldMap);

    // 根据指标数量决定默认显示的Tab
    const counts = [max_index, max_index_gameperf, max_index_trace, max_index_byteio];
    const maxCount = Math.max(...counts);
    const maxIndex = counts.indexOf(maxCount);
    const tabKeys = ["filed_manage", "gameperf_filed_manage", "trace_filed_manage", "byteio_filed_manage"];
    setActiveTabKey(tabKeys[maxIndex]);

    // 最后把其他的值赋值，弹窗设置为编辑状态
    dialogForm.setFieldsValue(record)
    setDailogVisible(true);
    setDialogType(DIALOG_TYPE_EDIT);
  }
// 删除配置
  const onClickDeleteConfig = async (record) => {
    // 在这里处理删除
     const payload = { "config_id": record.id }
        const response = await BackendApis2.deleteConfig({ payload }).finally(() => {
        });
        if (response === true) {
          Message.success("删除成功")
          fetchConfigList(pagination, searchFormValues)
        } else {
          Message.error("删除失败")
        }
  }

  // 复制配置
  const onCopyConfig = async(record) => {
    // 在这里处理复制操作
    console.log('复制操作:', record);
    const body = {
      ...record,
      "creator":name,
      "config_name":record.config_name + "_副本"
    }
    await BackendApis2.createConfig({body}).finally(() => {
    })

     // 刷新配置列表
     fetchConfigList(pagination, searchFormValues);

  }
  // 点击增加字段
  const onClickAddField = () => {
    const newConfigFieldMap = {
      ...configFieldMap,  
      [configFieldAutoId + 1]: startConfigField,
    };
    // console.log(newConfigFieldMap);
    setConfigFieldMap(newConfigFieldMap);
    setConfigFieldAutoId(configFieldAutoId + 1);
  }
  // 点击增加gameperf字段
  const onClickAddGameperfField = () => {
    const newConfigFieldMap = {
      ...gameperfConfigFieldMap,
      [gameperfConfigFieldAutoId + 1]: startGameperfConfigField,
    };
    // console.log(newConfigFieldMap);
    setGameperfConfigFieldMap(newConfigFieldMap);
    setGameperfConfigFieldAutoId(gameperfConfigFieldAutoId + 1);
  }

  // 点击增加trace字段
  const onClickAddTraceField = () => {
    const newConfigFieldMap = {
      ...traceConfigFieldMap,
      [traceConfigFieldAutoId + 1]: startTraceConfigField,
    };
    setTraceConfigFieldMap(newConfigFieldMap);
    setTraceConfigFieldAutoId(traceConfigFieldAutoId + 1);
  }

  // 点击增加byteio字段
  const onClickAddByteioField = () => {
    const newConfigFieldMap = {
      ...byteioConfigFieldMap,
      [byteioConfigFieldAutoId + 1]: startByteioConfigField,
    };
    setByteioConfigFieldMap(newConfigFieldMap);
    setByteioConfigFieldAutoId(byteioConfigFieldAutoId + 1);
  }

  // 一键添加所有DS采集字段
  const onClickAddAllDSFields = () => {
    const availableMetrics = metricList.filter(item => item.metric_category === 1);
    const selectedIds = getSelectedMetricIds(configFieldMap);
    const unselectedMetrics = availableMetrics.filter(metric => !selectedIds.includes(metric.id));

    if (unselectedMetrics.length === 0) {
      Message.info('所有DS指标已被添加');
      return;
    }

    const newConfigFieldMap = { ...configFieldMap };
    let currentAutoId = configFieldAutoId;

    unselectedMetrics.forEach(metric => {
      currentAutoId += 1;
      newConfigFieldMap[currentAutoId] = {
        ...startConfigField,
        metric_id: metric.id
      };
    });

    setConfigFieldMap(newConfigFieldMap);
    setConfigFieldAutoId(currentAutoId);
    Message.success(`成功添加${unselectedMetrics.length}个DS指标`);
  };

  // 一键添加所有GamePerf采集字段
  const onClickAddAllGameperfFields = () => {
    const availableMetrics = metricList.filter(item => item.metric_category === 2);
    const selectedIds = getSelectedMetricIds(gameperfConfigFieldMap);
    const unselectedMetrics = availableMetrics.filter(metric => !selectedIds.includes(metric.id));

    if (unselectedMetrics.length === 0) {
      Message.info('所有GamePerf指标已被添加');
      return;
    }

    const newConfigFieldMap = { ...gameperfConfigFieldMap };
    let currentAutoId = gameperfConfigFieldAutoId;

    unselectedMetrics.forEach(metric => {
      currentAutoId += 1;
      newConfigFieldMap[currentAutoId] = {
        ...startGameperfConfigField,
        metric_id: metric.id
      };
    });

    setGameperfConfigFieldMap(newConfigFieldMap);
    setGameperfConfigFieldAutoId(currentAutoId);
    Message.success(`成功添加${unselectedMetrics.length}个GamePerf指标`);
  };

  // 一键添加所有VQoS Trace采集字段
  const onClickAddAllTraceFields = () => {
    const availableMetrics = metricList.filter(item => item.metric_category === 3);
    const selectedIds = getSelectedMetricIds(traceConfigFieldMap);
    const unselectedMetrics = availableMetrics.filter(metric => !selectedIds.includes(metric.id));

    if (unselectedMetrics.length === 0) {
      Message.info('所有VQoS Trace指标已被添加');
      return;
    }

    const newConfigFieldMap = { ...traceConfigFieldMap };
    let currentAutoId = traceConfigFieldAutoId;

    unselectedMetrics.forEach(metric => {
      currentAutoId += 1;
      newConfigFieldMap[currentAutoId] = {
        ...startTraceConfigField,
        metric_id: metric.id
      };
    });

    setTraceConfigFieldMap(newConfigFieldMap);
    setTraceConfigFieldAutoId(currentAutoId);
    Message.success(`成功添加${unselectedMetrics.length}个VQoS Trace指标`);
  };

  // 一键添加所有ByteIO采集字段
  const onClickAddAllByteioFields = () => {
    const availableMetrics = metricList.filter(item => item.metric_category === 4);
    const selectedIds = getSelectedMetricIds(byteioConfigFieldMap);
    const unselectedMetrics = availableMetrics.filter(metric => !selectedIds.includes(metric.id));

    if (unselectedMetrics.length === 0) {
      Message.info('所有ByteIO指标已被添加');
      return;
    }

    const newConfigFieldMap = { ...byteioConfigFieldMap };
    let currentAutoId = byteioConfigFieldAutoId;

    unselectedMetrics.forEach(metric => {
      currentAutoId += 1;
      newConfigFieldMap[currentAutoId] = {
        ...startByteioConfigField,
        metric_id: metric.id
      };
    });

    setByteioConfigFieldMap(newConfigFieldMap);
    setByteioConfigFieldAutoId(currentAutoId);
    Message.success(`成功添加${unselectedMetrics.length}个ByteIO指标`);
  };

  // DS字段复制和删除函数
  const onCopyDSField = (index: string) => {
    const copyField = JSON.parse(JSON.stringify(configFieldMap[index]));
    const newConfigFieldMap = {
      ...configFieldMap,
      [configFieldAutoId + 1]: copyField,
    };
    setConfigFieldMap(newConfigFieldMap);
    setConfigFieldAutoId(configFieldAutoId + 1);
    Message.success('字段复制成功');
  };

  const onDeleteDSField = (index: string) => {
    const newConfigFieldMap = { ...configFieldMap };
    delete newConfigFieldMap[index];
    setConfigFieldMap(newConfigFieldMap);
    Message.success('字段删除成功');
  };

  // GamePerf字段复制和删除函数
  const onCopyGameperfField = (index: string) => {
    const copyField = JSON.parse(JSON.stringify(gameperfConfigFieldMap[index]));
    const newConfigFieldMap = {
      ...gameperfConfigFieldMap,
      [gameperfConfigFieldAutoId + 1]: copyField,
    };
    setGameperfConfigFieldMap(newConfigFieldMap);
    setGameperfConfigFieldAutoId(gameperfConfigFieldAutoId + 1);
    Message.success('字段复制成功');
  };

  const onDeleteGameperfField = (index: string) => {
    const newConfigFieldMap = { ...gameperfConfigFieldMap };
    delete newConfigFieldMap[index];
    setGameperfConfigFieldMap(newConfigFieldMap);
    Message.success('字段删除成功');
  };

  // Trace字段复制和删除函数
  const onCopyTraceField = (index: string) => {
    const copyField = JSON.parse(JSON.stringify(traceConfigFieldMap[index]));
    const newConfigFieldMap = {
      ...traceConfigFieldMap,
      [traceConfigFieldAutoId + 1]: copyField,
    };
    setTraceConfigFieldMap(newConfigFieldMap);
    setTraceConfigFieldAutoId(traceConfigFieldAutoId + 1);
    Message.success('字段复制成功');
  };

  const onDeleteTraceField = (index: string) => {
    const newConfigFieldMap = { ...traceConfigFieldMap };
    delete newConfigFieldMap[index];
    setTraceConfigFieldMap(newConfigFieldMap);
    Message.success('字段删除成功');
  };

  // ByteIO字段复制和删除函数
  const onCopyByteioField = (index: string) => {
    const copyField = JSON.parse(JSON.stringify(byteioConfigFieldMap[index]));
    const newConfigFieldMap = {
      ...byteioConfigFieldMap,
      [byteioConfigFieldAutoId + 1]: copyField,
    };
    setByteioConfigFieldMap(newConfigFieldMap);
    setByteioConfigFieldAutoId(byteioConfigFieldAutoId + 1);
    Message.success('字段复制成功');
  };

  const onDeleteByteioField = (index: string) => {
    const newConfigFieldMap = { ...byteioConfigFieldMap };
    delete newConfigFieldMap[index];
    setByteioConfigFieldMap(newConfigFieldMap);
    Message.success('字段删除成功');
  };

   // 单个字段的值发生变化
   const onConfigFieldItemChange = (json, index) => {
 
 
    const newConfigFieldMap = { ...configFieldMap };
    Object.keys(json).forEach(field => {
      newConfigFieldMap[index][field] = json[field];
    });
       console.log(newConfigFieldMap)
    setConfigFieldMap(newConfigFieldMap);
    console.log(newConfigFieldMap);

  };
  // 单个gameperf字段的值发生变化
  const onGameperfConfigFieldItemChange = (json, index) => {
    console.log('newConfigFieldMap for gameperf');
    const newConfigFieldMap = { ...gameperfConfigFieldMap };
    Object.keys(json).forEach(field => {
      newConfigFieldMap[index][field] = json[field];
    });
    setGameperfConfigFieldMap(newConfigFieldMap);
    console.log(newConfigFieldMap);
  };

  // 单个trace字段的值发生变化
  const onTraceConfigFieldItemChange = (json, index) => {
    console.log('newConfigFieldMap for trace');
    const newConfigFieldMap = { ...traceConfigFieldMap };
    Object.keys(json).forEach(field => {
      newConfigFieldMap[index][field] = json[field];
    });
    setTraceConfigFieldMap(newConfigFieldMap);
    console.log(newConfigFieldMap);
  };

  // 单个byteio字段的值发生变化
  const onByteioConfigFieldItemChange = (json, index) => {
    console.log('newConfigFieldMap for byteio');
    const newConfigFieldMap = { ...byteioConfigFieldMap };
    Object.keys(json).forEach(field => {
      newConfigFieldMap[index][field] = json[field];
    });
    setByteioConfigFieldMap(newConfigFieldMap);
    console.log(newConfigFieldMap);
  };

  // 关闭弹窗
  const handleCloseAddOrEditConfigDialog = () => {
    //  编辑和详情弹窗关闭时，清空表单
    if (dialogType == DIALOG_TYPE_EDIT || dialogType == DIALOG_TYPE_DETAIL) {
      dialogForm.resetFields();
      // 清空采集字段
      setConfigFieldAutoId(0);
      setGameperfConfigFieldAutoId(0);
      setTraceConfigFieldAutoId(0);
      setByteioConfigFieldAutoId(0);
      setConfigFieldMap({
        [configFieldAutoId]: startConfigField,
      });
      setGameperfConfigFieldMap({
        [gameperfConfigFieldAutoId]: startGameperfConfigField,
      });
      setTraceConfigFieldMap({
        [traceConfigFieldAutoId]: startTraceConfigField,
      });
      setByteioConfigFieldMap({
        [byteioConfigFieldAutoId]: startByteioConfigField,
      });
    }
    setDailogVisible(false)
  };

  // 提交弹窗
  const handleSubmitConfigDialog = async (value) => {
     let config = {};
      // if (activeTabKey == 'filed_manage') {
      //   config =  Object.values(configFieldMap)
        
      // } else if (activeTabKey == 'gameperf_filed_manage') {
      //   config = Object.values(gameperfConfigFieldMap)
        
      // }
    // 合并四个数组
    config = [
      ...Object.values(configFieldMap),
      ...Object.values(gameperfConfigFieldMap),
      ...Object.values(traceConfigFieldMap),
      ...Object.values(byteioConfigFieldMap)
    ]
    if (dialogType == DIALOG_TYPE_ADD) {
      // Message.info('添加');
     
      const body = {
        ...value,
        "creator":name,
        "metrics": config
      }
      await BackendApis2.createConfig({body}).finally(() => {

      })
    } else if(dialogType == DIALOG_TYPE_EDIT){
      // Message.info('更新');
      // console.log("configFieldMap")
      // console.log(Object.values(configFieldMap))
      const body = {
        ...value,
        "metrics": config
      }
      await BackendApis2.updateConfig({body}).finally(() => {
      })
    }
    // 删除掉表单，并关闭弹窗
    dialogForm.resetFields();
      // 清空采集字段
      setConfigFieldAutoId(0);
      setGameperfConfigFieldAutoId(0);
      setTraceConfigFieldAutoId(0);
      setByteioConfigFieldAutoId(0);
      setConfigFieldMap({
        [configFieldAutoId]: startConfigField,
      });
      setGameperfConfigFieldMap({
        [gameperfConfigFieldAutoId]: startGameperfConfigField,
      });
      setTraceConfigFieldMap({
        [traceConfigFieldAutoId]: startTraceConfigField,
      });
      setByteioConfigFieldMap({
        [byteioConfigFieldAutoId]: startByteioConfigField,
      });
    setDailogVisible(false);
    // 刷新配置列表
    fetchConfigList(pagination, searchFormValues);
  }
   function onTabChange(tabKey) {
    setActiveTabKey(tabKey);
  }

// 初始化
  useEffect(() => {
    fetchBusinessList();
    fetchMetricList();
    // console.log("metricList")
    // console.log(metricList)
 // 先判断localstate中是否有筛选参数，有的话，设置筛选参数
 console.log('configListSearchFormValues');
 const storedFilter = localStorage.getItem('configListSearchFormValues')
 console.log(storedFilter)
//  如果是从任务详情跳转过来的
if (location.state) {
  console.log("config_name")
  console.log(location.state.config_name)
    fetchConfigList(pagination,{
      "config_name":location.state.config_name
    })
   }
else{
 if (storedFilter) {
   configListForm.setFieldsValue(JSON.parse(storedFilter))
   setSearchFormValues(JSON.parse(storedFilter))
   fetchConfigList(pagination, JSON.parse(storedFilter))
 }
 else{
    fetchConfigList(pagination, searchFormValues)
   }
   }
  }, [pagination.current, pagination.pageSize]);

  const onChangeTable = (pagination: PaginationProps) => {
    setPagination(pagination);
  };

  return (
    <Card bordered={false}>
      <style>{customStyles}</style>
      <Form
        style={{ marginTop: '10px' }}
        wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
        form={configListForm}
        // onChange={onSearchFormDataChanges}
      >
        <Row gutter={20}>
          <Col span={3}>
            <Form.Item field="business_id">
              <Select
                placeholder={'业务'}
                // options={businessList}
                allowClear
                maxTagCount={1}
                size="large"
                // showSearch
                onChange={(value) => handleConfigListSearch(value, 'business_id')}
              >
                {businessList.map((item) => (
                  <Select.Option key={item.id} value={item.id}> {item.business_name} </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
         <Col span={3}>
                     <Form.Item field="config_name">
                     <Input 
                     placeholder={'配置名称'} 
                     // style={{ width: 225 }}
                     onChange={(value) => handleConfigListSearch(value, 'config_name')}
                     ></Input>
                     </Form.Item>
                   </Col>
            <Col span={3}>
                <Form.Item field="creator">
                  <Input 
                  placeholder={'创建人'} 
                  // style={{ width: 225 }}
                  onChange={(value) => handleConfigListSearch(value, 'creator')}
                  ></Input>
                </Form.Item>
            </Col>
          <Col span={2} offset={12}>
              <Button type='primary' style={{ float: 'right' }} onClick={() => onClickCreateConfig()}>
                创建配置
              </Button>
          </Col>
       </Row>
      </Form>
      <Modal
              visible={dailogVisible}
              title={
                <div style={{ textAlign: 'left' }}>
                  {dialogType == DIALOG_TYPE_ADD ? <span>添加配置</span> : <span>编辑配置</span>}
                </div>}
              style={{ width: '51%' }}
              onOk={() => handleCloseAddOrEditConfigDialog()}
              onCancel={() => handleCloseAddOrEditConfigDialog()}
              autoFocus={false}
              focusLock={true}
              footer={null}
              // width={720} 
            >
              <Form
                form={dialogForm}
                labelCol={{ span: 6 }}
                labelAlign={'right'}
                wrapperCol={{ span: 15 }}
                validateMessages={{
                  required: (_, { label }) => `必须填写 ${label}`
                }}
                // onChange={(value, values) => handleAddOrEditAccountParamsChange(value, values)} // 表单值变化响应函数
                onSubmit={(value) => handleSubmitConfigDialog(value)} // 提交表单响应函数
              >
                <Form.Item field="id" hidden>
                  <Input placeholder={'id'}></Input>
                </Form.Item>
                <Form.Item field="owner" hidden>
                  <Input placeholder={'owner'}></Input>
                </Form.Item>
                <Form.Item field="email" hidden>
                  <Input placeholder={'email'}></Input>
                </Form.Item>
                <Form.Item field="app" hidden>
                  <Input placeholder={'app'}></Input>
                </Form.Item>
                <Form.Item field="is_occuiped" hidden>
                  <Input placeholder={'is_occuiped'}></Input>
                </Form.Item>
                <Form.Item field="pwd" hidden>
                  <Input placeholder={'pwd'}></Input>
                </Form.Item>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="业务"
                      field="business_id"
                      disabled={dialogType == DIALOG_TYPE_DETAIL}
                      required
                      rules={[
                        {
                          type: 'number',
                          required: true,
                        },
                      ]}>
                      <Select
                        placeholder={'业务'}
                        triggerProps={{
                          autoAlignPopupWidth: false,
                          autoAlignPopupMinWidth: true,
                          position: 'bl',
                        }}
                      >
                        {businessList.map((item) => (
                          <Select.Option key={item.id} value={item.id}> {item.business_name} </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="配置名称"
                      field="config_name"
                      disabled = {dialogType == DIALOG_TYPE_DETAIL}
                      required
                      rules={[
                        {
                          type: 'string',
                          required: true,
                        },
                      ]}>
                      <Input placeholder={'请输入配置名'}></Input>
                    </Form.Item>
                  </Col>
                </Row>
               
         <Tabs
          activeTab={activeTabKey}
          onChange={(key) => onTabChange(key)}
          tabPosition="top"
          style={{ textAlign: 'center' }}>
          <TabPane key='filed_manage' title='DS基础性能指标'>
            <MetricConfigTab
              configFieldMap={configFieldMap}
              metricList={metricList}
              metricCategory={1}
              categoryName="DS"
              dialogType={dialogType}
              DIALOG_TYPE_DETAIL={DIALOG_TYPE_DETAIL}
              getAvailableMetricOptions={getAvailableMetricOptions}
              onConfigFieldItemChange={onConfigFieldItemChange}
              renderMetricOption={renderMetricOption}
              onCopyField={onCopyDSField}
              onDeleteField={onDeleteDSField}
            />


        </TabPane>

        <TabPane key='gameperf_filed_manage' title='Gameperf基础性能指标'>
            <MetricConfigTab
              configFieldMap={gameperfConfigFieldMap}
              metricList={metricList}
              metricCategory={2}
              categoryName="Gameperf"
              dialogType={dialogType}
              DIALOG_TYPE_DETAIL={DIALOG_TYPE_DETAIL}
              getAvailableMetricOptions={getAvailableMetricOptions}
              onConfigFieldItemChange={onGameperfConfigFieldItemChange}
              renderMetricOption={renderMetricOption}
              onCopyField={onCopyGameperfField}
              onDeleteField={onDeleteGameperfField}
            />
          </TabPane>

          <TabPane key='trace_filed_manage' title='VQoS Trace指标'>
            {/* VQoS Trace指标提示文案 */}
            <div style={{
              backgroundColor: '#fff7e6',
              border: '1px solid #ffd591',
              borderRadius: '8px',
              padding: '12px 16px',
              marginBottom: '16px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <IconTag style={{ color: '#fa8c16', fontSize: '16px' }} />
              <span style={{
                color: '#d46b08',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                新增指标请联系 hejiabei.oxp
              </span>
            </div>

            <MetricConfigTab
              configFieldMap={traceConfigFieldMap}
              metricList={metricList}
              metricCategory={3}
              categoryName="Trace"
              dialogType={dialogType}
              DIALOG_TYPE_DETAIL={DIALOG_TYPE_DETAIL}
              getAvailableMetricOptions={getAvailableMetricOptions}
              onConfigFieldItemChange={onTraceConfigFieldItemChange}
              renderMetricOption={renderMetricOption}
              onCopyField={onCopyTraceField}
              onDeleteField={onDeleteTraceField}
            />
          </TabPane>

          <TabPane key='byteio_filed_manage' title='ByteIO指标'>
            {/* ByteIO指标提示文案 */}
            <div style={{
              backgroundColor: '#fff7e6',
              border: '1px solid #ffd591',
              borderRadius: '8px',
              padding: '12px 16px',
              marginBottom: '16px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <IconTag style={{ color: '#fa8c16', fontSize: '16px' }} />
              <span style={{
                color: '#d46b08',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                新增指标请联系 hejiabei.oxp
              </span>
            </div>

            <MetricConfigTab
              configFieldMap={byteioConfigFieldMap}
              metricList={metricList}
              metricCategory={4}
              categoryName="ByteIO"
              dialogType={dialogType}
              DIALOG_TYPE_DETAIL={DIALOG_TYPE_DETAIL}
              getAvailableMetricOptions={getAvailableMetricOptions}
              onConfigFieldItemChange={onByteioConfigFieldItemChange}
              renderMetricOption={renderMetricOption}
              onCopyField={onCopyByteioField}
              onDeleteField={onDeleteByteioField}
            />
          </TabPane>

    </Tabs>
          <Row style={{ marginTop: 20 }}>

          </Row>
          <Row>
          {dialogType != DIALOG_TYPE_DETAIL ?
            <Col span={24}>
              <Form.Item wrapperCol={{ offset: 0 }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '20px 0',
                  borderTop: '1px solid #e5e6eb',
                  marginTop: '20px'
                }}>
                  {/* 左侧操作按钮组 */}
                  <div style={{
                    display: 'flex',
                    gap: '16px',
                    alignItems: 'center'
                  }}>
                    {activeTabKey === 'filed_manage' && (
                      <>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconTag style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddField()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#4080ff',
                            color: '#4080ff',
                            backgroundColor: '#f8faff',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(64, 128, 255, 0.1)'
                          }}
                        >
                          添加DS采集字段
                        </Button>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconPlus style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddAllDSFields()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#00b42a',
                            color: '#00b42a',
                            backgroundColor: '#f6ffed',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(0, 180, 42, 0.1)'
                          }}
                        >
                          一键添加所有DS采集字段
                        </Button>
                      </>
                    )}

                    {activeTabKey === 'gameperf_filed_manage' && (
                      <>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconTag style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddGameperfField()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#4080ff',
                            color: '#4080ff',
                            backgroundColor: '#f8faff',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(64, 128, 255, 0.1)'
                          }}
                        >
                          添加GamePerf采集字段
                        </Button>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconPlus style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddAllGameperfFields()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#00b42a',
                            color: '#00b42a',
                            backgroundColor: '#f6ffed',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(0, 180, 42, 0.1)'
                          }}
                        >
                          一键添加所有GamePerf采集字段
                        </Button>
                      </>
                    )}

                    {activeTabKey === 'trace_filed_manage' && (
                      <>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconTag style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddTraceField()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#4080ff',
                            color: '#4080ff',
                            backgroundColor: '#f8faff',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(64, 128, 255, 0.1)'
                          }}
                        >
                          添加VQoS Trace采集字段
                        </Button>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconPlus style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddAllTraceFields()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#00b42a',
                            color: '#00b42a',
                            backgroundColor: '#f6ffed',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(0, 180, 42, 0.1)'
                          }}
                        >
                          一键添加所有VQoS Trace采集字段
                        </Button>
                      </>
                    )}

                    {activeTabKey === 'byteio_filed_manage' && (
                      <>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconTag style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddByteioField()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#4080ff',
                            color: '#4080ff',
                            backgroundColor: '#f8faff',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(64, 128, 255, 0.1)'
                          }}
                        >
                          添加ByteIO采集字段
                        </Button>
                        <Button
                          type="outline"
                          size="large"
                          icon={<IconPlus style={{ fontSize: '16px' }} />}
                          onClick={() => onClickAddAllByteioFields()}
                          disabled={dialogType === DIALOG_TYPE_DETAIL}
                          style={{
                            height: '40px',
                            padding: '0 20px',
                            borderColor: '#00b42a',
                            color: '#00b42a',
                            backgroundColor: '#f6ffed',
                            borderRadius: '8px',
                            fontWeight: '500',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 2px 4px rgba(0, 180, 42, 0.1)'
                          }}
                        >
                          一键添加所有ByteIO采集字段
                        </Button>
                      </>
                    )}
                  </div>

                  {/* 右侧提交按钮 */}
                  <div>
                    <Button
                      type="primary"
                      size="large"
                      htmlType="submit"
                      loading={loading}
                      disabled={dialogType === DIALOG_TYPE_DETAIL}
                      style={{
                        height: '40px',
                        padding: '0 32px',
                        borderRadius: '8px',
                        fontWeight: '600',
                        fontSize: '16px',
                        background: 'linear-gradient(135deg, #4080ff 0%, #165dff 100%)',
                        border: 'none',
                        boxShadow: '0 4px 12px rgba(64, 128, 255, 0.3)',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      提交配置
                    </Button>
                  </div>
                </div>
              </Form.Item>
            </Col>
            :null}
          </Row>
        </Form>


  </Modal>
      <Table
       border={true} // 显示表格外部边框
       borderCell={true} // 显示单元格的内部边框
       hover={false} // 鼠标悬停时显示高亮效果
       stripe={true} // 显示斑马纹效果
       loading={loading} // 显示加载状态
        onChange={onChangeTable}
        columns={columns}
        data={configData}
        pagination={pagination}
      />

  </Card>
  );
};


