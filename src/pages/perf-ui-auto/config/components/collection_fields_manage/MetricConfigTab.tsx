import React, { FC } from 'react';
import { Form, Select, Collapse, Switch, Popover, Input, Grid, Button, Space } from '@arco-design/web-react';
import { IconQuestionCircle, IconTag, IconCopy, IconDelete } from '@arco-design/web-react/icon';
import { comparison_type_list, value_type_list, constants } from 'src/utils/const';

const { Row, Col } = Grid;

const CollapseItem = Collapse.Item;

interface MetricConfigTabProps {
  configFieldMap: any;
  metricList: any[];
  metricCategory: number;
  categoryName: string;
  dialogType: number;
  DIALOG_TYPE_DETAIL: number;
  getAvailableMetricOptions: (category: number, fieldMap: any, currentIndex?: string) => any[];
  onConfigFieldItemChange: (json: any, index: string) => void;
  renderMetricOption: (item: any) => JSX.Element;
  onCopyField: (index: string) => void;
  onDeleteField: (index: string) => void;
}

export const MetricConfigTab: FC<MetricConfigTabProps> = ({
  configFieldMap,
  metricList,
  metricCategory,
  categoryName,
  dialogType,
  DIALOG_TYPE_DETAIL,
  getAvailableMetricOptions,
  onConfigFieldItemChange,
  renderMetricOption,
  onCopyField,
  onDeleteField
}) => {
  return (
    <Collapse
      expandIconPosition={'right'}
      style={{ marginTop: 20 }}
      triggerRegion={'icon'}
      activeKey={Object.keys(configFieldMap)}
    >
      {Object.keys(configFieldMap).map(configFieldIndex => (
        <CollapseItem
          name={configFieldIndex}
          header={
            <div style={{
              marginTop: 10,
              marginLeft: 20,
              width: '100%',
              minWidth: '800px',
              overflow: 'visible',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: '16px'
            }}
            key={configFieldIndex}
            >
              {/* 左侧：字段勾选 */}
              <div style={{ flex: 1, minWidth: 0 }}>
                <Form
                  layout={'inline'}
                  disabled = {dialogType == DIALOG_TYPE_DETAIL}
                >
                   <Form.Item label="字段勾选" required style={{ marginBottom: 0 }}>
                      <Select
                          placeholder={
                            getAvailableMetricOptions(metricCategory, configFieldMap, configFieldIndex).length === 0
                              ? '所有指标已被选择，请删除其他字段后重试'
                              : '请勾选对应的字段，避免重复选择'
                          }
                          allowClear
                          maxTagCount={3}
                          size="large"
                          className="metric-field-select"
                          style={{
                            width: '580px',
                            height: '40px'
                          }}
                      dropdownMenuStyle={{
                        maxHeight: '400px',
                        padding: '8px',
                        borderRadius: '8px',
                        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)'
                      }}
                      dropdownRender={(menu) => (
                        <div>
                          <div style={{
                            padding: '8px 12px',
                            color: '#4080ff',
                            fontWeight: '500',
                            borderBottom: '1px solid #e5e6eb'
                          }}>
                            <IconTag style={{ marginRight: '6px' }} />
                            选择{categoryName}性能指标字段
                          </div>
                          {menu}
                        </div>
                      )}
                      filterOption={(input, option) => {
                        // 处理空选项的情况
                        if (option.props.value === null) {
                          return false;
                        }

                        // 获取对应的指标信息进行搜索
                        const metricId = option.props.value;
                        const metric = metricList.find(item => item.id === metricId);
                        if (!metric) return false;

                        // 在指标的各个字段中搜索
                        const searchText = input.toLowerCase();
                        return (
                          metric.metric_name?.toLowerCase().includes(searchText) ||
                          metric.metric_key?.toLowerCase().includes(searchText) ||
                          metric.metric_desc?.toLowerCase().includes(searchText) ||
                          metric.metric_unit?.toLowerCase().includes(searchText) ||
                          constants.metricTypeMap.get(metric.metric_type)?.toLowerCase().includes(searchText)
                        );
                      }}
                      showSearch
                      key={configFieldMap[configFieldIndex]}
                      defaultValue={configFieldMap[configFieldIndex].metric_id}
                      onChange={(value) => onConfigFieldItemChange({ 'metric_id': value}, configFieldIndex)}
                      value={configFieldMap[configFieldIndex].metric_id}
                    >
                      {(() => {
                        const availableOptions = getAvailableMetricOptions(metricCategory, configFieldMap, configFieldIndex);

                        if (availableOptions.length === 0) {
                          return (
                            <Select.Option
                              key="no-options"
                              value={null}
                              disabled
                              style={{
                                color: '#86909c',
                                fontStyle: 'italic',
                                textAlign: 'center',
                                height: '40px',
                                lineHeight: '40px'
                              }}
                            >
                              所有指标已被选择
                            </Select.Option>
                          );
                        }

                        return availableOptions.map((item) => (
                          <Select.Option
                            key={item.id}
                            value={item.id}
                            className="custom-select-option"
                            style={{
                              height: '40px',
                              lineHeight: '40px',
                              display: 'flex',
                              alignItems: 'center',
                              padding: '0 !important'
                            }}
                          >
                            {renderMetricOption(item)}
                          </Select.Option>
                        ));
                          })()}
                        </Select>
                      </Form.Item>
                    </Form>
                </div>

                {/* 右侧：重要字段开关和操作按钮 */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  flexShrink: 0
                }}>
                  <Switch style={{ width: '50px' }}
                      checked={configFieldMap[configFieldIndex].is_important}
                      onChange={(value) => onConfigFieldItemChange({ 'is_important': value }, configFieldIndex)}
                      checkedText='是' uncheckedText='否'
                      disabled={dialogType === DIALOG_TYPE_DETAIL}
          />
                   <Popover
                        trigger='hover'
                        title='重要字段含义'
                        content={
                          <span>
                            <pre style={{whiteSpace: 'pre - wrap'}} >
                            {`重要字段结果会进行比较
需填写比较的阈值标准，用于判断性能是否符合预期`}</pre>
                            <p ></p>
                          </span>
                        }
                      >
                        <span style={{ whiteSpace: 'nowrap', cursor: 'help' }}>
                          重要字段
                          <IconQuestionCircle style={{fontSize: 15, color: 'gray', marginLeft: '4px'}}/>
                        </span>
                      </Popover>

                      {/* 复制和删除按钮 */}
                      {dialogType !== DIALOG_TYPE_DETAIL && (
                        <Space size="small">
                          <Button
                            type="outline"
                            size="small"
                            icon={<IconCopy />}
                            onClick={() => onCopyField(configFieldIndex)}
                            style={{
                              borderColor: '#165dff',
                              color: '#165dff',
                              backgroundColor: '#f2f7ff'
                            }}
                          >
                            复制
                          </Button>
                          <Button
                            type="outline"
                            size="small"
                            status="danger"
                            icon={<IconDelete />}
                            onClick={() => onDeleteField(configFieldIndex)}
                            style={{
                              borderColor: '#f53f3f',
                              color: '#f53f3f',
                              backgroundColor: '#fff1f0'
                            }}
                          >
                            删除
                          </Button>
                        </Space>
                      )}
                  </div>
            </div>
          }
          key={configFieldIndex}
        >
          <Form
            disabled = {dialogType == DIALOG_TYPE_DETAIL}
          >
            <Row gutter={16}>
            <Col span={8}>
                <Form.Item
                  label="比较标准"
                  required
                  hidden={!configFieldMap[configFieldIndex].is_important}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
              <Select
                placeholder={'请选择比较标准'}
                size="large"
                style={{ width: '100%' }}
                showSearch
                key={configFieldMap[configFieldIndex]}
                defaultValue={configFieldMap[configFieldIndex].comparison_type}
                onChange={(value) => onConfigFieldItemChange({'comparison_type': value }, configFieldIndex)}
                value={configFieldMap[configFieldIndex].comparison_type}
              >
                {comparison_type_list.map((item) => (
                  <Select.Option
                    key={item.key}
                    value={item.value}
                    style={{
                      height: '32px',
                      lineHeight: '32px',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
              </Col>
              <Col span={8}>
              <Form.Item
                label="值类型"
                required
                hidden={!configFieldMap[configFieldIndex].is_important}
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
            <Select
              placeholder={'请选择值类型'}
              size="large"
              style={{ width: '100%' }}
              showSearch
              key={configFieldMap[configFieldIndex]}
              defaultValue={configFieldMap[configFieldIndex].value_type}
              onChange={(value) => onConfigFieldItemChange({'value_type': value }, configFieldIndex)}
              value={configFieldMap[configFieldIndex].value_type}
            >
              {value_type_list.map((item) => (
                <Select.Option
                  key={item.key}
                  value={item.value}
                  style={{
                    height: '32px',
                    lineHeight: '32px',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  {item.label}
                </Select.Option>
              ))}
            </Select>
            </Form.Item>
        </Col>
        <Col span={8}>
                <Form.Item
                  label="比较值"
                  required
                  hidden={!configFieldMap[configFieldIndex].is_important}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
              <Input
                placeholder={'阈值，百分点或数值'}
                style={{ width: '100%' }}
                onChange={(value) => onConfigFieldItemChange({ 'threshold_value': value }, configFieldIndex)}
                defaultValue={configFieldMap[configFieldIndex].threshold_value}
                value={configFieldMap[configFieldIndex].threshold_value}
                key={configFieldIndex}
              />
            </Form.Item>
              </Col>
            </Row>
      </Form>
     
     
    </CollapseItem>
  ))}
</Collapse>
  );
};
