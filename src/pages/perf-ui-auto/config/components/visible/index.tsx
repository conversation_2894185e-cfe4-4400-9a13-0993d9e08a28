import React, { FC, useState, useEffect } from 'react';
import { Message, Table } from '@arco-design/web-react';
import type { PaginationProps } from '@arco-design/web-react';
import { Apis } from '../../utils/apis';

export const TraceConfig: FC = () => {
  const [data, setData] = useState();
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 5,
    current: 1,
    sizeOptions: [5, 10, 15],
  });

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
    },
    {
      title: 'Count',
      dataIndex: 'status',
    },
    {
      title: 'ID',
      dataIndex: '_id',
    },
  ];

  const fetchData = async () => {
    setLoading(true);
    const query = { current: pagination.current, pageSize: pagination.pageSize };
    const result = await Apis.getList({ payload: query });
    if (result.code === 0) {
      const data = result.data;
      setData(data);
      setPagination({ ...pagination, total: result.total });
    } else {
      Message.error(`Request Failed: ${result.message}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize]);

  const onChangeTable = (pagination: PaginationProps) => {
    setPagination(pagination);
  };

  return (
    <div>
      <Table
        onChange={onChangeTable}
        loading={loading}
        columns={columns}
        data={data}
        pagination={pagination}
      />
    </div>
  );
};
