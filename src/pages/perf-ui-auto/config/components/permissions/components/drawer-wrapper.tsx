import React, { FC, useState, useEffect } from 'react';
import {
  Table,
  Message,
  Popconfirm,
  Button,
  Spin,
  Descriptions,
  Divider,
  Drawer,
} from '@arco-design/web-react';
import type { TableColumnProps } from '@arco-design/web-react';
import { Apis } from '../../../utils/apis';

export type IDesc = Record<string, string>;

export interface IProps {
  item: IDesc;
}

export interface IDrawerProps {
  item: IDesc;
  visible: boolean;
  setVisible: (value: boolean) => void;
}

export interface ITableData {
  key?: string;
  name?: string;
  authority?: string;
}

export const CardDesc: FC<IProps> = (props) => {
  const { item } = props;
  const [desc, setDesc] = useState<IDesc>({});
  const [loading, setLoading] = useState<boolean>(false);

  const fetchData = async () => {
    setLoading(true);
    const result = await Apis.getProject({ payload: { _id: item._id } });
    if (result.code === 0) {
      setDesc(result.data);
    } else {
      Message.error(`Check the detail failed: ${result.message}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const descData = Object.keys(desc).map((key) => {
    return {
      label: key,
      value: desc[key],
    };
  });

  return (
    <>
      <Spin style={{ display: 'block' }} loading={loading}>
        <div style={{ padding: '20px' }}>
          <Descriptions colon=" :" layout="inline-vertical" data={descData} />
        </div>
        <Divider />
      </Spin>
    </>
  );
};

export const CardTable: FC<IProps> = (props) => {
  const { item } = props;
  const [pageSize, setPageSize] = useState<number>(3);
  const [pageNum, setPageNum] = useState<number>(1);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<IDesc[]>([]);

  const fetchData = async () => {
    setLoading(true);
    const result = await Apis.getProjectList({ payload: { id: item._id } });
    if (result.code === 0) {
      setData(result.data);
    } else {
      Message.error(`Request Failed: ${result.message}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [refresh]);

  const handleDelete = async (item: IDesc) => {
    const result = await Apis.deleteProject({ payload: { _id: item.key } });
    if (result.code === 0) {
      const flag = !refresh;
      setRefresh(flag);
      Message.success('Delete success');
    } else {
      Message.error(`Delete failed: ${result.message}`);
    }
  };

  const columns: TableColumnProps[] = [
    {
      title: 'Name',
      dataIndex: 'name',
    },
    {
      title: 'Authority',
      dataIndex: 'authority',
    },
    {
      title: 'Actions',
      dataIndex: 'actions',
      render(actions: unknown, item: IDesc) {
        return (
          <>
            <Popconfirm title="whether to delete this?" onOk={() => handleDelete(item)}>
              <Button type="text" status="danger">
                Delete
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  const tableData = data.map(
    (item): ITableData => {
      return {
        key: item._id,
        name: item.name,
        authority: item.authority,
      };
    }
  );

  const handlePagination = (pageNum: number, pageSize: number) => {
    setPageNum(pageNum);
    setPageSize(pageSize);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Engineering Management</h3>
      <Spin style={{ display: 'block' }} loading={loading}>
        <Table
          columns={columns}
          data={tableData}
          pagination={{
            current: pageNum,
            pageSize,
            onChange: handlePagination,
          }}
        />
      </Spin>
    </div>
  );
};

export const DrawerWrapper: FC<IDrawerProps> = (props) => {
  const { item, visible, setVisible } = props;
  return (
    <Drawer
      width={700}
      unmountOnExit
      title={<span>{item?.title}</span>}
      visible={visible}
      onOk={() => {
        setVisible(false);
      }}
      onCancel={() => {
        setVisible(false);
      }}
      footer={null}
    >
      <CardDesc item={item} />
      <CardTable item={item} />
    </Drawer>
  );
};
