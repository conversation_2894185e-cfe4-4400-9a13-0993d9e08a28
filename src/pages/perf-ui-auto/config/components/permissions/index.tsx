import React, { FC, useState, useEffect } from 'react';
import { Card, Button, Message, List } from '@arco-design/web-react';
import dayjs from 'dayjs';
import { DrawerWrapper } from './components/drawer-wrapper';
import { Apis } from '../../utils/apis';

const ListItem = List.Item;

export interface ICardItem {
  [propsName: string]: any;
}

export const PermissionsContent: FC = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [drawerItem, setDrawerItem] = useState<ICardItem>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<ICardItem[]>([]);
  const [pageSize] = useState<number>(8);
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);

  const formatDate = (value: string) => {
    return dayjs(value).format('YYYY-MM-DD');
  };

  const fetchData = async () => {
    setLoading(true);
    const result = await Apis.getProjectList({
      payload: {
        pageSize,
        current,
      },
    });
    if (result.code === 0) {
      setData(result.data || []);
      setTotal(result.total);
      setCurrent(result.current);
    } else {
      Message.error(`Request Failed: ${result.message}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [current]);

  const onChange = (pageNumber: number) => {
    setCurrent(pageNumber);
  };

  const getActions = (item: ICardItem) => {
    const handleClick1 = () => {
      setVisible(true);
      setDrawerItem(item);
    };

    return [
      <div key={1}>
        <Button type="primary" onClick={handleClick1}>
          Details
        </Button>
      </div>,
    ];
  };

  return (
    <div>
      <List
        loading={loading}
        split={false}
        bordered={false}
        grid={{
          xs: 24,
          sm: 24,
          md: 24,
          lg: 12,
          xl: 6,
          xxl: 6,
        }}
        pagination={{
          pageSize,
          total,
          current,
          onChange,
        }}
        dataSource={data}
        render={(item, index) => {
          return (
            <ListItem key={index} style={{ padding: '10px' }} actionLayout="vertical">
              <Card style={{ width: '100%' }} title={item.title} actions={getActions(item)}>
                <div>Type：{item.type}</div>
                <div>Owner：{item.admin}</div>
                <div>Updated：{formatDate(item.createdAt)}</div>
              </Card>
            </ListItem>
          );
        }}
      />
      <DrawerWrapper item={drawerItem} visible={visible} setVisible={setVisible} />
    </div>
  );
};
