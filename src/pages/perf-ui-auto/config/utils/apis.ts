import { createProxy } from '@devsre/request';

const proxy = createProxy({
  consul: {
    psm: 'devsre.app.proxy',
  },
});

interface Payload {
  payload: Record<string, unknown>;
}

export interface IParams {
  [propName: string]: any;
}

export const Apis = {
  getList: async ({ payload }: Payload) => {
    const res = await proxy.get('/api/getAppList', {
      params: payload,
    });
    return res.data;
  },

  getProjectList: async ({ payload }: Payload) => {
    const res = await proxy.get('api/getProjectList', {
      params: payload,
    });
    return res.data;
  },

  getProject: async ({ payload }: Payload) => {
    const res = await proxy.get('api/getProject', {
      params: payload,
    });
    return res.data;
  },

  deleteProject: async ({ payload }: Payload) => {
    const res = await proxy.post('/api/deleteProject', payload);
    return res.data;
  },
};
