import React, { useEffect, useState } from 'react';
import type { TableColumnProps } from '@arco-design/web-react';
import { Button, Card, Form, Image, Input, Modal, Space, Table, Tag, Tooltip, Message, Popconfirm } from '@arco-design/web-react';
import {
  IconCheckCircleFill,
  IconCloseCircle,
  IconDelete,
  IconEdit,
  IconThunderbolt,
  IconWifi,
} from '@arco-design/web-react/icon';
import { BackendApis2 } from 'src/utils/backendApis2';
import { useLocation } from 'react-router-dom';
import { useUser } from '@devsre/react-utils'
import {defaultCountrys,developer_list} from 'src/utils/const'


export default function CurdCard() {
  interface Record {
    id: number;
    name: string;
    sys_type: number;
    sys_version: string;
    brand: string;
    model: string;
    resolution: string;
    udid: string;
    ip: string;
    serial_port: string;
    is_occupied: number;
    user: string;
    owner: string;
    state: number;
    connect_type: number[];
    create_time: string;
    update_time: string;
  }
  const { email, name } = useUser();
  const [loading, setLoading] = useState(false);
  const [deviceDataList, setDeviceList] = useState([]);
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Record>();
  const { state } = useLocation(); // 获取传递的状态对象

  const client_id = state?.client_id; // 从 state 中获取客户端 ID
  const deviceColumns: TableColumnProps[] = [
    {
      title: '设备ID',
      dataIndex: 'id',
      align: 'center' as const,
    },
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center' as const,
    },
    {
      title: '系统类型',
      dataIndex: 'sys_type',
      align: 'center' as const,
    },
    {
      title: '系统版本',
      dataIndex: 'sys_version',
      align: 'center' as const,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      align: 'center' as const,
    },
    {
      title: '型号',
      dataIndex: 'model',
      align: 'center' as const,
    },
    {
      title: '分辨率',
      dataIndex: 'resolution',
      align: 'center' as const,
    },
    {
      title: 'UDID',
      dataIndex: 'udid',
      align: 'center' as const,
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      align: 'center' as const,
    },
    {
      title: '连接类型',
      dataIndex: 'connect_type',
      align: 'center' as const,
      render: (_, record) => (
        <span>
          {Array.isArray(record.connect_type) && record.connect_type.map((type, index) => (
            <Tag
              key={index}
              color={type === 1 ? 'blue' : 'cyan'}
              icon={type === 1 ? <IconThunderbolt /> : <IconWifi />}
              style={{ marginRight: '4px' }}
            >
              {type === 1 ? '有线连接' : '无线连接'}
            </Tag>
          ))}
          {(!Array.isArray(record.connect_type) || record.connect_type.length === 0) && (
            <Tag color="red" icon={<IconCloseCircle />}>
              断开连接
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      align: 'center' as const,
      render: (_, record) => (
        <span>
          {record.state === 0 ? (
            <Tag color="red" icon={<IconCloseCircle />}>
              离线
            </Tag>
          ) : (
            <Tag color="green" icon={<IconCheckCircleFill />}>
              在线
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: '是否占用',
      dataIndex: 'is_occupied',
      align: 'center' as const,
      render: (_, record) => (
        <span>
          {record.is_occupied === 0 ? (
            <Tag color="green" icon={<IconCheckCircleFill />}>
              空闲
            </Tag>
          ) : (
            <Tag color="red" icon={<IconCloseCircle />}>
              占用中
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: '使用人',
      dataIndex: 'user',
      align: 'center' as const,
      render: (col, record, index) => (
        <Space>
          <Image
            preview={false}
            style={{ borderRadius: '50%' }}
            height="20"
            src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.owner}?format=40x40.png`}
          ></Image>
          <Tooltip mini content={record.owner}>
            {record.owner.length > 8 ? record.owner.substring(0, 8) + '...' : record.owner}
          </Tooltip>
        </Space>
      ),
    },
    // {
    //   title: '责任人',
    //   dataIndex: 'owner',
    //   align: 'center' as const,
    //   render: (col, record, index) => (
    //     <Space>
    //       <Image
    //         preview={false}
    //         style={{ borderRadius: '50%' }}
    //         height="20"
    //         src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.owner}?format=40x40.png`}
    //       ></Image>
    //       <Tooltip mini content={record.owner}>
    //         {record.owner.length > 8 ? record.owner.substring(0, 8) + '...' : record.owner}
    //       </Tooltip>
    //     </Space>
    //   ),
    // },
    {
      title: ' 操作',
      dataIndex: 'operate',
      align: 'center' as const,
      render: (_, record) => (
        <div>
          <Button
            type="text"
            // icon={<IconEdit />}
            onClick={() => handleDeviceDetailEditClick(record)}
          >
            编辑
          </Button>
               {/* <Button
                type="text"
            icon={<IconDelete />}
            onClick={() => handleDeleteButtonClick(record.id)}
          >
            删除
               </Button> */}

           {(record.owner == name||developer_list.includes(name))?
                         <Popconfirm
                                      focusLock
                                      title='删除确认'
                                      content={`确认删除设备 "${record.name}" ？`}
                                      onOk={() => {
                                        handleDeleteButtonClick(record.id)
                                      }}
                                      onCancel={() => {
                                        Message.info({
                                          content: '取消删除',
                                        });
                                      }}
                                    >
                         <Button
                          type="text"
                          // icon={<IconList />}
                          // onClick={() => onClickDeleteAccount(record)}
                          >删除
                         </Button>
                        </Popconfirm>
                        :null}
        </div>
      ),
    },
  ];

  // 定义设备系统类型映射表
  const deviceSysTypeMap: Map<number, string> = new Map([
    [0, ''],
    [1, 'Android'],
    [2, 'iOS'],
    [3, 'Windows'],
    [4, 'MacOS'],
    [5, 'Linux'],
  ]);
  // 定义设备连接状态映射表
  const deviceStateMap: Map<number, string> = new Map([
    [0, '离线'],
    [1, '在线'],
  ]);
  // 定义设备连接类型映射表
  const deviceConnectTypeMap: Map<number, string> = new Map([
    [0, ''],
    [1, '有线连接'],
    [2, '无线连接'],
  ]);
  

  // 分页器
  const [deviceListPagination, setDeviceListPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 20,
    current: 1,
    pageSizeChangeResetCurrent: true,
    sizeOptions: [10, 20, 50, 100, 200],
  });

  // 任务列表分页器变化
  const onChange = (pagination) => {
    fetchDeviceList(pagination, client_id)
  };

  // 获取设备列表
  const fetchDeviceList = async (pagination,client_id) => {
    setLoading(true);
    const response = await BackendApis2.getDeviceList({
      payload: { client_id: client_id,page:pagination.current, page_size: pagination.pageSize },
    }).finally(() => {
      setLoading(false);
    });
    const transformedDeviceList = response.items.map((device) => ({
      ...device,
      sys_type: deviceSysTypeMap.get(device.sys_type) || '',
    }));
    setDeviceList(transformedDeviceList);
    deviceListPagination.current = response.page;
    deviceListPagination.pageSize = response.page_size;
    deviceListPagination.total = response.total;
  };
  // 编辑设备详情
  const handleDeviceDetailEditClick = (record) => {
    setCurrentRecord(record);
    setIsEditing(true);
    form.setFieldsValue(record);
  };
  // 编辑设备详情确认
  const handleDeviceDetailEditConfirm = (values) => {
    setCurrentRecord(null);
    setIsEditing(false);
  };
  // 编辑设备详情取消
  const handleDeviceDetailEditCancel = () => {
    setCurrentRecord(null);
    setIsEditing(false);
  };
  // 删除设备
  const handleDeleteButtonClick = async (device_id) => {
    // console.log(client_id);
    // Message.info('删除'+client_id );
    const response = await BackendApis2.deleteDevice({ payload: { device_id: device_id } }).finally(() => {
    });
     if (response.code != 200) {
          Message.error("删除失败")
        } else {
          Message.success("删除成功")
          fetchDeviceList(deviceListPagination,client_id);
        }
  };
  // 使用useEffect钩子，初始化
  useEffect(() => {
    if (client_id) {
      fetchDeviceList(deviceListPagination,client_id);
    }
  }, [client_id]);


  return (
    <Card bordered={false}>
      <Table
        border={true} // 显示表格外部边框
        borderCell={true} // 显示单元格的内部边框
        hover={true} // 鼠标悬停时显示高亮效果
        loading={loading} // 显示加载状态
        columns={deviceColumns} // 表格的列配置
        pagination={deviceListPagination} // 分页器
        data={deviceDataList} // 表格的数据源
        onChange={onChange}
      />
      <Modal
        title="设备详情"
        visible={isEditing}
        onOk={() => form.submit()}
        onCancel={handleDeviceDetailEditCancel}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          onSubmit={(values) => {
            return new Promise((resolve) => {
              handleDeviceDetailEditConfirm(values);
              setTimeout(() => {
                resolve(1);
              }, 3000);
            });
          }}
          layout="vertical"
          initialValues={currentRecord}
        >
          <Form.Item
            label="设备名称"
            field="name"
            rules={[{ required: true, message: '设备名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="系统类型" field="sys_type">
            <Input disabled />
          </Form.Item>
          <Form.Item label="系统版本" field="sys_version">
            <Input disabled />
          </Form.Item>
          <Form.Item label="品牌" field="brand">
            <Input disabled />
          </Form.Item>
          <Form.Item label="型号" field="model">
            <Input disabled />
          </Form.Item>
          <Form.Item label="分辨率" field="resolution">
            <Input disabled />
          </Form.Item>
          {/* <Form.Item label="资产编号" field="asset_number">
            <Input disabled />
          </Form.Item>
          <Form.Item label="UUID" field="uuid">
            <Input disabled />
          </Form.Item> */}
          <Form.Item label="UDID" field="udid">
            <Input disabled />
          </Form.Item>
          <Form.Item label="使用人" field="user">
            <Input disabled />
          </Form.Item>
          {/* <Form.Item label="责任人" field="owner">
            <Input disabled />
          </Form.Item> */}
          <Form.Item label="状态" field="state">
            <Input disabled />
          </Form.Item>
          <Form.Item label="连接方式" field="connect_type">
            <Input disabled />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
}
