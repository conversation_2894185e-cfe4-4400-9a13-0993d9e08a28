import React, { useEffect, useState } from 'react';
import {
  Button,
  Card,
  Form,
  Image,
  Input,
  Message,
  Modal,
  Popconfirm,
  Space,
  Table,
  Tag,
  Tooltip,
} from '@arco-design/web-react';
import { IconCheckCircleFill, IconCloseCircle, IconDelete, IconEdit, IconList } from '@arco-design/web-react/icon';
import { BackendApis2 } from 'src/utils/backendApis2';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@devsre/react-utils';
import {developer_list} from 'src/utils/const';

export default function CurdCard() {
  interface Record {
    id: number;
    team: string;
    name: string;
    sys_type: string;
    mac_address: string;
    ipv4: string;
    ipv6: string;
    state: string;
    owner: string;
  }
  const { email, name } = useUser();

  const [loading, setLoading] = useState(false);
  const [clientList, setClientList] = useState([]);
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Record | null>(null);
  const [clientDetail, setClientDetail] = useState([]);
  const navigate = useNavigate();
  const clientColumns = [
    {
      title: 'id',
      dataIndex: 'id',
      align: 'center',
      width: '5%',
    },
    {
      title: '团队',
      dataIndex: 'business["business_name"]',
      align: 'center',
      width: '5%',
    },
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
      width: '10%',
    },
    {
      title: '系统类型',
      dataIndex: 'sys_type',
      align: 'center',
      width: '5%',
    },
    {
      title: 'Mac地址',
      dataIndex: 'mac_address',
      align: 'center',
      width: '10%',
    },
    {
      title: ' IPv4',
      dataIndex: 'ipv4',
      align: 'center',
      width: '8%',
    },
    {
      title: ' IPv6',
      dataIndex: 'ipv6',
      align: 'center',
      width: '10%',
    },
    {
      title: '状态',
      dataIndex: 'state',
      align: 'center',
      width: '5%',
      render: (_, record) => (
        <span>
          {record.state === '在线' ? (
            <Tag color="green" icon={<IconCheckCircleFill />}>
              {record.state}
            </Tag>
          ) : (
            <Tag color="red" icon={<IconCloseCircle />}>
              {record.state}
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: ' Owner',
      dataIndex: 'owner',
      align: 'center',
      width: '8%',
      render: (col, record, index) => (
        <Space>
          <Image
            preview={false}
            style={{ borderRadius: '50%' }}
            height="20"
            src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.owner}?format=40x40.png`}
          ></Image>
          <Tooltip mini content={record.owner}>
            {record.owner.length > 8 ? record.owner.substring(0, 8) + '...' : record.owner}
          </Tooltip>
        </Space>
      ),
    },
    {
      title: ' 操作',
      dataIndex: 'action',
      align: 'center',
      width: '20%',
      render: (_, record) => (
        <div>
          <Button
            type="text"
            icon={<IconList />}
            onClick={() => handleDeviceListButtonClick(navigate, record.id)}
          >
            设备列表
          </Button>
          <Button
            type="text"
            icon={<IconEdit />}
            onClick={() => handleClientDetailEditClick(record)}
          >
            编辑
          </Button>
          <Popconfirm
            focusLock
            title="删除客户端"
            content="确定要删除该客户端吗？"
            onOk={() => {
              handleDeleteButtonClick(record.id);
              Message.info({
                content: '删除成功',
              });
            }}
            onCancel={() => {
              Message.error({
                content: '删除已取消',
              });
            }}
          >
            {(record.owner == name||developer_list.includes(name))?
            <Button type="text" icon={<IconDelete />}>
              删除
            </Button>:null}
          </Popconfirm>
        </div>
      ),
    },
  ];

  // 定义设备系统类型映射表
  const clientSysTypeMap: Map<number, string> = new Map([
    [0, ''],
    [1, 'Android'],
    [2, 'iOS'],
    [3, 'Windows'],
    [4, 'MacOS'],
    [5, 'Linux'],
  ]);
  const clientSysTypeMap_anti: Map<string,number> = new Map([
    ['', 0],
    ['Android',1],
    ['iOS',2],
    ['Windows',3],
    ['MacOS',4],
    ['Linux',5],
  ]);
  // 定义设备连接状态映射表
  const clientStateMap: Map<number, string> = new Map([
    [0, '离线'],
    [1, '在线'],
  ]);
  const clientStateMap_anti: Map<string,number> = new Map([
    ['离线', 0],
    ['在线', 1],
  ]);
  const fetchClientList = async () => {
    setLoading(true);
    const response = await BackendApis2.getClientList().finally(() => {
      setLoading(false);
    });
    const transformedClientList = response.client_items.map((client) => ({
      ...client,
      sys_type: clientSysTypeMap.get(client.sys_type) || '', // 转换系统类型
      state: clientStateMap.get(client.state) || '', // 转换连接状态
    }));
    setClientList(transformedClientList);
  };
  const fetchUpdateClientDetail = async (body) => {
    setLoading(true);
    const response = await BackendApis2.updateClientDetail({ body }).finally(() => {
      setLoading(false);
    });
    const transformedClientDetail = response.map((client) => ({
      ...client,
      sys_type: clientSysTypeMap.get(client.sys_type) || 0, // 转换系统类型
      state: clientStateMap.get(client.state) || 0, // 转换连接状态
    }));
    setClientDetail(transformedClientDetail);
  };
  const fetchDeleteClient = async (client_id) => {
    setLoading(true);
    await BackendApis2.deleteClient({ payload: { client_id: client_id } }).finally(() => {
    });
    setLoading(false);
  };

  const handleDeviceListButtonClick = (navigate, client_id) => {
    navigate('/perf-ui-auto/client/device', { state: { client_id } }); // 将客户端ID作为state传递给下一个页面
  };
  const handleClientDetailEditClick = (record) => {
    form.setFieldsValue(record);
    // setCurrentRecord(record);
    setIsEditing(true);
  };
  const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  const handleClientDetailEditConfirm = async(values) => {
    fetchUpdateClientDetail({
      id: values.id,
      business_id: values.business_id,
      name: values.name,
      sys_type: clientSysTypeMap_anti.get(values.sys_type)|| 0,
      mac_address: values.mac_address,
      ipv4: values.ipv4,
      ipv: values.ipv6,
      state: clientStateMap_anti.get(values.state),
      port:  values.port,
      owner: values.owner

    });
    form.resetFields();
    // setCurrentRecord(null);
    setIsEditing(false);
    await wait(800);
    fetchClientList();
    // clearTimeout(timer);
  };

  const handleClientDetailEditCancel = () => {
    form.resetFields();
    // setCurrentRecord(null);
    setIsEditing(false);
  };
  const handleDeleteButtonClick = (client_id) => {
    fetchDeleteClient(client_id);
    fetchClientList();
  };

  useEffect(() => {
    fetchClientList();
  }, []);

  return (
    <Card bordered={false}>
      <Table
        border={true} // 显示表格外部边框
        borderCell={true} // 显示单元格的内部边框
        hover={true} // 鼠标悬停时显示高亮效果
        loading={loading} // 显示加载状态
        columns={clientColumns} // 表格的列配置
        data={clientList} // 表格的数据源
      />
      <Modal
        title="客户端详情"
        visible={isEditing}
        onOk={() => form.submit()}
        onCancel={() => handleClientDetailEditCancel()}
        // onSubmit={(value) => handleSubmitAddOrEditAppGroupDialog(value)}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          onSubmit={(values) => {
            return new Promise((resolve) => {
              handleClientDetailEditConfirm(values);
              setTimeout(() => {
                resolve(1);
              }, 3000);
            });
          }}
          
          layout="vertical"
          // initialValues={currentRecord}
        >
          <Form.Item field="id" label="客户端ID">
            <Input disabled />
          </Form.Item>
          <Form.Item field="business['business_name']" label="团队">
            <Input disabled />
          </Form.Item>
          <Form.Item field="name" label="名称" rules={[{ required: true, message: '名称' }]}>
            <Input />
          </Form.Item>
          <Form.Item field="sys_type" label="系统类型">
            <Input disabled />
          </Form.Item>
          <Form.Item field="mac_address" label="Mac地址">
            <Input disabled />
          </Form.Item>
          <Form.Item field="ipv4" label="IPv4">
            <Input disabled />
          </Form.Item>
          <Form.Item field="ipv6" label="IPv6">
            <Input disabled />
          </Form.Item>
          <Form.Item field="state" label="状态">
            <Input disabled />
          </Form.Item>
          <Form.Item field="owner" label="Owner">
            <Input disabled />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
}
