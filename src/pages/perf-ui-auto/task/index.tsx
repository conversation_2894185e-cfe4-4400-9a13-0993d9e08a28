import React, { useState, useEffect } from 'react';
import { Card, Form, Input,Select, Table, Grid, Space, Image, Tooltip, Tag, Button, Message, Popconfirm,Alert } from '@arco-design/web-react';
import Builder from '@devsre/builder';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@devsre/react-utils'
import {
  IconCopy,
  IconEdit,
  IconPlayArrow,
  IconRefresh,
  IconList,
  IconDelete,
  IconFile
} from '@arco-design/web-react/icon';
import { BackendApis2 } from 'src/utils/backendApis2';
import config from './config.json';
import{developer_list} from'src/utils/const';
const Option = Select.Option;
const { Row, Col } = Grid;

export default function CurdCard() {
  const { email, name } = useUser()
  const navigate = useNavigate();
  const [taskListForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [businessList, setBusinessList] = useState([])
  const [statusList, setStatusList] = useState([]);
  const [searchFormValues, setSearchFormValues] = useState({}); //查询任务的条件
  const [taskList, setTaskList] = useState([])
  const [copyTaskName, setCopyTaskName] = useState('');


  // 任务类型分类
  const taskTypeMap: Map<number, Map<string, string>> = new Map([
    [1, new Map([['name', '版本回归'], ['color', 'purple']])],
    [2, new Map([['name', 'libra实验'], ['color', 'blue']])],
  ]);

  // 任务状态分类
  const TASK_STATUS_NOT_EXECUTE = 0
  const TASK_STATUS_WAITING = 1
  const TASK_STATUS_RUNNING = 2
  const TASK_STATUS_SUCCESS = 3
  const TASK_STATUS_ERROR = 4
  const TASK_STATUS_RETRY_PENDING = 5  
  const TASK_STATUS_RETRY_RUNNING = 6 
  const TASK_STATUS_CANCELED = 7 
  const TASK_STATUS_TIMEOUT = 8 
  const taskStatusMap: Map<number, Map<string, string>> = new Map([
    [TASK_STATUS_NOT_EXECUTE, new Map([['name', '未开始'], ['color', 'gray']])],
    [TASK_STATUS_WAITING, new Map([['name', '等待中'], ['color', 'gray']])],
    [TASK_STATUS_RUNNING, new Map([['name', '执行中'], ['color', 'blue']])],
    [TASK_STATUS_SUCCESS, new Map([['name', '执行成功'], ['color', 'green']])],
    [TASK_STATUS_ERROR, new Map([['name', '执行失败'], ['color', 'red']])],
    [TASK_STATUS_RETRY_PENDING, new Map([['name', '重试等待中'], ['color', 'orange']])],
    [TASK_STATUS_RETRY_RUNNING, new Map([['name', '重试执行中'], ['color', 'orange']])],
    [TASK_STATUS_CANCELED, new Map([['name', '已取消'], ['color', 'gray']])],
    [TASK_STATUS_TIMEOUT, new Map([['name', '已超时'], ['color', 'orange']])],
  ]);

  // 任务结果分类
  const TASK_RESULT_NOT_COMPLETE = 0
  const TASK_RESULT_SUCCESS = 1
  const TASK_RESULT_FAIL = 2
  const taskResultMap: Map<number, Map<string, string>> = new Map([
    [TASK_RESULT_NOT_COMPLETE, new Map([['name', '暂无结果'], ['color', 'gray']])],
    [TASK_RESULT_SUCCESS, new Map([['name', '通过'], ['color', 'green']])],
    [TASK_RESULT_FAIL, new Map([['name', '不通过'], ['color', 'red']])],

  ]);

  // 分页器
  const [taskListPagination, setTaskListPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 20,
    current: 1,
    pageSizeChangeResetCurrent: true,
    sizeOptions: [10, 20, 50, 100, 200],
  });

  // 获取业务列表
  const fetchBusinessList = async () => {
    const response = await BackendApis2.fetchBusinessList().finally(() => { });
    setBusinessList(response.items);
  };
  


  // 获取任务列表
  const fetchTaskList = async (pagination, body) => {
    setLoading(true)
    const payload = { page: pagination.current, page_size: pagination.pageSize };
    const response = await BackendApis2.getPerfTaskList({ payload, body }).finally(() => {
      setLoading(false)
    });
    setTaskList(response.items);
    taskListPagination.current = response.page;
    taskListPagination.pageSize = response.page_size;
    taskListPagination.total = response.total;
    setTaskListPagination(taskListPagination);
  };

  // 复制任务+子任务
  const copyTask = async (task_id) => {
    const payload = { "task_id": task_id, "operator": name }
    const response = await BackendApis2.copyPerfTask({ payload }).finally(() => {
    });
    if (response.code != 200) {
      Message.error("复制失败")
    } else {
      Message.success("复制成功")
      fetchTaskList(taskListPagination, searchFormValues)
    }
  };

  // 删除任务+子任务
  const deleteTask = async (task_id) => {
    const payload = { "task_id": task_id }
    const response = await BackendApis2.deletePerfTask({ payload }).finally(() => {
    });
    if (response.code != 200) {
      Message.error("删除失败")
    } else {
      Message.success("删除成功")
      fetchTaskList(taskListPagination, searchFormValues)
    }
  };

  // 执行任务
  const executeTask = async (task_id) => {
    const payload = { "task_id": task_id }
    const response = await BackendApis2.executePerfTask({ payload }).finally(() => {
    });
    if (response.code != 200) {
      Message.error("执行失败")
    } else {
      Message.success("开始执行")
      fetchTaskList(taskListPagination, searchFormValues)
    }
  };

  // 重试任务
  const retryTask = async (task_id) => {
    const payload = { "task_id": task_id }
    const response = await BackendApis2.retryPerfTask({ payload }).finally(() => {
    });
    if (response.code != 200) {
      Message.error("重试失败")
    } else {
      Message.success("开始重试")
      fetchTaskList(taskListPagination, searchFormValues)
    }
  };
  // 取消任务
  const cancelTask = async (task_id,status,client_id) => {
    const body = { 
      "task_id": task_id,
      "status":status,
      "client_id":client_id
     }
    const response = await BackendApis2.cancelPerfTask({ body }).finally(() => {
    });
    if (response.code != 200) {
      Message.error("取消失败")
      fetchTaskList(taskListPagination, searchFormValues)

    } else {
      Message.success("取消成功")
      fetchTaskList(taskListPagination, searchFormValues)
    }
  };
  // 根据条件搜索任务
  const handleTaskListSearch = (value, fieldName) => {
    const updatedFormValues = {
      ...searchFormValues,
      [fieldName]: value,
    };
    setSearchFormValues(updatedFormValues)
    // 需要设置localstate，值筛选器参数
    localStorage.setItem('taskListSearchFormValues', JSON.stringify(updatedFormValues))
    fetchTaskList(taskListPagination, updatedFormValues);
  };

  const clickSearch = () => {
    fetchTaskList(taskListPagination, searchFormValues);
  }

  // 点击复制
  const onClickCopy = (record) => {
    // Message.info("复制")
    copyTask(record.id)
  };

  // 点击编辑
  const onClickEdit = (record) => {
    // Message.info("编辑")
    navigate(`/perf-ui-auto/task/edit`, { state: { data: { task_id: record.id } } });
  };

  // 点击执行
  const onClickExecute = (record) => {
    // Message.info("执行")
    executeTask(record.id)
  };
  // 点击取消
  const onClickCancel = (record) => {
    cancelTask(record.id,record.status,record.client_id)
  }

  // 点击执行结果
  const onClickResultDetail = (record) => {
    // Message.info("执行结果")
    navigate(`/perf-ui-auto/task/detail?task_id=${record.id}`);
  };

  // 点击重试
  const onClickRetry = (record) => {
    // Message.info("重试")
    retryTask(record.id)
  };

  // 点击性能报告
  const onClickPerfReport = (record) => {
    navigate (`/perf-ui-auto/report?task_id=${record.id}&type=${record.type}&task_name=${record.name}&perf_tool_type=${record.perf_tool_type}&config_id=${record.config_id}`);
    // navigate(`/perf-ui-auto/report`, { state: { data: { task_id: record.id,task_name:record.name} } });
    // Message.info("性能报告")
  };

  // 点击删除
  const onClickDelete = (record) => {
    // Message.info("删除")
    deleteTask(record.id)
  };

  // 点击创建任务
  const onClickCreateTask = () => {
    // Message.info("创建任务")
    navigate(`/perf-ui-auto/task/edit`);
  };

  // 任务列表分页器变化
  const onChange = (pagination) => {
    fetchTaskList(pagination, searchFormValues)
  };

  // 刷新复制任务的名称
  const refreshCopyTaskName = (value) => {
    setCopyTaskName(value);
  };

  // 初始化数据
  useEffect(() => {
    fetchBusinessList();
    // 先判断localstate中是否有筛选参数，有的话，设置筛选参数
    console.log('taskListSearchFormValues');
    const storedFilter = localStorage.getItem('taskListSearchFormValues')
    console.log(storedFilter)
    if (storedFilter) {
      taskListForm.setFieldsValue(JSON.parse(storedFilter))
      setSearchFormValues(JSON.parse(storedFilter))
      fetchTaskList(taskListPagination, JSON.parse(storedFilter))
    }
    else{
       fetchTaskList(taskListPagination, searchFormValues)
      }
    // 否则，设置默认筛选参数
   
  }, []);

  
  // 列表页表格的列配置
  const taskListColumns = [
    {
      title: 'id', dataIndex: 'id', width: '3%', ellipsis: true
    },
    {
      title: '业务', dataIndex: 'business_name', width: '5%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    },
    {
      title: '任务名称', dataIndex: 'name', width: '13.5%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    },
    {
      title: '任务类型', dataIndex: 'type', width: '5%', ellipsis: true,
      render: (col, record, index) => (
        <Tag color={taskTypeMap.get(record.type).get('color')}>{taskTypeMap.get(record.type).get('name')}</Tag>
      )
    },
    {
      title: '创建人', dataIndex: 'owner', width: '8%', ellipsis: true,
      render: (col, record, index) => (
        <Space>
          <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.owner}?format=40x40.png`}></Image>
          <Tooltip mini content={record.owner}>
            {/* {record.owner.length > 18 ? record.owner.substring(0, 18) + '...' : record.owner} */}
            <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{record.owner}</div>
          </Tooltip>
        </Space>
      )
    },
    {
      title: '创建时间', dataIndex: 'create_time', width: '8%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    },
    {
      title: '状态', dataIndex: 'status', width: '5%', ellipsis: true,
      render: (col, record, index) => (
        <Tag color={taskStatusMap.get(record.status).get('color')}>
          {taskStatusMap.get(record.status).get('name')}
        </Tag>
      )
    },
    // {
    //   title: '临时测试', dataIndex:'config_id', width: '6%', ellipsis: true
      
    // },
    // {
    //   title: '结果', dataIndex: 'result', width: '6%', ellipsis: true,
    //   render: (col, record, index) => (
    //     <Tag color={taskResultMap.get(record.result).get('color')}>{taskResultMap.get(record.result).get('name')}</Tag>
    //   )
    // },
    {
      title: '操作', dataIndex: 'opreation', width: '16%', ellipsis: true,
      render: (col, record, index) => (
        <span>
          {/* <Row> */}
          {/* <Col span={6}> */}
          <Popconfirm
            focusLock
            title={`复制确认`}
            // style={{ width: 700 }} 
            content={
              `确认复制任务 "${record.name}" ?`
                          //  <div>
                          //     <Input
                          //       style={{ width: 300 }}
                          //       // defaultValue={record.name}
                          //       value={copyTaskName}
                          //       onChange={refreshCopyTaskName}
                          //       // placeholder="新记录名称"
                          //       className="popconfirm-input"
                          //       // onPressEnter={record.name.trim() ? onCopyApp(record) : null}
                          //       autoFocus
                          //     />
                            
                          //   </div>
            }
            onOk={() => {
              onClickCopy(record)
            }}
            onCancel={() => {
              Message.info({
                content: '取消复制',
              });
            }}
          >
            <Button
              type="text"
              // icon={<IconCopy />}
            // onClick={() => onClickCopy(record)}
            >
              复制
            </Button>
          </Popconfirm>
          {/* </Col> */}
          <Button
            type="text"
            // icon={<IconEdit />}
            onClick={() => onClickEdit(record)}>
            编辑
          </Button>
          {(record.status == TASK_STATUS_NOT_EXECUTE) ?
            // <Col span={6}>
            <Button
              type="text"
              // icon={<IconPlayArrow />}
              onClick={() => onClickExecute(record)}>
              执行
            </Button>
            // </Col>
            :
            <span />
          }
          {((record.status == TASK_STATUS_WAITING)||(record.status === TASK_STATUS_RUNNING)||(record.status ===TASK_STATUS_RETRY_PENDING)||(record.status===TASK_STATUS_RETRY_RUNNING)) ?
            // <Col span={6}>
            <Button
              type="text"
              // icon={<IconPlayArrow />}
              onClick={() => onClickCancel(record)}>
              取消
            </Button>
            // </Col>
            :
            <span />
          }
          {((record.status != TASK_STATUS_NOT_EXECUTE) && (record.status != TASK_STATUS_WAITING)) ?
            // <Col span={6}>
            <Button
              type="text"
              // icon={<IconList />}
              onClick={() => onClickResultDetail(record)}>
              执行结果
            </Button>
            // </Col>
            :
            <span />
          }
          {((record.status == TASK_STATUS_ERROR) || (record.status == TASK_STATUS_CANCELED) || (record.status == TASK_STATUS_TIMEOUT))?
            // <Col span={6}>
            <Button
              type="text"
              // icon={<IconRefresh />}
              onClick={() => onClickRetry(record)}>
              重试
            </Button>
            // </Col>
            :
            <span />
          }
          {(record.status == TASK_STATUS_SUCCESS) ?
            // <Col span={6}>
            <Button
              type="text"
              // icon={<IconFile />}
              onClick={() => onClickPerfReport(record)}>
              性能报告
            </Button>
            // </Col>
            :
            <span />
          }
          {((record.status != TASK_STATUS_RUNNING)&&(record.status != TASK_STATUS_RETRY_RUNNING)&&(record.owner == name||developer_list.includes(name))) ?
            // <Col span={6}>
            <Popconfirm
              focusLock
              title='删除确认'
              content={`删除任务后无法恢复，请再次确认删除任务 "${record.name}" ？`}
              onOk={() => {
                onClickDelete(record)
              }}
              onCancel={() => {
                Message.info({
                  content: '取消删除',
                });
              }}
            >
              <Button
                type="text"
                // icon={<IconDelete />}
                // onClick={() => onClickDelete(record)}
                >
                删除
              </Button>
            </Popconfirm>
            // </Col>
            :
            <span />
          }
          {/* </Row> */}
        </span>
      )
    },
  ]

  return (
    <Card bordered={false}>

      <Form
        style={{ marginTop: '20px' }}
        wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
        form={taskListForm}
      >
        <Row gutter={20}>
          <Col span={3}>
            <Form.Item field="business_id">
              <Select
                placeholder={'业务'}
                // options={businessList}
                allowClear
                maxTagCount={1}
                size="large"
                showSearch
                onChange={(value) => handleTaskListSearch(value, 'business_id')}
              >
                {businessList.map((item, index) => (
                  <Option key={item.id} value={item.id}> {item.business_name} </Option>
                ))}
              </Select>
              
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item field="status">
              <Select
                placeholder={'执行状态'}
                // options={businessList}
                allowClear
                maxTagCount={1}
                size="large"
                showSearch
                onChange={(value) => handleTaskListSearch(value, 'status')}
              >
                {Array.from(taskStatusMap.entries()).map(([statusKey, innerMap]) => {
                const name = innerMap.get('name');
                // console.info(statusKey,name)
                return (
                    <Option key={statusKey} value={statusKey}>
                        {name}
                    </Option>
                );
            })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item field="task_name">
            <Input 
            placeholder={'任务名称'} 
            // style={{ width: 225 }}
            onChange={(value) => handleTaskListSearch(value, 'task_name')}
            ></Input>
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item field="owner">
            <Input 
            placeholder={'创建人邮箱'} 
            // style={{ width: 225 }}
            onChange={(value) => handleTaskListSearch(value, 'owner')}
            ></Input>
            </Form.Item>
          </Col>
          {/* <Col span={2} >
            <Button type='primary' style={{ float: 'right' }} onClick={() => clickSearch()}>
              查询
            </Button>
          </Col> */}
          <Col span={2} offset={8}>
            <Button type='primary' style={{ float: 'right' }} onClick={() => onClickCreateTask()}>
              创建任务
            </Button>
          </Col>
        </Row>
      </Form>
      <Table
        border={true} // 显示表格外部边框
        borderCell={true} // 显示单元格的内部边框
        hover={false} // 鼠标悬停时显示高亮效果
        stripe={true} // 显示斑马纹效果
        loading={loading} // 显示加载状态
        columns={taskListColumns} // 表格的列配置
        data={taskList} // 表格的数据源
        pagination={taskListPagination} // 分页器
        onChange={onChange}
      />
    </Card>
  );
}
