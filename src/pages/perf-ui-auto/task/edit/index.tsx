import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, Collapse, Divider, Form, Grid, Input, Message, Radio, Select,Tooltip,InputTag,AutoComplete } from '@arco-design/web-react';
import { IconCheckCircleFill } from '@arco-design/web-react/icon';
import { BackendApis2 } from 'src/utils/backendApis2';
import { useUser } from '@devsre/react-utils';
import { useLocation, useNavigate } from 'react-router-dom';
import { get } from 'lodash';

const Option = Select.Option;
const { Row, Col } = Grid;
const CollapseItem = Collapse.Item;
const RadioGroup = Radio.Group;

export default function CurdCard() {
  const navigate = useNavigate();
  const location = useLocation();
  const { email, name } = useUser();

  const [taskForm] = Form.useForm();
  const [appGroupForm] = Form.useForm();

  // 平台类型
  const PLATFORM_ANDROID = 1;
  const PLATFORM_IOS = 2;

  //视频源默认值
  const DEFAULT_VIDEO_URL = 'https://tosv.boe.byted.org/obj/global-rtc-test-platform/test_video/default.mp4';
  const EMPTY_VIDEO_URL = '';

  // 性能采集类型
  const PERF_COLLECT_TYPE_FPS = 1;
  const PERF_COLLECT_TYPE_CPU = 2;
  const PERF_COLLECT_TYPE_GPU = 3;
  const PERF_COLLECT_TYPE_MEM = 4;
  const PERF_COLLECT_TYPE_DISK = 5;
  const PERF_COLLECT_TYPE_NET = 6;
  const PERF_COLLECT_TYPE_BATTERY = 7;
  const PERF_COLLECT_TYPE_FD = 8;
  const PERF_COLLECT_TYPE_THREAD = 9;
  const PERF_COLLECT_TYPE_TEMPERATURE = 10;
  const PERF_COLLECT_TYPE_ENERGY = 11;
  const PERF_COLLECT_TYPE_HITCHES = 12;
  const PERF_COLLECT_TYPE_TIME_PROFILER = 13;
  const PERF_COLLECT_TYPE_SIGNPOST = 14;
  const PERF_COLLECT_TYPE_THREAD_STATE = 15;
  const PERF_COLLECT_TYPE_PRO_INFO = 16;
  const PERF_COLLECT_TYPE_ALL_SYS_MON = 17;
  const perf_collect_type_list = [{
    'id': PERF_COLLECT_TYPE_FPS,
    'name': 'FPS',
  }, {
    'id': PERF_COLLECT_TYPE_CPU,
    'name': 'CPU',
  }, {
    'id': PERF_COLLECT_TYPE_GPU,
    'name': 'GPU',
  }, {
    'id': PERF_COLLECT_TYPE_MEM,
    'name': 'MEM',
  }, {
    'id': PERF_COLLECT_TYPE_DISK,
    'name': 'DISK',
  }, {
    'id': PERF_COLLECT_TYPE_NET,
    'name': 'NET',
  }, {
    'id': PERF_COLLECT_TYPE_BATTERY,
    'name': 'BATTERY',
  }, {
    'id': PERF_COLLECT_TYPE_FD,
    'name': 'FD',
  }, {
    'id': PERF_COLLECT_TYPE_THREAD,
    'name': 'THREAD',
  }, {
    'id': PERF_COLLECT_TYPE_TEMPERATURE,
    'name': 'TEMPERATURE',
  }, {
    'id': PERF_COLLECT_TYPE_ENERGY,
    'name': 'ENERGY',
  }, {
    'id': PERF_COLLECT_TYPE_HITCHES,
    'name': 'HITCHES',
  }, {
    'id': PERF_COLLECT_TYPE_TIME_PROFILER,
    'name': 'TIME_PROFILER',
  }, {
    'id': PERF_COLLECT_TYPE_SIGNPOST,
    'name': 'SIGNPOST',
  }, {
    'id': PERF_COLLECT_TYPE_THREAD_STATE,
    'name': 'THREAD_STATE',
  }, {
    'id': PERF_COLLECT_TYPE_PRO_INFO,
    'name': 'PRO_INFO',
  }, {
    'id': PERF_COLLECT_TYPE_ALL_SYS_MON,
    'name': 'ALL_SYS_MON',
  },
  ];
  // 设备状态类型
  const DEVICE_STATE_DISCONNECTED = 0;
  const DEVICE_STATE_ONLINE = 1;
  const DEVICE_STATE_OFFLINE = 2;
  const device_state_map = {
    [DEVICE_STATE_DISCONNECTED]: '断连',
    [DEVICE_STATE_ONLINE]: '在线',
    [DEVICE_STATE_OFFLINE]: '离线',
  };
  // 客户端状态
  const CLIENT_STATUS_OFFLINE = 0;
  const CLIENT_STATUS_ONLINE = 1;
  const client_state_map = {
    [CLIENT_STATUS_ONLINE]: '在线',
    [CLIENT_STATUS_OFFLINE]: '离线'
  };

  // 账号类型映射
  const account_type_map = [
    {account_type: 1, account_type_name: '性能账号', color: "green"},
    {account_type: 2, account_type_name: '辅助账号', color: "gray"}
  ];
  //页面类型（创建or编辑）
  const DIALOG_TYPE_ADD = 1;
  const DIALOG_TYPE_EDIT = 2;
  const [addOrEdit, setAddOrEdit] = useState(DIALOG_TYPE_ADD);

  // 任务状态常量
  const TASK_STATUS_NOT_EXECUTE = 0;
  const [taskStatus, setTaskStatus] = useState(TASK_STATUS_NOT_EXECUTE);

  // 判断字段是否可编辑的函数
  const isFieldEditable = (fieldType) => {
    // 如果是新建任务，所有字段都可编辑
    if (addOrEdit === DIALOG_TYPE_ADD) {
      return false; // disabled = false，表示可编辑
    }

    // 如果任务状态是未开始，所有字段都可编辑
    if (taskStatus === TASK_STATUS_NOT_EXECUTE) {
      return false; // disabled = false，表示可编辑
    }

    // 如果任务状态不是未开始，根据字段类型判断
    const editableFields = [
      'task_name',           // 任务名称
      'config_id',           // 采集配置
      'subtask_name',        // 子任务名称
      'case_id',             // 测试用例
      'video_url',           // 测试视频源
      'app_install_type',    // App安装方式
      'is_collect_cpu_profile', // 是否采集cpuProfile
      'execute_params'       // 执行参数
    ];

    return !editableFields.includes(fieldType); // 如果在可编辑列表中，返回false（可编辑）
  };
  // 常量
  const [updateCaseLoading, setUpdateCaseLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [businessList, setBusinessList] = useState([]);
  const [configList,setConfigList] = useState([
  ])
  const [clientList, setClientList] = useState([]);
  const [deviceList, setDeviceList] = useState([]);
  const [accountList, setAccountList] = useState([
  ])
  const [appGroupList, setAppGroupList] = useState([]);
  const [videoUrlList, setVideoUrlList] = useState([{
    value: DEFAULT_VIDEO_URL,
    label: '测试视频源',
  },
  {
    value: EMPTY_VIDEO_URL,
    label: '默认不使用测试视频源',
  }

]);
  const [appInstallTypeList, setAppInstallTypeList] = useState([{
    id: 0,
    name: '不安装',
  },{
    id: 1,
    name: '卸载安装',
  }, {
    id: 2,
    name: '覆盖安装',
  }]);
  const [perfCollectMode, setPerfCollectMode] = useState([{
    id: 1,
    name:"有线连接采集",
  },{
    id: 2,
    name:"无线连接采集"
  }]);

// 实验id,默认是-1
  const [experimentId, setExperimentId] = useState(-1);


  const [caseList, setCaseList] = useState([]);
  // const [caseMap, setCaseMap] = useState(
  //   {
  //     [PLATFORM_ANDROID]: [],
  //     [PLATFORM_IOS]: []
  //   })
  const TYPE_1 = 1;
  const TYPE_2 = 2;
  const [taskTypeList, setTaskTypeList] = useState([
    { id: TYPE_1, name: '版本回归' },
    { id: TYPE_2, name: 'libra实验测试' }
  ]);
  const [libraHitTypeList, setLibraHitTypeList] = useState([
    { id: 1, name: 'did命中' },
    { id: 2, name: 'uid命中' }
  ])
  const [isLibraType, setIsLibraType] = useState(false);

  // 性能采集工具类型
  const PERF_TOOL_TYPE_DS = 1;
  const PERF_TOOL_TYPE_GAMEPERF = 2;
  const [perfToolTypeList, setPerfToolTypeList] = useState([
    { id: PERF_TOOL_TYPE_DS, name: 'DS采集工具' },
    { id: PERF_TOOL_TYPE_GAMEPERF, name: 'GamePerf采集工具' }
  ]);
  const [platformList, setPlatformList] = useState([
    { id: PLATFORM_ANDROID, name: 'Android' },
    { id: PLATFORM_IOS, name: 'iOS' },
  ]);
  const [subTaskAutoId, setSubTaskAutoId] = useState(0);
  const [subtaskMap, setSubtaskMap] = useState({
    [subTaskAutoId]: {
      'name': null,
      'platform': null,
      'case_id_list': [],
      // "finish_notice": null,
      "perf_account_id": null,
      'perf_device_id': null,
      'app_group_id': null,
      'video_url': EMPTY_VIDEO_URL,
      'app_install_type': 1,
      'perf_collect_type_list': [1, 2, 3, 4, 5, 6, 7, 11],
      'perf_collect_duration': 300,
      'perf_collect_interval': 1000,
      'perf_device_power_level': 90,
      'case_run_count': 3,
      'case_retry_count': 3,
      'enabled': true,
      'cpuProfile': true,
    },
  });
  // console.log(subtaskMap)
  // console.log(taskForm)

  // 获取业务列表
  const fetchBusinessList = async () => {
    const response = await BackendApis2.fetchBusinessList().finally(() => {
    });
    setBusinessList(response.items);
  };
  const fetchConfigList = async () => {
    const body = {
      'page': 1,
      'page_size': 100
    }
    const response = await BackendApis2.configList({body}).finally(() => {
    });
    setConfigList(response.items);
  };
  // 获取执行机房
  const fetchClientList = async () => {
    const response = await BackendApis2.getClientList().finally(() => {
    });
    setClientList(response.client_items);
  };

  // 获取用例
  const getCaseList = async (business_id) => {
    const payload = {
      'page': 1,
      'page_size': 100,
    };
    const body = {
      'business_id': business_id,
    };
    const response = await BackendApis2.getCaseList({ payload, body: body }).finally(() => {
    });
    setCaseList(response);
  };

  // 更新用例
  const updateCaseList = async () => {
    setUpdateCaseLoading(true);
    const response = await BackendApis2.updateCaseList().finally(() => {
      setUpdateCaseLoading(false);
    });
    Message.info('用例更新完成');
    const business_id = taskForm.getFieldValue('business_id');
    if (business_id > 0) {
      getCaseList(business_id);
    }
  };

  // 获取设备
  const getDeviceList = async (client_id) => {
    const response = await BackendApis2.getDeviceList({
      payload: { client_id: client_id,page:1,page_size:100},
    }).finally(() => {
    });
    setDeviceList(response.items);
  };
  // 获取账号列表
  const getAccountList = async (business_id) => {
    const body = {
      'business_id': business_id,
      'page': 1,
      'page_size': 100
    };
    const response = await BackendApis2.getAccountList({body}).finally(() => {
    });
    setAccountList(response.items);
  };

  // 获取App组
  const fetchAppGroupList = async (business_id,app_group_name=undefined) => {
    const payload = {
      'page': 1,
      'page_size': 100
    };
    const body = {
      business_id: business_id,
      app_group_name:app_group_name

    };
    const response = await BackendApis2.getAppGroupList({ payload, body }).finally(() => {
    });
    // console.log("获取App组,response")
    // console.log(response)

    setAppGroupList(response.items);
    return response;

  };

  // 获取任务详情
  const fetchTaskDetail = async (task_id) => {
    Message.info('任务数据加载中...');
    const response = await BackendApis2.getPerfTaskDetail({ task_id }).finally(() => {
    });
    const taskData = {
      ...response,
    };
    taskForm.setFieldsValue(taskData);
    setTaskStatus(response.status); // 保存任务状态
    setExperimentId(response.experiment_id);
    console.log("response.experiment_id");
    console.log(response.experiment_id)
    // 如果是libra实验，那么就把is_libra_type设置为true
    if (response.type == TYPE_2){
      setIsLibraType(true);
      // 获取到实验id的详情，填充到实验组和对照组的id中
      const experiment_response = await BackendApis2.getExperiment({ payload:{"experiment_id": response.experiment_id }}).finally(() => {
      
      });
        taskForm.setFieldValue('experiment_group_version_ids', experiment_response.experiment_group_version_ids);
        taskForm.setFieldValue('control_group_version_ids', experiment_response.control_group_version_ids);
        taskForm.setFieldValue('hit_type', experiment_response.hit_type)
    }
    else{
      setIsLibraType(false);
    }

    const newSubtaskMap = {};
    let max_index = 0;
    response.sub_tasks.map((item, index) => {
      newSubtaskMap[index] = item;
      if (index > max_index) {
        max_index = index;
      }
    });
    appGroupForm.setFieldValue('app_group_id', response.sub_tasks.length > 0 ? response.sub_tasks[0].app_group_id : null);
    setSubtaskMap(newSubtaskMap);
    setSubTaskAutoId(max_index + 1);
    getCaseList(response.business_id);
    fetchAppGroupList(response.business_id);
    getDeviceList(response.client_id);
    getAccountList(response.business_id);
    setTimeout(() => {
      Message.info('任务数据加载完成');
    }, 2000);
  };

  // 执行任务
  const executeTask = async (task_id) => {
    const payload = { 'task_id': task_id };
    const response = await BackendApis2.executePerfTask({ payload }).finally(() => {
    });
    if (response.code != 200) {
      Message.error('执行失败');
    } else {
      Message.success('开始执行');
      navigate(`/perf-ui-auto/task`);
    }
  };
  // 比较两个数组的值和元素是否相等
  const isArraysEqual = (arr1, arr2) => {
if (arr1.length !== arr2.length) return false;
return arr1.every ((item, index) => item === arr2 [index]);
};

  // 创建任务+子任务
  const createPerfTask = async (is_start_execute = false) => {
    setSaveLoading(true);
    const body = {
      ...taskForm.getFieldsValue(),
      'owner': name,
      sub_tasks: Object.values(subtaskMap),
    };
    // 构建实验body,如果类型是libra实验
    let experiment_id = -1;
    const experiment_body = {
      name:"",
      hit_type: taskForm.getFieldValue('hit_type'),
      creator: name,
      description: '',
      experiment_group_version_ids: taskForm.getFieldValue('experiment_group_version_ids'),
      control_group_version_ids: taskForm.getFieldValue('control_group_version_ids')
    };
    body.sub_tasks.map((item, index) => {
      item['app_group_id'] = appGroupForm.getFieldValue('app_group_id');
    });
    // 获取到实验id
    //  如果是编辑任务,而且新任务是libra类型，之前的实验id不是-1（说明之前就是实验类型），那么需要比对一下实验组和对照组是否有变化，如果有变化，那么就创建新的实验id
    if (addOrEdit == DIALOG_TYPE_EDIT && taskForm.getFieldValue('type')== TYPE_2 && taskForm.getFieldValue('experiment_id')!= -1){
      const experiment_info = await BackendApis2.getExperiment({ payload:{"experiment_id": taskForm.getFieldValue('experiment_id') }}).finally(() => {
      })
      setExperimentId(taskForm.getFieldValue('experiment_id'));
      // 如果实验组和对照组有变化，那么就创建新的实验id
      if ((!isArraysEqual(experiment_info.experiment_group_version_ids, taskForm.getFieldValue('experiment_group_version_ids'))) || (!isArraysEqual(experiment_info.control_group_version_ids,taskForm.getFieldValue('control_group_version_ids')))){
        console.log("实验组和对照组有变化，创建新的实验id")
        console.log(experiment_info.experiment_group_version_ids)
        console.log(taskForm.getFieldValue('experiment_group_version_ids'))
        console.log(experiment_info.control_group_version_ids)
        console.log(taskForm.getFieldValue('control_group_version_ids'))
        const get_experiment = await BackendApis2.createExperiment({ body:experiment_body }).finally(() => {
        });
        experiment_id = get_experiment.data.id;
        setExperimentId(experiment_id);
      }
      // 如果实验组和对照组没有变化，那么就沿用之前的实验id
      else{
        experiment_id = taskForm.getFieldValue('experiment_id');
        setExperimentId(experiment_id);
      }
    }
      //  如果是创建libra任务，或者是编辑任务，之前是版本回归，现在改成libra，而且本次是libra类型，就创建一个新的实验id
    else{
      // 只有本次是libra任务，才会创建新的实验id
      if (isLibraType == true){
      const get_experiment = await BackendApis2.createExperiment({ body:experiment_body }).finally(() => {
      });
    experiment_id = get_experiment.data.id;
    setExperimentId(experiment_id);
    console.log("experiment_id");
    console.log(experiment_id);
    }}
     
    body["experiment_id"] = experiment_id;
    console.log("body");
    console.log(body);
    console.log(appGroupForm.getFieldValue('app_group_id'));
    console.log(appGroupForm.getFieldsValue());
    // TODO：校验数据是否合法
    if (taskForm.getFieldValue('id') != undefined) {  //更新任务
     
      const response = await BackendApis2.updatePerfTask({ body }).finally(() => {
        setSaveLoading(false);
      });
      if (response.code != 200) {
        Message.error('保存失败');
      } else {
        Message.success('保存成功');
        if (is_start_execute == true) {  //触发执行
          setTimeout(() => {
            executeTask(response.data.id);
          }, 2000);
        }
        navigate(`/perf-ui-auto/task`);
      }
    } else {  //创建任务
      const response = await BackendApis2.createPerfTask({ body }).finally(() => {
        setSaveLoading(false);
      });
      if (response.code != 200) {
        Message.error('保存失败');
      } else {
        Message.success('保存成功');
        if (is_start_execute == true) {  //触发执行
          setTimeout(() => {
            executeTask(response.data.id);
          }, 2000);
        }
        navigate(`/perf-ui-auto/task`);
      }
    }
  };

  // 业务id变化
  const onBusinessIdChange = (business_id) => {
    getCaseList(business_id);
    fetchAppGroupList(business_id);
    // 更换业务后要清空所有子任务选中的用例、app组、执行机房
    taskForm.setFieldValue("client_id",undefined)
    setDeviceList([]);
    setAccountList([]);
    appGroupForm.clearFields();
    // 更新账号列表
    getAccountList(business_id);

    onAllSubTaskItemChange({ case_id_list: [], perf_device_id: null});
  };

  // 机房变化
  const onClientIdChange = (client_id) => {
    console.log('client_id');
    console.log(client_id);
    if (client_id != undefined) {
      getDeviceList(client_id);
    } else {
      setDeviceList([]);
    }
    // 更换执行机房后要清空所有子任务的设备
    onAllSubTaskItemChange({ perf_device_id: null });
  };

  // 平台变化
  const onPlatformChange = (value, subTaskIndex) => {
    // 平台更改时清空此子任务的性能设备、用例
    onSubTaskItemChange({ 'platform': value, case_id_list: [], perf_device_id: null }, subTaskIndex);
  };

  // 点击添加子任务
  const onClickAddSubTask = () => {
    // Message.info("添加子任务")
    const newSubtaskMap = {
      ...subtaskMap,
      [subTaskAutoId + 1]: {
        'name': null,
        'platform': null,
        'case_id_list': [],
        // "finish_notice": null,
        "perf_account_id": null,
        'perf_device_id': null,
        'app_group_id': null,
        'video_url': EMPTY_VIDEO_URL,
        'app_install_type': 1,
        'perf_collect_type_list': [1, 2, 3, 4, 5, 6, 7, 11],
        'perf_collect_duration': 300,
        'perf_collect_interval': 1000,
        'perf_device_power_level': 90,
        'case_run_count': 3,
        'case_retry_count': 3,
        'enabled': true,
        'cpuProfile': true,
      },
    };
    console.log(newSubtaskMap);
    setSubtaskMap(newSubtaskMap);
    setSubTaskAutoId(subTaskAutoId + 1);
  };

  // 点击复制子任务
  const onClickCopySubTask = (index) => {
    // Message.info("复制子任务")
    let copySubTask = JSON.parse(JSON.stringify(subtaskMap[index]));
    copySubTask.id = subTaskAutoId + 1;
    const newSubtaskMap = {
      ...subtaskMap,
      [subTaskAutoId + 1]: copySubTask,
    };
    console.log(newSubtaskMap);
    setSubtaskMap(newSubtaskMap);
    setSubTaskAutoId(subTaskAutoId + 1);
  };

  // 点击删除子任务
  const onClickDeleteSubTask = (index) => {
    // Message.info("删除子任务")
    delete subtaskMap[index];
    const newSubtaskMap = { ...subtaskMap };
    console.log(index);
    console.log(newSubtaskMap);
    setSubtaskMap(newSubtaskMap);
  };

  // 所有子任务的值发生变化
  const onAllSubTaskItemChange = (json) => {
    const newSubtaskMap = { ...subtaskMap };
    Object.keys(newSubtaskMap).forEach(index => {
      const newSubtaskItem = { ...newSubtaskMap[index] };
      Object.keys(json).forEach(field => {
        newSubtaskItem[field] = json[field];
      });
      newSubtaskMap[index] = newSubtaskItem;
    });
    setSubtaskMap(newSubtaskMap);
  };

  // 子任务的值发生变化
  const onSubTaskItemChange = (json, index) => {
    console.log('onSubTaskItemChange');
    const newSubtaskMap = { ...subtaskMap };
    Object.keys(json).forEach(field => {
      newSubtaskMap[index][field] = json[field];
    });
    setSubtaskMap(newSubtaskMap);
  };
  // APP 组的名称发生变化

  const onAppGroupNameChange = (app_group_name) => {
    console.log('app_group_name');
    console.log(app_group_name);
    // appGroupForm.clearFields();  
    fetchAppGroupList(taskForm.getFieldValue('business_id'),app_group_name);  
    appGroupForm.clearFields();
    appGroupForm.setFieldValue('app_group_id', undefined);
    onAllSubTaskItemChange({ app_group_id: null });
  };



  // 点击保存
  const onClickSave = () => {
    // Message.info("保存")
    createPerfTask(false);
  };



  // 点击更新用例
  const onClickUpdateCases = () => {
    Message.info('开始更新用例');
    updateCaseList();
  };

  // 点击添加账号
  const onClickAddAccount = () => {
    Message.info('添加账号');
  };

  // 点击添加App组
  const onClickAddAppGroup = () => {
    // Message.info("添加App组")
    window.open(`/perf-ui-auto/app`, '_blank');
  };

  // 点击添加采集配置
  const onClickAddConfig = () => {
    // Message.info("添加采集配置")
    window.open(`/perf-ui-auto/config`, '_blank');
  };

  // 处理测试用例选择变化（包含全选逻辑）
  const handleCaseSelectionChange = (value, subTaskIndex) => {
    const SELECT_ALL_KEY = 'SELECT_ALL_CASES';

    // 获取当前平台的所有可用用例
    const availableCases = subtaskMap[subTaskIndex].platform != null ?
      caseList.filter(caseItem =>
        JSON.parse('[' + caseItem.platform + ']').some((platformItem: any) =>
          platformItem === subtaskMap[subTaskIndex].platform
        )
      ) : [];

    // 检查是否点击了全选
    if (value.includes(SELECT_ALL_KEY)) {
      // 全选：选择当前平台的所有用例
      const allCaseIds = availableCases.map(item => item.case_id);
      onSubTaskItemChange({ 'case_id_list': allCaseIds }, subTaskIndex);
    } else {
      // 正常选择：过滤掉全选标识，保留实际的用例ID
      const filteredValue = value.filter(id => id !== SELECT_ALL_KEY);
      onSubTaskItemChange({ 'case_id_list': filteredValue }, subTaskIndex);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchBusinessList();
    fetchConfigList();
    fetchClientList();
    if (location.state) {
      console.log('location.state');
      console.log(location.state);
      fetchTaskDetail(location.state.data.task_id);
      setAddOrEdit(DIALOG_TYPE_EDIT);
    }
  }, []);

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Card bordered={false} style={{ marginBottom: '24px' }}>
        {/* 页面标题和操作按钮 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px',
          padding: '16px 0',
          borderBottom: '1px solid #e8e8e8'
        }}>
          <h2 style={{ margin: 0, fontSize: '20px', fontWeight: 600 }}>
            {addOrEdit === DIALOG_TYPE_ADD ? '创建性能测试任务' : '编辑性能测试任务'}
          </h2>
          <div style={{ display: 'flex', gap: '12px' }}>
            <Button
              disabled={saveLoading}
              type="primary"
              size="large"
              loading={saveLoading}
              onClick={() => onClickSave()}
              style={{
                background: saveLoading
                  ? 'linear-gradient(135deg, #a0a0a0 0%, #808080 100%)'
                  : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: '700',
                padding: '14px 32px',
                height: '48px',
                minWidth: '120px',
                boxShadow: saveLoading
                  ? '0 4px 15px rgba(0, 0, 0, 0.1)'
                  : '0 8px 25px rgba(102, 126, 234, 0.4)',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                transform: 'translateY(0px)',
                position: 'relative',
                overflow: 'hidden',
                color: '#ffffff',
                letterSpacing: '0.5px'
              }}
              onMouseEnter={(e) => {
                if (!saveLoading) {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 12px 35px rgba(102, 126, 234, 0.5)';
                }
              }}
              onMouseLeave={(e) => {
                if (!saveLoading) {
                  e.currentTarget.style.transform = 'translateY(0px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)';
                }
              }}
            >
              {saveLoading ? '保存中...' : '确认保存'}
            </Button>
          </div>
        </div>

        <Divider orientation={'left'} style={{ fontSize: '16px', fontWeight: 500 }}>任务信息</Divider>
        <Form
          form={taskForm}
          layout="vertical"
          style={{ width: '100%' }}
        >
          <Form.Item field="id" hidden>
            <Input placeholder={'id'}></Input>
          </Form.Item>
          <Form.Item field="owner" hidden>
            <Input placeholder={'owner'}></Input>
          </Form.Item>
          <Form.Item field="status" hidden>
            <Input placeholder={'status'}></Input>
          </Form.Item>
          <Form.Item field="result" hidden>
            <Input placeholder={'result'}></Input>
          </Form.Item>
          <Form.Item field="duration" hidden>
            <Input placeholder={'duration'}></Input>
          </Form.Item>

          <Row gutter={24}>
            <Col span={1}>
            </Col>
            <Col span={4}>
              <Form.Item label="任务名称" field="name" required>
                <Input
                  placeholder="请输入任务名称"
                  disabled={isFieldEditable('task_name')}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="业务" field="business_id" required>
                <Select
                  placeholder="请选择业务"
                  allowClear
                  showSearch
                  disabled={isFieldEditable('business_id')}
                  onChange={(value) => onBusinessIdChange(value)}
                >
                  {businessList.map((item) => (
                    <Option key={item.id} value={item.id}>{item.business_name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="执行机房" field="client_id" required>
                <Select
                  placeholder="请选择执行机房"
                  allowClear
                  showSearch
                  disabled={isFieldEditable('client_id')}
                  onChange={(value) => onClientIdChange(value)}
                >
                  {clientList.filter(item => item.business_id == taskForm.getFieldValue('business_id') && item.state == 1).map((item) => (
                    <Option key={item.id} value={item.id}>
                      <Tooltip content={`${item.name}-${client_state_map[item.state]}`}>
                        {`${item.name}-${client_state_map[item.state]}`}
                      </Tooltip>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="任务类型" field="type" initialValue={1} required>
                <Select
                  placeholder="请选择任务类型"
                  allowClear
                  disabled={isFieldEditable('type')}
                  onChange={(value) => {
                    if (value == 2) {
                      setIsLibraType(true);
                    } else {
                      setIsLibraType(false);
                    }
                  }}
                >
                  {taskTypeList.map((item) => (
                    <Option key={item.id} value={item.id}>{item.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="性能采集工具" field="perf_tool_type" initialValue={1} required>
                <Select
                  placeholder="请选择性能采集工具"
                  allowClear
                  disabled={isFieldEditable('perf_tool_type')}
                >
                  {perfToolTypeList.map((item) => (
                    <Option key={item.id} value={item.id}>{item.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          {/* Libra实验配置 */}
          {isLibraType && (
            <Row gutter={24}>
              <Col span={1}>
            </Col>
              <Col span={6}>
                <Form.Item label="实验命中" field="hit_type" initialValue={1} required>
                  <Select
                    placeholder="请选择命中方式"
                    allowClear
                    showSearch
                  >
                    {libraHitTypeList.map((item) => (
                      <Option key={item.id} value={item.id}>{item.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="实验组" field="experiment_group_version_ids" required>
                  <InputTag placeholder="实验组id 回车分隔" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="对照组" field="control_group_version_ids" required>
                  <InputTag placeholder="对照组id 回车分隔" />
                </Form.Item>
              </Col>
            </Row>
          )}
        </Form>
      </Card>

      {/* 配置管理区域 */}
      <Card bordered={false} style={{ marginBottom: '24px' }}>
        <Divider orientation={'left'} style={{ fontSize: '16px', fontWeight: 500 }}>配置管理</Divider>
          <Row gutter={24} align="end" style={{ marginBottom: '16px' }}>
        {/* 采集配置选择 */}
        <Col span={1}>
            </Col>
            <Col span={6}>
            <Form form={taskForm} layout="vertical">
              <Form.Item label="采集配置" field="config_id" required style={{ marginBottom: 0 }}>
                <Select
                  placeholder="请选择采集配置"
                  allowClear
                  showSearch
                  disabled={isFieldEditable('config_id')}
                  filterOption={(input, option) => {
                    return option.props.children.toString().toLowerCase().includes(input.toLowerCase());
                  }}
                >
                  {configList.filter(item => (item.business_id == taskForm.getFieldValue('business_id'))).map((item) => (
                    <Option key={item.id} value={item.id}>
                      <Tooltip content={`${item.config_name}`}>{`${item.config_name}`}</Tooltip>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
            </Col>
            <Col span={3}>
              <Button type="primary" onClick={() => onClickAddConfig()}>
                添加采集配置
              </Button>
            </Col>  
       
        <Col span={6}>
         <Form form={appGroupForm} layout="vertical">
                <Form.Item label="App组" field="app_group_id" required style={{ marginBottom: 0 }}>
                  <Select
                    placeholder="请先选择业务，再选择App组"
                    allowClear
                    showSearch
                    disabled={isFieldEditable('app_group_id')}
                  >
                    {appGroupList.map((item) => (
                    <Option key={item.id} value={item.id}>
                      <Tooltip content={`${item.name}-${item.version}`}>
                        <span>{item.name}-{item.version}</span>
                      </Tooltip>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
        </Form>
        </Col>
        <Col span={2}>
              <Button type="primary" onClick={() => onClickAddAppGroup()}>
                添加App组
              </Button>
            </Col>
       </Row>
      </Card>

      {/* 子任务信息区域 */}
      <Card bordered={false}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 500 }}>子任务信息</h3>
          <Button type="primary" onClick={() => onClickAddSubTask()}>
            添加子任务
          </Button>
        </div>
        <Collapse
          expandIconPosition="right"
          triggerRegion="icon"
          defaultActiveKey={['0']}
          style={{
            backgroundColor: '#fafafa',
            border: '1px solid #e8e8e8',
            borderRadius: '8px'
          }}
        >
          {Object.keys(subtaskMap).map(subTaskIndex => (
            <CollapseItem
              key={subTaskIndex}
              name={subTaskIndex.toString()}
              header={
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '24px',
                  padding: '8px 0'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                    <span style={{ fontWeight: 500, minWidth: '80px' }}>
                      子任务名称:
                      <span style={{ color: '#f53f3f', marginLeft: '2px' }}>*</span>
                    </span>
                    <Input
                      placeholder="请输入子任务名称"
                      style={{ width: '200px' }}
                      disabled={isFieldEditable('subtask_name')}
                      onChange={(value) => onSubTaskItemChange({ 'name': value }, subTaskIndex)}
                      defaultValue={subtaskMap[subTaskIndex].name}
                      value={subtaskMap[subTaskIndex].name}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                    <span style={{ fontWeight: 500, minWidth: '40px' }}>平台:</span>
                    <Select
                      placeholder="请选择平台"
                      allowClear
                      style={{ width: '150px' }}
                      disabled={isFieldEditable('platform')}
                      onChange={(value) => onPlatformChange(value, subTaskIndex)}
                      defaultValue={subtaskMap[subTaskIndex].platform}
                      value={subtaskMap[subTaskIndex].platform}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {platformList.map((item) => (
                        <Option key={item.id} value={item.id}>{item.name}</Option>
                      ))}
                    </Select>
                  </div>
                </div>
              }
              extra={
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Button
                    type="text"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      onClickCopySubTask(subTaskIndex);
                    }}
                  >
                    复制
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      onClickDeleteSubTask(subTaskIndex);
                    }}
                  >
                    删除
                  </Button>
                </div>
              }
            >
              <div style={{ padding: '24px', backgroundColor: '#fff' }}>
                <Form layout="vertical">
                  {/* 用例选择区域 */}
                  <Row gutter={24} style={{ marginBottom: '24px' }} align="end">
                    <Col span={20}>
                      <Form.Item label="测试用例" required style={{ marginBottom: 0 }}>
                        <Select
                          placeholder="请先选择业务和平台，再选择用例"
                          allowClear
                          mode="multiple"
                          showSearch
                          maxTagCount="responsive"
                          disabled={isFieldEditable('case_id')}
                          defaultValue={subtaskMap[subTaskIndex].case_id_list}
                          onChange={(value) => handleCaseSelectionChange(value, subTaskIndex)}
                          value={subtaskMap[subTaskIndex].case_id_list}
                          triggerProps={{
                            autoAlignPopupWidth: false,
                            autoAlignPopupMinWidth: true,
                            position: 'bl'
                          }}
                          dropdownMenuStyle={{
                            maxHeight: '400px'
                          }}
                          renderTag={(props) => {
                            // 自定义已选择标签的显示，只显示case_name
                            const { value, closable, onClose } = props;
                            const caseItem = caseList.find(item => item.case_id === value);
                            const displayText = caseItem?.case_name || `用例${value}`;

                            return (
                              <span
                                style={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  padding: '4px 8px',
                                  margin: '2px 4px 2px 0',
                                  backgroundColor: '#f7f8fa',
                                  color: '#1d2129',
                                  borderRadius: '6px',
                                  fontSize: '12px',
                                  fontWeight: 400,
                                  border: '1px solid #e5e6eb',
                                  maxWidth: '200px',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap'
                                }}
                              >
                                {displayText}
                                {closable && (
                                  <span
                                    style={{
                                      marginLeft: '6px',
                                      cursor: 'pointer',
                                      fontSize: '12px',
                                      color: '#86909c',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: '14px',
                                      height: '14px',
                                      borderRadius: '50%',
                                      backgroundColor: 'transparent',
                                      transition: 'all 0.2s'
                                    }}
                                    onClick={onClose}
                                    onMouseEnter={(e) => {
                                      e.currentTarget.style.backgroundColor = '#e5e6eb';
                                      e.currentTarget.style.color = '#1d2129';
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.backgroundColor = 'transparent';
                                      e.currentTarget.style.color = '#86909c';
                                    }}
                                  >
                                    ×
                                  </span>
                                )}
                              </span>
                            );
                          }}
                          filterOption={(inputValue, option) => {
                            // 自定义搜索逻辑，支持按title、case_name、owner搜索
                            const optionProps = option.props || option;
                            const item = caseList.find(caseItem => caseItem.case_id === optionProps.value);
                            if (!item) return false;

                            const searchText = inputValue.toLowerCase();
                            return (
                              (item.title && item.title.toLowerCase().includes(searchText)) ||
                              (item.case_name && item.case_name.toLowerCase().includes(searchText)) ||
                              (item.owner && item.owner.toLowerCase().includes(searchText))
                            );
                          }}
                          dropdownRender={(menu) => {
                            const availableCases = subtaskMap[subTaskIndex].platform != null ?
                              caseList.filter(caseItem =>
                                JSON.parse('[' + caseItem.platform + ']').some((platformItem: any) =>
                                  platformItem === subtaskMap[subTaskIndex].platform
                                )
                              ) : [];

                            return (
                              <div>
                                {availableCases.length > 0 && (
                                  <>
                                    <div
                                      style={{
                                        padding: '8px 12px',
                                        backgroundColor: '#F7F8FA',
                                        borderBottom: '1px solid #E5E6EB',
                                        cursor: 'pointer',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px',
                                        fontSize: '14px',
                                        fontWeight: 500,
                                        color: '#165DFF'
                                      }}
                                      onClick={() => {
                                        const allCaseIds = availableCases.map(item => item.case_id);
                                        onSubTaskItemChange({ 'case_id_list': allCaseIds }, subTaskIndex);
                                      }}
                                      onMouseEnter={(e) => {
                                        e.currentTarget.style.backgroundColor = '#E8F3FF';
                                      }}
                                      onMouseLeave={(e) => {
                                        e.currentTarget.style.backgroundColor = '#F7F8FA';
                                      }}
                                    >
                                      <IconCheckCircleFill style={{ color: '#00B42A', fontSize: '16px' }} />
                                      <span>全选用例</span>
                                      <span style={{
                                        padding: '2px 8px',
                                        backgroundColor: '#E8F3FF',
                                        color: '#165DFF',
                                        borderRadius: '12px',
                                        fontSize: '12px',
                                        fontWeight: 500
                                      }}>
                                        {availableCases.length}个
                                      </span>
                                    </div>
                                  </>
                                )}
                                {menu}
                              </div>
                            );
                          }}
                        >
                          {subtaskMap[subTaskIndex].platform != null ?
                            caseList.filter(caseItem =>
                              JSON.parse('[' + caseItem.platform + ']').some((platformItem: any) =>
                                platformItem === subtaskMap[subTaskIndex].platform
                              )
                            ).map((item) => (
                              <Option key={item.case_id} value={item.case_id}>
                                <div style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  gap: '4px',
                                  padding: '4px 0'
                                }}>
                                  <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    fontSize: '14px',
                                    fontWeight: 500,
                                    color: '#1D2129'
                                  }}>
                                    <span style={{
                                      fontSize: '16px',
                                      fontWeight: 600,
                                      color: '#165DFF'
                                    }}>
                                      {item.title}
                                    </span>
                                    {item.case_name && (
                                      <span style={{
                                        padding: '2px 8px',
                                        backgroundColor: '#F0F9FF',
                                        color: '#165DFF',
                                        borderRadius: '12px',
                                        fontSize: '12px',
                                        fontWeight: 500
                                      }}>
                                        {item.case_name}
                                      </span>
                                    )}
                                    {item.device_count && (
                                      <span style={{
                                        padding: '2px 8px',
                                        backgroundColor: '#E8F7E8',
                                        color: '#00B42A',
                                        borderRadius: '12px',
                                        fontSize: '12px',
                                        fontWeight: 500
                                      }}>
                                        {item.device_count}台设备
                                      </span>
                                    )}
                                  </div>
                                  <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '12px',
                                    fontSize: '12px',
                                    color: '#86909C'
                                  }}>
                                    {item.dir && (
                                      <span style={{
                                        fontFamily: 'Monaco, Consolas, monospace'
                                      }}>
                                        📁 {item.dir}
                                      </span>
                                    )}
                                    {item.owner && (
                                      <span>
                                        👤 {item.owner}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </Option>
                            )) : null
                          }
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Button
                        type="primary"
                        loading={updateCaseLoading}
                        onClick={() => onClickUpdateCases()}
                      >
                        更新用例
                      </Button>
                    </Col>
                  </Row>
              {/* <Row style={{ marginLeft: -32 }}>
                <Col span={20}>
                  <Form.Item label="性能采集设备" required>
                    <Select
                      placeholder={'请先选择机房和平台，再选择性能采集设备'}
                      // options={businessList}
                      allowClear
                      maxTagCount={1}
                      size="large"
                      showSearch
                      key={subtaskMap[subTaskIndex]}
                      defaultValue={subtaskMap[subTaskIndex].perf_device_id}
                      onChange={(value) => onSubTaskItemChange({ 'perf_device_id': value }, subTaskIndex)}
                      value={subtaskMap[subTaskIndex].perf_device_id}

                    >
                      {deviceList.filter(item => item.sys_type == subtaskMap[subTaskIndex].platform&&item.state==1).map((item, index) => (
                        <Option key={item.id}
                                value={item.id}> {item.name}-{item.brand}-{item.udid}-{item.is_occupied == 0 ? '空闲' : '占用中'}-{device_state_map[item.state]} </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row> */}
              {/* <Row>
                <Col span={20}>
                  <Form.Item label="App组" required>
                    <Select
                      placeholder={'请先选择业务，再选择App组'}
                      // options={businessList}
                      allowClear
                      maxTagCount={1}
                      size="large"
                      showSearch
                      key={subTaskIndex}
                      defaultValue={subtaskMap[subTaskIndex].app_group_id}
                      onChange={(value) => onSubTaskItemChange(value, 'app_group_id', subTaskIndex)}
                    >
                      {appGroupList.map((item, index) => (
                        <Option key={item.id} value={item.id}> {item.name}-{item.version} </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Button type='primary' onClick={() => onClickAddAppGroup()} style={{ marginLeft: 30 }}>
                    添加App组
                  </Button>
                </Col>
              </Row> */}
              {/* <Row>
                <Col span={20}>
                  <Form.Item label="账号" required>
                    <Select
                      placeholder={'请选择账号'}
                      // options={businessList}
                      allowClear
                      maxTagCount={1}
                      size="large"
                      showSearch
                      disabled
                      key={subtaskMap[subTaskIndex]}
                      defaultValue={0}
                      onChange={(value) => onSubTaskItemChange({ 'account_id': value }, subTaskIndex)}
                      value={subtaskMap[subTaskIndex].account_id}
                    >
                      <Option key={0} value={0}> {'版本回归默认账号组'} </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Button type='primary' onClick={() => onClickAddAccount()} disabled style={{ marginLeft: 30 }}>
                    添加账号
                  </Button>
                </Col>
              </Row> */}
                  {/* 配置区域 - 两列布局 */}
                  <Row gutter={24}>
                    <Col span={12}>
                      <div style={{
                        padding: '16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '6px',
                        marginBottom: '16px'
                      }}>
                        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 500 }}>基础配置</h4>

                        <Form.Item label="测试视频源"  style={{ marginBottom: '16px' }}>
                          <AutoComplete
                           // placeholder 显示value对应的label值
                            placeholder={videoUrlList.find(item => item.value == subtaskMap[subTaskIndex].video_url)?.label}
                            allowClear
                            disabled={isFieldEditable('video_url')}
                            // 同时
                            // defaultActiveValue={subtaskMap[subTaskIndex].video_url}
                            // defaultValue={subtaskMap[subTaskIndex].video_url}
                            onSelect={(value) => onSubTaskItemChange({ 'video_url': value }, subTaskIndex)}
                            onChange={(value) => onSubTaskItemChange({ 'video_url': value }, subTaskIndex)}
                            value={subtaskMap[subTaskIndex].video_url}
                          >
                            {videoUrlList.map((item) => (
                              <Option key={item.value} value={item.value}>{item.label}</Option>
                            ))}
                          </AutoComplete>
                        </Form.Item>

                        <Form.Item label="性能采集方式" required style={{ marginBottom: '16px' }}>
                          <Select
                            placeholder="请选择性能采集方式"
                            allowClear
                            showSearch
                            defaultValue={subtaskMap[subTaskIndex].perf_collect_mode}
                            onChange={(value) => onSubTaskItemChange({ 'perf_collect_mode': value }, subTaskIndex)}
                            value={subtaskMap[subTaskIndex].perf_collect_mode}
                          >
                            {perfCollectMode.map((item) => (
                              <Option key={item.id} value={item.id}>{item.name}</Option>
                            ))}
                          </Select>
                        </Form.Item>

                        <Form.Item label="性能采集设备" required style={{ marginBottom: '16px' }}>
                          <Select
                            placeholder="请先选择机房和平台"
                            allowClear
                            showSearch
                            disabled={isFieldEditable('perf_device_id')}
                            defaultValue={subtaskMap[subTaskIndex].perf_device_id}
                            onChange={(value) => onSubTaskItemChange({ 'perf_device_id': value }, subTaskIndex)}
                            value={subtaskMap[subTaskIndex].perf_device_id}
                            triggerProps={{
                              autoAlignPopupWidth: false,
                              autoAlignPopupMinWidth: true,
                              position: 'bl'
                            }}
                            dropdownMenuStyle={{
                              maxHeight: '300px'
                            }}
                          >
                            {deviceList.filter(item =>
                              item.sys_type == subtaskMap[subTaskIndex].platform &&
                              item.state == 1 &&
                              ((subtaskMap[subTaskIndex].perf_collect_mode == 1) || (item.serial_port != null))
                            ).map((item) => (
                              <Option key={item.id} value={item.id}>
                                <div style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  gap: '4px',
                                  padding: '4px 0'
                                }}>
                                  <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    fontSize: '14px',
                                    fontWeight: 500,
                                    color: '#1D2129'
                                  }}>
                                    <span style={{
                                      fontSize: '16px',
                                      fontWeight: 600,
                                      color: '#165DFF'
                                    }}>
                                      {item.name}
                                    </span>
                                    <span style={{
                                      padding: '2px 8px',
                                      backgroundColor: '#E8F3FF',
                                      color: '#165DFF',
                                      borderRadius: '12px',
                                      fontSize: '12px',
                                      fontWeight: 500
                                    }}>
                                      {item.brand}
                                    </span>
                                    <span style={{
                                      padding: '2px 8px',
                                      backgroundColor: item.is_occupied == 0 ? '#E8F7E8' : '#FFF2E8',
                                      color: item.is_occupied == 0 ? '#00B42A' : '#FF7D00',
                                      borderRadius: '12px',
                                      fontSize: '12px',
                                      fontWeight: 500
                                    }}>
                                      {item.is_occupied == 0 ? '空闲' : '占用中'}
                                    </span>
                                  </div>
                                  <div style={{
                                    fontSize: '12px',
                                    color: '#86909C',
                                    fontFamily: 'Monaco, Consolas, monospace'
                                  }}>
                                    UDID: {item.udid}
                                  </div>
                                </div>
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>

                        <Form.Item label="性能采集账号" style={{ marginBottom: '16px' }}>
                          <Select
                            placeholder="需要先选择业务"
                            allowClear
                            showSearch
                            disabled={isFieldEditable('perf_account_id')}
                            defaultValue={subtaskMap[subTaskIndex].perf_account_id}
                            onChange={(value) => onSubTaskItemChange({ 'perf_account_id': value }, subTaskIndex)}
                            value={subtaskMap[subTaskIndex].perf_account_id}
                            triggerProps={{
                              autoAlignPopupWidth: false,
                              autoAlignPopupMinWidth: true,
                              position: 'bl'
                            }}
                            dropdownMenuStyle={{
                              maxHeight: '300px'
                            }}
                          >
                            {accountList.filter(item => (item.is_occupied == 0) && (item.account_type == 1)).map((item) => (
                              <Option key={item.id} value={item.id}>
                                <div style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  gap: '4px',
                                  padding: '4px 0'
                                }}>
                                  <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    fontSize: '14px',
                                    fontWeight: 500,
                                    color: '#1D2129'
                                  }}>
                                    <span style={{
                                      fontSize: '16px',
                                      fontWeight: 600,
                                      color: '#165DFF'
                                    }}>
                                      {item.username}
                                    </span>
                                    <span style={{
                                      padding: '2px 8px',
                                      backgroundColor: '#F0F9FF',
                                      color: '#165DFF',
                                      borderRadius: '12px',
                                      fontSize: '12px',
                                      fontWeight: 500,
                                      fontFamily: 'Monaco, Consolas, monospace'
                                    }}>
                                      {item.iphone}
                                    </span>
                                    <span style={{
                                      padding: '2px 8px',
                                      backgroundColor: account_type_map.find(type => type.account_type === item.account_type)?.color === 'green' ? '#E8F7E8' : '#F5F5F5',
                                      color: account_type_map.find(type => type.account_type === item.account_type)?.color === 'green' ? '#00B42A' : '#86909C',
                                      borderRadius: '12px',
                                      fontSize: '12px',
                                      fontWeight: 500
                                    }}>
                                      {account_type_map.find(type => type.account_type === item.account_type)?.account_type_name || '未知类型'}
                                    </span>
                                    <span style={{
                                      padding: '2px 8px',
                                      backgroundColor: item.is_occupied == 0 ? '#E8F7E8' : '#FFF2E8',
                                      color: item.is_occupied == 0 ? '#00B42A' : '#FF7D00',
                                      borderRadius: '12px',
                                      fontSize: '12px',
                                      fontWeight: 500
                                    }}>
                                      {item.is_occupied == 0 ? '空闲' : '占用中'}
                                    </span>
                                  </div>
                                  <div style={{
                                    fontSize: '12px',
                                    color: '#86909C',
                                    fontFamily: 'Monaco, Consolas, monospace'
                                  }}>
                                    UID: {item.uid}
                                  </div>
                                </div>
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>

                        <Form.Item label="App安装方式" required style={{ marginBottom: '16px' }}>
                          <Select
                            placeholder="请选择app安装方式"
                            allowClear
                            showSearch
                            disabled={isFieldEditable('app_install_type')}
                            defaultValue={subtaskMap[subTaskIndex].app_install_type}
                            onChange={(value) => onSubTaskItemChange({ 'app_install_type': value }, subTaskIndex)}
                            value={subtaskMap[subTaskIndex].app_install_type}
                          >
                            {appInstallTypeList.map((item) => (
                              <Option key={item.id} value={item.id}>{item.name}</Option>
                            ))}
                          </Select>
                        </Form.Item>

                        <Form.Item label="采集cpuProfile" required style={{ marginBottom: '0' }}>
                          <RadioGroup
                            defaultValue={subtaskMap[subTaskIndex].cpuProfile}
                            disabled={isFieldEditable('is_collect_cpu_profile')}
                            onChange={(value) => onSubTaskItemChange({'cpuProfile':value}, subTaskIndex)}
                          >
                            <Radio value={true}>是</Radio>
                            <Radio value={false}>否</Radio>
                          </RadioGroup>
                        </Form.Item>
                      </div>
                    </Col>
                  {/* <Row>
                    <Col span={24}>
                      <Form.Item label="子任务完成通知" disabled labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                        <Select
                          placeholder={'功能暂不支持'}
                          // options={businessList}
                          allowClear
                          maxTagCount={1}
                          size="large"
                          showSearch
                          key={subTaskIndex}
                          defaultValue={subtaskMap[subTaskIndex].finish_notice}
                          onChange={(value) => onSubTaskItemChange({'finish_notice':value}, subTaskIndex)}
                        >
                          {businessList.map((item, index) => (
                            <Option key={item.id} value={item.id}> {item.business_name} </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={24}>
                      <Form.Item label="子任务是否启用" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                        <RadioGroup
                          style={{ marginBottom: 20 }}
                          disabled
                          key={subTaskIndex}
                          defaultValue={subtaskMap[subTaskIndex].enabled}
                          onChange={(value) => onSubTaskItemChange({'enabled':value}, subTaskIndex)}
                        >
                          <Radio value={true}>是</Radio>
                          <Radio value={false}>否</Radio>
                        </RadioGroup>
                      </Form.Item>
                    </Col>
                  </Row> */}
                    <Col span={12}>
                      <div style={{
                        padding: '16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '6px',
                        marginBottom: '16px'
                      }}>
                        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 500 }}>执行参数</h4>

                        <Form.Item label="执行用例最低电量" required style={{ marginBottom: '16px' }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <Input
                              placeholder="执行用例最低电量"
                              disabled={isFieldEditable('execute_params')}
                              defaultValue={subtaskMap[subTaskIndex].perf_device_power_level}
                              onChange={(value) => onSubTaskItemChange({ 'perf_device_power_level': value }, subTaskIndex)}
                              value={subtaskMap[subTaskIndex].perf_device_power_level}
                              style={{ flex: 1 }}
                            />
                            <span>%</span>
                          </div>
                        </Form.Item>

                        <Form.Item label="单条用例执行次数" required style={{ marginBottom: '16px' }}>
                          <Input
                            placeholder="单条用例执行次数"
                            disabled={isFieldEditable('execute_params')}
                            defaultValue={subtaskMap[subTaskIndex].case_run_count}
                            onChange={(value) => onSubTaskItemChange({ 'case_run_count': value }, subTaskIndex)}
                            value={subtaskMap[subTaskIndex].case_run_count}
                          />
                        </Form.Item>

                        <Form.Item label="用例失败重试次数" required style={{ marginBottom: '16px' }}>
                          <Input
                            placeholder="用例失败重试次数"
                            disabled={isFieldEditable('execute_params')}
                            defaultValue={subtaskMap[subTaskIndex].case_retry_count}
                            onChange={(value) => onSubTaskItemChange({ 'case_retry_count': value }, subTaskIndex)}
                            value={subtaskMap[subTaskIndex].case_retry_count}
                          />
                        </Form.Item>

                        <Form.Item label="性能采集等待时间" required style={{ marginBottom: '16px' }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <Input
                              placeholder="性能采集等待时间"
                              disabled={isFieldEditable('execute_params')}
                              defaultValue={subtaskMap[subTaskIndex].perf_collect_duration}
                              onChange={(value) => onSubTaskItemChange({ 'perf_collect_duration': value }, subTaskIndex)}
                              value={subtaskMap[subTaskIndex].perf_collect_duration}
                              style={{ flex: 1 }}
                            />
                            <span>秒</span>
                          </div>
                        </Form.Item>

                        <Form.Item label="性能采集时间间隔" required style={{ marginBottom: '0' }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <Input
                              placeholder="性能采集时间间隔"
                              disabled={isFieldEditable('execute_params')}
                              defaultValue={subtaskMap[subTaskIndex].perf_collect_interval}
                              onChange={(value) => onSubTaskItemChange({ 'perf_collect_interval': value }, subTaskIndex)}
                              value={subtaskMap[subTaskIndex].perf_collect_interval}
                              style={{ flex: 1 }}
                            />
                            <span>毫秒</span>
                          </div>
                        </Form.Item>
                      </div>
                    </Col>
                  </Row>
                </Form>
              </div>
          </CollapseItem>
        ))}
      </Collapse>
      </Card>
    </div>
  );
}
