import React, { useState, useEffect } from 'react';
import {
  Card,
  Grid,
  Button,
  Modal,
  Message,
  Divider,
  Form,
  Select,
  Table,
  Collapse,
  Radio,
  Link,
  Tooltip,
  Tag,
  Image,
  Space,
  Typography
} from '@arco-design/web-react';
import Builder from '@devsre/builder';
import { BackendApis2 } from 'src/utils/backendApis2';
import config from './config.json';
import { useUser } from '@devsre/react-utils';
import { useNavigate, useLocation } from 'react-router-dom';
import{developer_list,version_type_map,TASK_TYPE_2,libraHitTypeMap, constants} from'src/utils/const';
import{IconArrowLeft} from '@arco-design/web-react/icon';
import { getLogUrls, getPerfDataUrls } from 'src/utils/urlFormatUtils';
import { LogLinksDisplay, PerfDataLinksDisplay } from 'src/components/UrlLinksDisplay';

const { Title } = Typography;

const Option = Select.Option;
const { Row, Col } = Grid;
const CollapseItem = Collapse.Item;
const RadioGroup = Radio.Group;

export default function CurdCard() {
  //页面跳转相关
  const navigate = useNavigate();
  const location = useLocation();

  //---url参数相关---
  const query = new URLSearchParams(location.search);
  // 处理查询参数名
  const QUERY_TASK_ID = 'task_id'
  // 处理查询参数值
  var queryTaskId = query.get(QUERY_TASK_ID);

  //用户信息相关
  const { email, name } = useUser();

  const [taskForm] = Form.useForm();
  const [appGroupForm] = Form.useForm();
  const defaultPagination = {
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 10,
    current: 1,
    pageSizeChangeResetCurrent: true,
    sizeOptions: [10, 20, 50, 100, 200],
  };
  const [paginationMap, setPaginationMap] = useState({});
  const [tableLoadingMap, setTableLoadingMap] = useState({});
  const [caseRunDetailMap, setCaseRunDetailMap] = useState({});
  // 错误弹窗是否显示,错误弹窗的内容
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalData, setModalData] = useState({
    title: '为空',
    content: '为空',
  });

  

  // 平台类型
  const PLATFORM_ANDROID = 1;
  const PLATFORM_IOS = 2;
  const platform_type_map = {
    [PLATFORM_ANDROID]: 'Android',
    [PLATFORM_IOS]: 'iOS',
  };
  //视频源默认值
  const DEFAULT_VIDEO_URL = 'https://tosv.boe.byted.org/obj/global-rtc-test-platform/test_video/default.mp4';

  // 性能采集类型
  const PERF_COLLECT_TYPE_FPS = 1;
  const PERF_COLLECT_TYPE_CPU = 2;
  const PERF_COLLECT_TYPE_GPU = 3;
  const PERF_COLLECT_TYPE_MEM = 4;
  const PERF_COLLECT_TYPE_DISK = 5;
  const PERF_COLLECT_TYPE_NET = 6;
  const PERF_COLLECT_TYPE_BATTERY = 7;
  const PERF_COLLECT_TYPE_FD = 8;
  const PERF_COLLECT_TYPE_THREAD = 9;
  const PERF_COLLECT_TYPE_TEMPERATURE = 10;
  const PERF_COLLECT_TYPE_ENERGY = 11;
  const PERF_COLLECT_TYPE_HITCHES = 12;
  const PERF_COLLECT_TYPE_TIME_PROFILER = 13;
  const PERF_COLLECT_TYPE_SIGNPOST = 14;
  const PERF_COLLECT_TYPE_THREAD_STATE = 15;
  const PERF_COLLECT_TYPE_PRO_INFO = 16;
  const PERF_COLLECT_TYPE_ALL_SYS_MON = 17;
  const perf_collect_type_map = {
    [PERF_COLLECT_TYPE_FPS]: 'FPS',
    [PERF_COLLECT_TYPE_CPU]: 'CPU',
    [PERF_COLLECT_TYPE_GPU]: 'GPU',
    [PERF_COLLECT_TYPE_MEM]: 'MEM',
    [PERF_COLLECT_TYPE_DISK]: 'DISK',
    [PERF_COLLECT_TYPE_NET]: 'NET',
    [PERF_COLLECT_TYPE_BATTERY]: 'BATTERY',
    [PERF_COLLECT_TYPE_FD]: 'FD',
    [PERF_COLLECT_TYPE_THREAD]: 'THREAD',
    [PERF_COLLECT_TYPE_TEMPERATURE]: 'TEMPERATURE',
    [PERF_COLLECT_TYPE_ENERGY]: 'ENERGY',
    [PERF_COLLECT_TYPE_HITCHES]: 'HITCHES',
    [PERF_COLLECT_TYPE_TIME_PROFILER]: 'TIME_PROFILER',
    [PERF_COLLECT_TYPE_SIGNPOST]: 'SIGNPOST',
    [PERF_COLLECT_TYPE_THREAD_STATE]: 'THREAD_STATE',
    [PERF_COLLECT_TYPE_PRO_INFO]: 'PRO_INFO',
    [PERF_COLLECT_TYPE_ALL_SYS_MON]: 'ALL_SYS_MON',
  };

  // 设备状态类型
  const DEVICE_STATE_DISCONNECTED = 0;
  const DEVICE_STATE_ONLINE = 1;
  const DEVICE_STATE_OFFLINE = 2;
  const device_state_map = {
    [DEVICE_STATE_DISCONNECTED]: '断连',
    [DEVICE_STATE_ONLINE]: '在线',
    [DEVICE_STATE_OFFLINE]: '离线',
  };
  // 任务类型分类
  const TASK_TYPE_VERSION = 1;
  const TASK_TYPE_SPECIAL = 2;
  const task_type_map: Map<number, Map<string, string>> = new Map([
    [TASK_TYPE_VERSION, new Map([['name', '版本回归'], ['color', 'purple']])],
    [TASK_TYPE_SPECIAL, new Map([['name', 'libra实验'], ['color', 'blue']])],
  ]);

  // 任务状态分类
  const TASK_STATUS_NOT_EXECUTE = 0
  const TASK_STATUS_WAITING = 1
  const TASK_STATUS_RUNNING = 2
  const TASK_STATUS_SUCCESS = 3
  const TASK_STATUS_ERROR = 4
  const TASK_STATUS_RETRY_PENDING = 5  
  const TASK_STATUS_RETRY_RUNNING = 6 
  const TASK_STATUS_CANCELED = 7 
  const TASK_STATUS_TIMEOUT = 8 
  const task_state_map: Map<number, Map<string, string>> = new Map([
    [TASK_STATUS_NOT_EXECUTE, new Map([['name', '未开始'], ['color', 'gray']])],
    [TASK_STATUS_WAITING, new Map([['name', '等待中'], ['color', 'gray']])],
    [TASK_STATUS_RUNNING, new Map([['name', '执行中'], ['color', 'blue']])],
    [TASK_STATUS_SUCCESS, new Map([['name', '执行成功'], ['color', 'green']])],
    [TASK_STATUS_ERROR, new Map([['name', '执行失败'], ['color', 'red']])],
    [TASK_STATUS_RETRY_PENDING, new Map([['name', '重试等待中'], ['color', 'orange']])],
    [TASK_STATUS_RETRY_RUNNING, new Map([['name', '重试执行中'], ['color', 'orange']])],
    [TASK_STATUS_CANCELED, new Map([['name', '已取消'], ['color', 'gray']])],
    [TASK_STATUS_TIMEOUT, new Map([['name', '已超时'], ['color', 'orange']])],
  ]);

  //子任务状态类型
  const SUB_TASK_STATUS_NOT_START = 0;
  const SUB_TASK_STATUS_PENDING = 1;
  const SUB_TASK_STATUS_RUNNING = 2;
  const SUB_TASK_STATUS_SUCCESS = 3;
  const SUB_TASK_STATUS_FAILED = 4;
  const SUB_TASK_STATUS_RETRY_PENDING = 5  
  const SUB_TASK_STATUS_RETRY_RUNNING = 6 
  const SUB_TASK_STATUS_CANCELED = 7 
  const SUB_TASK_STATUS_TIMEOUT = 8 
  const sub_task_state_map: Map<number, Map<string, string>> = new Map([
    [SUB_TASK_STATUS_NOT_START, new Map([['name', '未开始'], ['color', 'gray']])],
    [SUB_TASK_STATUS_PENDING, new Map([['name', '等待中'], ['color', 'gray']])],
    [SUB_TASK_STATUS_RUNNING, new Map([['name', '执行中'], ['color', 'blue']])],
    [SUB_TASK_STATUS_SUCCESS, new Map([['name', '执行成功'], ['color', 'green']])],
    [SUB_TASK_STATUS_FAILED, new Map([['name', '执行失败'], ['color', 'red']])],
    [SUB_TASK_STATUS_RETRY_PENDING, new Map([['name', '重试等待中'], ['color', 'orange']])],
    [SUB_TASK_STATUS_RETRY_RUNNING, new Map([['name', '重试执行中'], ['color', 'orange']])],
    [SUB_TASK_STATUS_CANCELED, new Map([['name', '已取消'], ['color', 'gray']])],
    [SUB_TASK_STATUS_TIMEOUT, new Map([['name', '已超时'], ['color', 'red']])],

  ]);

  // 性能采集工具类型
    const perfToolTypeList= [
      { id: constants.PERF_TOOL_TYPE_DS, name: 'DS采集工具' },
      { id: constants.PERF_TOOL_TYPE_GAMEPERF, name: 'GamePerf采集工具' }
    ];

  // 用例状态类型
  const CASE_STATUS_PENDING = 0;
  const CASE_STATUS_RUNNING = 1;
  const CASE_STATUS_RETRYING = 2;
  const CASE_STATUS_FINISHED = 3;
  const CASE_STATUS_FAILED = 4;
  const CASE_STATUS_CANCELLED = 5;
  const CASE_STATUS_ERROR = 6;
  const case_state_map: Map<number, Map<string, string>> = new Map([
    [CASE_STATUS_PENDING, new Map([['name', '等待执行'], ['color', 'gray']])],
    [CASE_STATUS_RUNNING, new Map([['name', '执行中'], ['color', 'blue']])],
    [CASE_STATUS_RETRYING, new Map([['name', '重试中'], ['color', 'orange']])],
    [CASE_STATUS_FINISHED, new Map([['name', '执行完成'], ['color', 'green']])],
    [CASE_STATUS_FAILED, new Map([['name', '执行失败'], ['color', 'red']])],
    [CASE_STATUS_CANCELLED, new Map([['name', '已取消'], ['color', 'gray']])],
    [CASE_STATUS_ERROR, new Map([['name', '错误'], ['color', 'red']])],
  ]);

  // app安装方式
  const APP_INSTALL_TYPE_UNINTALL = 1;
  const APP_INSTALL_TYPE_COVER = 2;
  const app_install_map = {
    [APP_INSTALL_TYPE_UNINTALL]: '卸载安装',
    [APP_INSTALL_TYPE_COVER]: '覆盖安装',
  };
  const PERF_COLLECT_MODE_WIRE = 1;
  const PERF_COLLECT_MODE_WIRELESS = 2;
  const perf_collect_mode_map = {
    [PERF_COLLECT_MODE_WIRE]: '有线连接采集',
    [PERF_COLLECT_MODE_WIRELESS]: '无线连接采集',
  };

  // 客户端状态
  const CLIENT_STATUS_ONLINE = 1;
  // 常量
  const [updateCaseLoading, setUpdateCaseLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [taskDetail, setTaskDetail] = useState({
    'id': null,
    'name': null,
    'owner': null,
    'type': null,
    'status': null,
    'result': null,
    'duration': null,
    'start_time': null,
    'end_time': null,
    'sub_tasks': [],
    'create_time': null,
    'update_time': null,
    'perf_tool_type': null,
    'experiments_name': null,
    'control_name': null,
    'hit_type': null,
    'business': {
      'id': null,
      'business_name': null,
      'create_time': null,
      'update_time': null,
    },
    'client': {
      'id': null,
      'business_id': null,
      'name': null,
      'sys_type': null,
      'mac_address': null,
      'ipv4': null,
      'ipv6': null,
      'state': null,
      'port': null,
      'owner': null,
    },
    'config': null,
    'experiment': null,
    'app_group': {
      'id': null,
      'business_id': null,
      'name': null,
      'version': null,
      'android_perf_app_id_list': null,
      'android_assist_app_id_list': null,
      'ios_perf_app_id_list': null,
      'ios_assist_app_id_list': null,
      'create_time': null,
      'update_time': null,
    },
  });
  const [businessList, setBusinessList] = useState([]);
  const [clientList, setClientList] = useState([]);
  const [deviceList, setDeviceList] = useState([]);
  const [appGroupList, setAppGroupList] = useState([]);
  const [videoUrlList, setVideoUrlList] = useState([{
    id: DEFAULT_VIDEO_URL,
    name: '默认测试视频源',
  }]);


  const [caseMap, setCaseMap] = useState(
    {
      [PLATFORM_ANDROID]: [],
      [PLATFORM_IOS]: [],
    });
  const [taskTypeList, setTaskTypeList] = useState([
    { id: 1, name: '版本回归' },
    // { id: 2, name: '专项测试' }
  ]);
  const [platformList, setPlatformList] = useState([
    { id: PLATFORM_ANDROID, name: 'Android' },
    { id: PLATFORM_IOS, name: 'iOS' },
  ]);
  const [subTaskAutoId, setSubTaskAutoId] = useState(0);
  const [subtaskMap, setSubtaskMap] = useState({
    [subTaskAutoId]: {
      'name': null,
      'platform': null,
      'case_id_list': [],
      // "account_id": 0,
      'perf_device_id': null,
      'app_group_id': null,
      'video_url': DEFAULT_VIDEO_URL,
      'app_install_type': 0,
      'perf_collect_type_list': [1, 2, 3, 4, 5, 6, 7, 11],
      'perf_collect_duration': 300,
      'perf_collect_interval': 1000,
      'perf_device_power_level': 90,
      'case_run_count': 3,
      'case_retry_count': 3,
      'enabled': true,
    },
  });
  // console.log(subtaskMap)
  // console.log(taskForm)

  // 截图相关逻辑 ——————————————————————————————————————————————————————————————————
  // 是否展示截图弹窗
  const [screenshotVisible,setScreenshotVisible] = useState(false);
  // 截图链接相关
  const [screenshotTosUrls, setScreenshotTosUrls] = useState([
  // "https://cloud-page.bytedance.net/platform/api/v1/user/avatar/yinyanting.2022?format=40x40.png"
  
  ]);
// 当前展示的大图链接，初始为列表中的第一张图片
  const [currentLargeImage, setCurrentLargeImage] = useState(screenshotTosUrls[0]);

  // 处理小图点击事件，更新当前展示的大图链接
  const handleSmallImageClick = (imageUrl) => {
      setCurrentLargeImage(imageUrl);
  };

//  截图逻辑结束————————————————————————————————————————————————————————————————————————

  // 获取业务列表
  const fetchBusinessList = async () => {
    const response = await BackendApis2.fetchBusinessList().finally(() => {
    });
    setBusinessList(response.items);
  };

  // 获取执行机房
  const fetchClientList = async () => {
    const response = await BackendApis2.getClientList().finally(() => {
    });
    setClientList(response.client_items);
  };

  // 获取用例
  const getCaseList = async (business_id) => {
    const payload = {
      'page': 1,
      'page_size': 100,
    };
    const androidBody = {
      'business_id': business_id,
      'platform': PLATFORM_ANDROID,
    };
    const iosBody = {
      'business_id': business_id,
      'platform': PLATFORM_IOS,
    };
    const androidResponse = await BackendApis2.getCaseList({ payload, body: androidBody }).finally(() => {
    });
    const iosResponse = await BackendApis2.getCaseList({ payload, body: iosBody }).finally(() => {
    });
    const result = {
      [PLATFORM_ANDROID]: androidResponse,
      [PLATFORM_IOS]: iosResponse,
    };
    console.log('getCaseList');
    console.log(androidResponse);
    console.log(iosResponse);
    console.log(result);
    setCaseMap(result);
  };

  // 更新用例
  const updateCaseList = async () => {
    setUpdateCaseLoading(true);
    const response = await BackendApis2.updateCaseList().finally(() => {
      setUpdateCaseLoading(false);
    });
    Message.info('用例更新完成');
    const business_id = taskForm.getFieldValue('business_id');
    if (business_id > 0) {
      getCaseList(business_id);
    }
  };

  // 获取设备
  const getDeviceList = async (client_id) => {
    const response = await BackendApis2.getDeviceList({
      payload: { client_id: client_id },
    }).finally(() => {
    });
    setDeviceList(response.items);
  };

  // 获取App组
  const fetchAppGroupList = async (business_id) => {
    const payload = {
      'page': 1,
      'page_size': 100,
    };
    const body = {
      business_id: business_id,
    };
    const response = await BackendApis2.getAppGroupList({ payload, body }).finally(() => {
    });
    setAppGroupList(response.items);
  };

  // 获取任务详情
  const fetchTaskDetail = async (task_id) => {
    Message.info('任务数据加载中...');
    const response = await BackendApis2.getPerfTaskDetailAll({ task_id }).finally(() => {
    });

    if (response.code!=200) {
      Message.error("任务id不存在或数据查询失败")
    }
    else {
      let result = response.data;
     
      // 如果是libra实验任务，并且有实验信息，那么就获取实验的详细信息
      if (response.data.type === TASK_TYPE_2 && response.data.experiment) {
        // 现在experiment对象已经包含在响应中，不需要额外请求
        const experiments = response.data.experiment;
        // 下面是根据vid获取名称的逻辑，暂时不用
        // let experiments_name = "";
        // const experiments_flights = await BackendApis2.viewFlightByVid({ body: { "vids": experiments.experiment_group_version_ids } }).finally(() => {});
        // for (let i = 0; i < experiments_flights.length; i++) {
        //   if ( experiments_flights[i]!=null){
        // experiments_name += experiments_flights[i].flight.name + "; ";
        //   }
        //   else{
        //     experiments_name += "实验组名称获取失败；";
        //   }
        // }
        // let control_name = "";
        // const control_flights = await BackendApis2.viewFlightByVid({ body: { "vids": experiments.control_group_version_ids } }).finally(() => {});
        // for (let i = 0; i < control_flights.length; i++) {
        //   if(control_flights[i]!=null){
        // control_name += control_flights[i].flight.name + "; ";
        //   }
        //   else{
        //     control_name += "对照组名称获取失败；";
        //   }
        // }
        result =  {
          ...response.data,
          experiments_info: experiments,
          // 暂时不用experiments_name
          // experiments_name: experiments_name,
          experiments_name: experiments.experiment_group_version_ids,
          // 暂时不用control_name
          // control_name: control_name,
          control_name: experiments.control_group_version_ids,
          hit_type: experiments.hit_type,
        }
      }
      setTaskDetail(result);
      Message.info('任务数据加载完成');
    }
  };

  // 获取用例执行结果详情
  const fetchCaseRunDetail = async (pagination, sub_task_id) => {
    console.log(pagination);

    const payload = {
      page: pagination.current,
      page_size: pagination.pageSize,
      sub_task_id: sub_task_id,
    };
    tableLoadingMap[sub_task_id] = true;
    setTableLoadingMap((JSON.parse(JSON.stringify(tableLoadingMap))));
    const response = await BackendApis2.getPerfSubTaskCaseRunDetail({ payload }).finally(() => {
      tableLoadingMap[sub_task_id] = false;
      setTableLoadingMap((JSON.parse(JSON.stringify(tableLoadingMap))));
    });
    caseRunDetailMap[sub_task_id] = response.items;
    setCaseRunDetailMap(JSON.parse(JSON.stringify(caseRunDetailMap)));
    pagination.current = response.page;
    pagination.pageSize = response.page_size;
    pagination.total = response.total;
    paginationMap[sub_task_id] = pagination;
    setPaginationMap(JSON.parse(JSON.stringify(paginationMap)));
    console.log('fetchCaseRunDetail sub_task_id=' + sub_task_id);
    console.log(caseRunDetailMap);
    console.log(paginationMap);
  };

  // 创建任务+子任务
  const createPerfTask = async () => {
    setSaveLoading(true);
    const body = {
      'owner': name,
      ...taskForm.getFieldsValue(),
      sub_tasks: Object.values(subtaskMap),
    };
    body.sub_tasks.map((item, index) => {
      item['app_group_id'] = appGroupForm.getFieldValue('app_group_id');
    });
    console.log(body);
    console.log(appGroupForm.getFieldValue('app_group_id'));
    console.log(appGroupForm.getFieldsValue());
    if (taskForm.getFieldValue('id') != undefined) {  //更新任务
      const response = await BackendApis2.updatePerfTask({ body }).finally(() => {
        setSaveLoading(false);
      });
      if (response.code != 200) {
        Message.error('保存失败');
      } else {
        Message.success('保存成功');
        navigate(`/perf-ui-auto/task`);
      }
    } else {  //创建任务
      const response = await BackendApis2.createPerfTask({ body }).finally(() => {
        setSaveLoading(false);
      });
      if (response.code != 200) {
        Message.error('保存失败');
      } else {
        Message.success('保存成功');
        navigate(`/perf-ui-auto/task`);
      }
    }
  };

  // 业务id变化
  const onBusinessIdChange = (business_id) => {
    getCaseList(business_id);
    fetchAppGroupList(business_id);
    // 更换业务后要清空所有子任务选中的用例、app组
    appGroupForm.clearFields();
    onAllSubTaskItemChange({ case_id_list: [] });
  };

  // 机房变化
  const onClientIdChange = (client_id) => {
    getDeviceList(client_id);
    // 更换执行机房后要清空所有子任务的设备
    onAllSubTaskItemChange({ perf_device_id: null });
  };

  // 平台变化
  const onPlatformChange = (value, subTaskIndex) => {
    // 平台更改时清空此子任务的性能设备、用例
    onSubTaskItemChange({ 'platform': value, case_id_list: [], perf_device_id: null }, subTaskIndex);
  };

  // 点击添加子任务
  const onClickAddSubTask = () => {
    // Message.info("添加子任务")
    const newSubtaskMap = {
      ...subtaskMap,
      [subTaskAutoId + 1]: {
        'name': null,
        'platform': null,
        'case_id_list': [],
        // "account_id": 0,
        'perf_device_id': null,
        'app_group_id': null,
        'video_url': DEFAULT_VIDEO_URL,
        'app_install_type': 0,
        'perf_collect_type_list': [],
        'perf_collect_duration': 300,
        'perf_collect_interval': 1000,
        'perf_device_power_level': 90,
        'case_run_count': 3,
        'case_retry_count': 3,
        'enabled': true,
      },
    };
    console.log(newSubtaskMap);
    setSubtaskMap(newSubtaskMap);
    setSubTaskAutoId(subTaskAutoId + 1);
  };

  // 点击复制子任务
  const onClickCopySubTask = (index) => {
    // Message.info("复制子任务")
    let copySubTask = JSON.parse(JSON.stringify(subtaskMap[index]));
    copySubTask.id = subTaskAutoId + 1;
    const newSubtaskMap = {
      ...subtaskMap,
      [subTaskAutoId + 1]: copySubTask,
    };
    console.log(newSubtaskMap);
    setSubtaskMap(newSubtaskMap);
    setSubTaskAutoId(subTaskAutoId + 1);
  };

  // 点击删除子任务
  const onClickDeleteSubTask = (index) => {
    // Message.info("删除子任务")
    delete subtaskMap[index];
    const newSubtaskMap = { ...subtaskMap };
    console.log(index);
    console.log(newSubtaskMap);
    setSubtaskMap(newSubtaskMap);
  };

  // 所有子任务的值发生变化
  const onAllSubTaskItemChange = (json) => {
    const newSubtaskMap = { ...subtaskMap };
    Object.keys(newSubtaskMap).forEach(index => {
      const newSubtaskItem = { ...newSubtaskMap[index] };
      Object.keys(json).forEach(field => {
        newSubtaskItem[field] = json[field];
      });
      newSubtaskMap[index] = newSubtaskItem;
    });
    setSubtaskMap(newSubtaskMap);
  };

  // 子任务的值发生变化
  const onSubTaskItemChange = (json, index) => {
    console.log('onSubTaskItemChange');
    const newSubtaskMap = { ...subtaskMap };
    Object.keys(json).forEach(field => {
      newSubtaskMap[index][field] = json[field];
    });
    setSubtaskMap(newSubtaskMap);
  };

  // 点击查看任务报告
  const onClickViewReport = () => {
    // Message.info("返回")
    // navigate(`/perf-ui-auto/task`);

    navigate (`/perf-ui-auto/report?task_id=${queryTaskId}&type=${taskDetail.type}&task_name=${taskDetail.name}&perf_tool_type=${taskDetail.perf_tool_type}&config_id=${taskDetail.config?.id}`);
  };
// 回退到上个页面的逻辑
const handleBack = () => {
  navigate(-1);
}

  // // 点击保存
  // const onClickSave = () => {
  //   // Message.info("保存")
  //   createPerfTask()
  // };

  // // 点击保存并执行
  // const onClickSaveAndExecute = () => {
  //   Message.info("保存了但还不会执行")
  //   createPerfTask()
  // };

  // 点击更新用例
  const onClickUpdateCases = () => {
    Message.info('开始更新用例');
    updateCaseList();
  };

  // 点击添加账号
  const onClickAddAccount = () => {
    Message.info('添加账号');
  };

  // 点击添加App组
  const onClickAddAppGroup = () => {
    // Message.info("添加App组")
    window.open(`/perf-ui-auto/app`, '_blank');
  };
  // 设置弹窗不可见
  const setModalInvisible = () => {
    setIsModalVisible(false);
};
// 设置弹窗可见
  const setModalVisible = () => {
    setIsModalVisible(true);
  }
  // 设置弹窗的信息
  const get_error_info = (error_title,error_content) => {
    setModalData({
      title: error_title,
      content: error_content
    }) 
  }
  // 点击进入用例的截图详情
  const onShowCaseDetail = (record) => {
    if(record['screenshot_tos_urls']==null || record['screenshot_tos_urls'].length==0){
      Message.info('无截图')
    }
    else{
      // navigate(`/perf-ui-auto/task/case_detail?case_id=${record['case_id']}`, { state: { data: { case_id: record['case_id'] ,screenshot_tos_urls:record['screenshot_tos_urls']} } });
      setScreenshotTosUrls(record['screenshot_tos_urls']);
      setCurrentLargeImage(record['screenshot_tos_urls'][0]);
      setScreenshotVisible(true);
    }
  };

    // 判断字段是否可见
    const judageVisible = (field) => {
      if(taskDetail.type===1){
        // 如果是版本回归任务，
        if (field == 'version_type') {
        return false;
      }
      if (field =='app') {
        return true;
      }
      }
      if(taskDetail.type===2){
        // 如果是专项测试任务，
        if (field =='version_type') {
          return true;
        }
        if (field =='app') {
          return false;
        }
      }
      return false;
      
    }
// 点击错误码详情，显示弹窗，并阻止冒泡
  const onClickErrorDetail = (e,error_title,error_content) => {
    get_error_info(error_title,error_content);
    e.stopPropagation();
    setModalVisible();
  }
// 点击跳转到APP组
  const onClickAPPGroup = (name) => {
    // Message.info("编辑")
    navigate(`/perf-ui-auto/app`, { state: { data: { app_group_name: name } } });
  };
  // 点击跳转到机房的设备列表
  const onClickDevice = (client_id) => {
    // Message.info("编辑")
    navigate(`/perf-ui-auto/client/device`, { state: { client_id } });
  };
  // 点击跳转到配置页面
  const onClickConfig = (config_name) => {
    // Message.info("编辑")
    navigate(`/perf-ui-auto/config`, { state: { config_name: config_name } });
  };
//   const handleClick = () => {
// // 在新标签页中打开链接
// window.open ('https://www.example.com', '_blank', 'noopener noreferrer');
// };


  // taskDetail变化时刷新用例执行结果
  useEffect(() => {
    console.log('taskDetail[sub_tasks] change');
    console.log(taskDetail['sub_tasks']);
    taskDetail['sub_tasks'].map((subTaskItem, subTaskIndex) => {
      const sub_task_id = subTaskItem['id'];
      const pagination = sub_task_id in paginationMap ? paginationMap[sub_task_id] : JSON.parse(JSON.stringify(defaultPagination));
      fetchCaseRunDetail(pagination, sub_task_id);
    });
  }, [taskDetail]);

  // 初始化数据
  useEffect(() => {
    // fetchBusinessList();
    // fetchClientList();
    console.log('queryTaskId');
    console.log(queryTaskId);
    // 不是null并且是正整数
    if (queryTaskId != null && /^\d+$/.test(queryTaskId)) {
      fetchTaskDetail(queryTaskId);
    } else {
      Message.warning('任务id不存在或非法');
    }
  }, []);

  // 用例执行结果列表column格式
  const caseRunDetailColumns = [
    // { title: '执行id', dataIndex: 'id', width: '5%', ellipsis: true },
    { title: '用例名称', dataIndex: 'case["case_name"]', width: 200, fixed: 'left' as const, align: 'center' as const,
        visible:true,
      render: (col, record, index) => (
        <Tooltip mini content={record["case"]["case_name"]}>
          {record["case"]["case_name"]}
        </Tooltip>
        ),
    },
    // { title: '用例标题', dataIndex: 'case["title"]', width: '8%', ellipsis: true },
    {
      title: '应用', dataIndex: 'app["name"]', width: 200, align: 'center' as const,
      visible:judageVisible('app'),
      render: (col, record, index) => (
        <Tooltip mini content={record["app"]["name"]}>
           <Link href={record["app"]["url"]} target="_blank"> {record["app"]["name"]}</Link>
      </Tooltip>
      ),
    },
      {
      title: '实验组别',
      dataIndex: 'version_type',
      fixed: 'left' as const,
      width: 100,
      align: 'center' as const,
      visible: judageVisible('version_type'),
       render: (col, record, index) => (
      //  <Tooltip mini content={record["app"]["name"]}>
      <div>
      {version_type_map.get(record["version_type"])}
      </div>

     // </Tooltip>
      ),
    },
    // {
    //   title: '设备', dataIndex: 'device', width: 200,align: 'center',
    //   render: (col, record, index) => (
    //     <Tooltip mini content={<span>{record["device"]["name"]} - {record["device"]["udid"]}</span>}>
    //       {record["device"]["name"]} - {record["device"]["udid"]}
    //   </Tooltip>
    //   ),
    // },
    {
      title: '执行状态', dataIndex: 'status', width: 100, align: 'center' as const,
              visible:true,
      render: (col, record, index) => (
        <Tag
          color={case_state_map?.get(record.status)?.get('color')}>{case_state_map?.get(record.status)?.get('name')}</Tag>
      ),
    },
    {
      title: '执行日志', dataIndex: 'case_log_tos_urls', width: 120, align: 'center' as const,
              visible:true,
      render: (col, record, index) => {
        const logUrls = getLogUrls(record);
        return <LogLinksDisplay urls={logUrls} maxDisplay={2} />;
      },
    },
    // 只有开发者能看到

    {
      title: '执行截图', dataIndex: 'operation',  width: 100, ellipsis: true, align: 'center' as const, visible:true,
      render: (col, record, index) => (
        <div  style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>
          {record['screenshot_tos_urls']!=null && record['screenshot_tos_urls'].length>0 ?
          <Button
          type="text"
          // icon={<IconEdit />}
          onClick={() => onShowCaseDetail(record)}>
          点击查看
        </Button>
        :"暂无"}
        </div>
      )
    },
    {
      title: '性能数据', dataIndex: 'perf_data_tos_urls', width: 120, align: 'center' as const, visible:true,
      render: (col, record, index) => {
        const perfUrls = getPerfDataUrls(record);
        return <PerfDataLinksDisplay urls={perfUrls} maxDisplay={2} />;
      },
    },
    {
      title:'CPUprofile文件',dataIndex:'cpu_profile_tos_url',width: 100, align: 'center' as const, visible:true,
      render: (col, record, index) => (
        <span>{record['cpu_profile_tos_url'] != null ?
          <Link href={record['cpu_profile_tos_url']} target="_blank"> 下载文件</Link> : <span />}</span>
      ),
    }
    ,
    { title: '开始时间', dataIndex: 'create_time', width: 150, align: 'center' as const, visible:true,},
    { title: '结束时间', dataIndex: 'update_time', width: 150, align: 'center' as const, visible:true,},
  ];

  return (
    <Card bordered={false}>
           <Row>
                  <Button style={{ float: 'left', width: '60x' }}
                   type="text"  
                  icon={<IconArrowLeft />}
                  onClick={() => handleBack()}
                  >
                      返回
                    </Button>
            </Row>
       <div>
       {isModalVisible && (
                // <div className={overlayStyle}>
                //     <div className="modal-content">
                //         <h2>这是一个弹窗</h2>
                //         <p>这是弹窗的内容。</p>
                //         {/* 创建确定和取消按钮，点击时分别调用 handleOk 和 handleCancel 函数关闭弹窗 */}
                //         <button onClick={setModalInvisible}>关闭</button>
                //     </div>
                // </div>
                <Modal
                title="报错信息"
                visible={isModalVisible}
                onCancel={setModalInvisible}
                footer={null} // 隐藏默认的底部按钮
                // centered
                // 确保弹窗居中显示
            >
                <p>错误标题：{modalData.title}</p>
                <p>错误详情：{modalData.content}</p>
            </Modal>
            )}
       </div>

      <Row gutter={20}>
        <Col offset={22} span={2}>
      {/* 只有当任务状态为进行中时不能点击查看报告按钮 */}
          {taskDetail?.status == constants.TASK_STATUS_SUCCESS ?
          <Button style={{ marginRight: 20 }} type="outline" onClick={() => onClickViewReport()}>
            查看报告
          </Button>
          :
          null
          }
        </Col>
      </Row>
      <Divider orientation={'left'} style={{
        fontSize: '16px',
        fontWeight: 600,
        color: '#1D2129',
        marginBottom: '24px'
      }}>
        任务信息
      </Divider>

      <Card
        bordered={true}
        style={{
          borderRadius: '6px',
          boxShadow: '0 1px 4px rgba(0, 0, 0, 0.04)',
          marginBottom: '16px'
        }}
        bodyStyle={{ padding: '16px' }}
      >
        {/* 基础信息区域 - 一行6列紧凑布局 */}
        <div style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 12]}>
            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  任务名称
                </span>
                <span style={{
                  fontSize: '15px',
                  color: '#1D2129',
                  fontWeight: 500,
                  wordBreak: 'break-all'
                }}>
                  {taskDetail?.name || '-'}
                </span>
              </div>
            </Col>

            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  任务ID
                </span>
                <span style={{
                  fontSize: '14px',
                  color: '#1D2129',
                  fontWeight: 500,
                  fontFamily: 'Monaco, Consolas, monospace'
                }}>
                  {taskDetail?.id || '-'}
                </span>
              </div>
            </Col>

            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  任务类型
                </span>
                <div>
                  <Tag
                    color={task_type_map?.get(taskDetail.type)?.get('color')}
                    style={{
                      borderRadius: '4px',
                      fontSize: '13px',
                      fontWeight: 500,
                      border: 'none'
                    }}
                  >
                    {task_type_map?.get(taskDetail.type)?.get('name') || '-'}
                  </Tag>
                </div>
              </div>
            </Col>

            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  任务状态
                </span>
                <div>
                  <Tag
                    color={task_state_map?.get(taskDetail.status)?.get('color')}
                    style={{
                      borderRadius: '4px',
                      fontSize: '13px',
                      fontWeight: 500,
                      border: 'none'
                    }}
                  >
                    {task_state_map?.get(taskDetail.status)?.get('name') || '-'}
                  </Tag>
                </div>
              </div>
            </Col>

            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  业务
                </span>
                <span style={{
                  fontSize: '15px',
                  color: '#1D2129',
                  fontWeight: 500
                }}>
                  {taskDetail?.business?.business_name || '-'}
                </span>
              </div>
            </Col>

            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  执行机房
                </span>
                <div>
                  <Button
                    type="text"
                    size="small"
                    onClick={() => onClickDevice(taskDetail?.client?.id)}
                    style={{
                      padding: '0',
                      height: 'auto',
                      fontSize: '15px',
                      color: '#165DFF',
                      fontWeight: 500,
                      textDecoration: 'none'
                    }}
                    onMouseEnter={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'underline';
                    }}
                    onMouseLeave={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'none';
                    }}
                  >
                    {taskDetail?.client?.name || '-'}
                  </Button>
                </div>
              </div>
            </Col>
          </Row>

          {/* 第二行：App组、性能配置、采集方式等 */}
          <Row gutter={[16, 12]} style={{ marginTop: '12px' }}>
            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  App组
                </span>
                <div>
                  <Button
                    type="text"
                    size="small"
                    onClick={() => onClickAPPGroup(taskDetail?.app_group?.name)}
                    style={{
                      padding: '0',
                      height: 'auto',
                      fontSize: '15px',
                      color: '#165DFF',
                      fontWeight: 500,
                      textDecoration: 'none'
                    }}
                    onMouseEnter={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'underline';
                    }}
                    onMouseLeave={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'none';
                    }}
                  >
                    {taskDetail?.app_group?.name || '-'}
                  </Button>
                </div>
              </div>
            </Col>

            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  性能配置
                </span>
                 <div>
                  <Button
                    type="text"
                    size="small"
                    onClick={() => onClickConfig(taskDetail?.config?.config_name)}
                    style={{
                      padding: '0',
                      height: 'auto',
                      fontSize: '15px',
                      color: '#165DFF',
                      fontWeight: 500,
                      textDecoration: 'none'
                    }}
                    onMouseEnter={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'underline';
                    }}
                    onMouseLeave={(e) => {
                      (e.target as HTMLElement).style.textDecoration = 'none';
                    }}
                  >
                    {taskDetail?.config?.config_name || '-'}
                  </Button>
                </div>
              </div>
            </Col>

            <Col xs={24} sm={12} md={8} lg={4}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <span style={{
                  fontSize: '13px',
                  color: '#86909C',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  采集方式
                </span>
                <span style={{
                  fontSize: '15px',
                  color: '#1D2129',
                  fontWeight: 500
                }}>
                  {perfToolTypeList.find((item) => item.id === taskDetail?.perf_tool_type)?.name || '未知'}
                </span>
              </div>
            </Col>

            {taskDetail.owner && (
              <Col xs={24} sm={12} md={8} lg={4}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                  <span style={{
                    fontSize: '13px',
                    color: '#86909C',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}>
                    创建人
                  </span>
                  <div>
                    <Space size={8}>
                      <Image
                        preview={false}
                        style={{
                          borderRadius: '50%',
                          border: '1px solid #F2F3F5'
                        }}
                        height="24"
                        width="24"
                        src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${taskDetail.owner}?format=40x40.png`}
                      />
                      <Tooltip content={taskDetail.owner}>
                        <span style={{
                          fontSize: '15px',
                          color: '#1D2129',
                          fontWeight: 500
                        }}>
                          {taskDetail.owner}
                        </span>
                      </Tooltip>
                    </Space>
                  </div>
                </div>
              </Col>
            )}

            {taskDetail.status >= TASK_STATUS_RUNNING && (
              <Col xs={24} sm={12} md={8} lg={4}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                  <span style={{
                    fontSize: '13px',
                    color: '#86909C',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}>
                    开始时间
                  </span>
                  <span style={{
                    fontSize: '14px',
                    color: '#1D2129',
                    fontWeight: 500,
                    fontFamily: 'Monaco, Consolas, monospace'
                  }}>
                    {taskDetail?.start_time || '-'}
                  </span>
                </div>
              </Col>
            )}

            {taskDetail.status >= TASK_STATUS_SUCCESS && (
              <Col xs={24} sm={12} md={8} lg={4}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                  <span style={{
                    fontSize: '13px',
                    color: '#86909C',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}>
                    结束时间
                  </span>
                  <span style={{
                    fontSize: '14px',
                    color: '#1D2129',
                    fontWeight: 500,
                    fontFamily: 'Monaco, Consolas, monospace'
                  }}>
                    {taskDetail?.end_time || '-'}
                  </span>
                </div>
              </Col>
            )}
          </Row>
        </div>

        {/* 实验信息区域 - 仅当任务类型为libra实验时显示 */}
        {taskDetail.type >= TASK_TYPE_2 && (
          <>
            <Divider style={{ margin: '12px 0', borderColor: '#F2F3F5' }} />
            <div style={{ marginBottom: '16px' }}>
              <Row gutter={[16, 12]}>
                <Col xs={24} sm={12} md={8} lg={8}>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                    <span style={{
                      fontSize: '13px',
                      color: '#86909C',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      实验组
                    </span>
                    <span style={{
                      fontSize: '15px',
                      color: '#1D2129',
                      fontWeight: 500,
                      wordBreak: 'break-all'
                    }}>
                      {taskDetail?.experiments_name || taskDetail?.experiment?.experiment_group_version_ids || '无'}
                    </span>
                  </div>
                </Col>

                <Col xs={24} sm={12} md={8} lg={8}>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                    <span style={{
                      fontSize: '13px',
                      color: '#86909C',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      对照组
                    </span>
                    <span style={{
                      fontSize: '15px',
                      color: '#1D2129',
                      fontWeight: 500,
                      wordBreak: 'break-all'
                    }}>
                      {taskDetail?.control_name || taskDetail?.experiment?.control_group_version_ids || '无'}
                    </span>
                  </div>
                </Col>

                <Col xs={24} sm={12} md={8} lg={8}>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                    <span style={{
                      fontSize: '13px',
                      color: '#86909C',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      命中方式
                    </span>
                    <span style={{
                      fontSize: '15px',
                      color: '#1D2129',
                      fontWeight: 500
                    }}>
                      {libraHitTypeMap.get(taskDetail?.hit_type || taskDetail?.experiment?.hit_type) || '未知'}
                    </span>
                  </div>
                </Col>
              </Row>
            </div>
          </>
        )}
      </Card>
      <Divider orientation={'left'} style={{
        fontSize: '16px',
        fontWeight: 600,
        color: '#1D2129',
        marginBottom: '24px'
      }}>
        子任务信息
      </Divider>
      <Collapse
        expandIconPosition={'right'}
        style={{
          marginTop: 20,
          border: 'none',
          backgroundColor: 'transparent'
        }}
        defaultActiveKey={'0'}
      >
        {('sub_tasks' in taskDetail) ? taskDetail['sub_tasks'].map((subTaskItem, subTaskIndex) => (
          <CollapseItem
            style={{
              marginBottom: '16px',
              border: '1px solid #F2F3F5',
              borderRadius: '8px',
              backgroundColor: '#FFFFFF'
            }}
            header={
              <div style={{
                padding: '16px 20px',
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                gap: '16px 32px'
              }}>
                <span style={{ marginRight: 40 }}><b>子任务ID：</b>{subTaskItem['id']}</span>

                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{
                    fontSize: '14px',
                    color: '#86909C',
                    fontWeight: 500,
                    minWidth: '60px'
                  }}>
                    任务名称
                  </span>
                  <span style={{
                    fontSize: '16px',
                    color: '#1D2129',
                    fontWeight: 500
                  }}>
                    {subTaskItem['name'] || '-'}
                  </span>
                </div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{
                    fontSize: '14px',
                    color: '#86909C',
                    fontWeight: 500,
                    minWidth: '40px'
                  }}>
                    平台
                  </span>
                  <Tag
                    color="blue"
                    style={{
                      borderRadius: '6px',
                      fontSize: '14px',
                      fontWeight: 500,
                      border: 'none'
                    }}
                  >
                    {platform_type_map[subTaskItem['platform']] || '-'}
                  </Tag>
                </div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{
                    fontSize: '14px',
                    color: '#86909C',
                    fontWeight: 500,
                    minWidth: '60px'
                  }}>
                    执行状态
                  </span>
                  <Tag
                    color={sub_task_state_map?.get(subTaskItem['status'])?.get('color')}
                    style={{
                      borderRadius: '6px',
                      fontSize: '14px',
                      fontWeight: 500,
                      border: 'none'
                    }}
                  >
                    {sub_task_state_map?.get(subTaskItem['status'])?.get('name') || '-'}
                  </Tag>
                </div>

                {subTaskItem['status'] >= TASK_STATUS_RUNNING && (
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      fontSize: '14px',
                      color: '#86909C',
                      fontWeight: 500,
                      minWidth: '60px'
                    }}>
                      开始时间
                    </span>
                    <span style={{
                      fontSize: '15px',
                      color: '#1D2129',
                      fontWeight: 500,
                      fontFamily: 'Monaco, Consolas, monospace'
                    }}>
                      {subTaskItem['start_time'] || '-'}
                    </span>
                  </div>
                )}

                {subTaskItem['status'] >= TASK_STATUS_RUNNING && (
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      fontSize: '14px',
                      color: '#86909C',
                      fontWeight: 500,
                      minWidth: '60px'
                    }}>
                      结束时间
                    </span>
                    <span style={{
                      fontSize: '15px',
                      color: '#1D2129',
                      fontWeight: 500,
                      fontFamily: 'Monaco, Consolas, monospace'
                    }}>
                      {subTaskItem['end_time'] || '-'}
                    </span>
                  </div>
                )}

                {(subTaskItem['status'] == TASK_STATUS_ERROR && subTaskItem['error_code'] != null) && (
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      fontSize: '12px',
                      color: '#86909C',
                      fontWeight: 500,
                      minWidth: '50px'
                    }}>
                      错误码
                    </span>
                    <Tooltip content={subTaskItem['error_title']}>
                      <Button
                        type='text'
                        size="small"
                        onClick={(e) => onClickErrorDetail(e, subTaskItem['error_title'], subTaskItem['error_detail'])}
                        style={{
                          padding: '0 8px',
                          height: '24px',
                          fontSize: '12px',
                          color: '#F53F3F',
                          fontWeight: 500,
                          border: '1px solid #F53F3F',
                          borderRadius: '4px'
                        }}
                      >
                        {subTaskItem['error_code']}
                      </Button>
                    </Tooltip>
                  </div>
                )}
              </div>
            }
            name={subTaskIndex.toString()}
          >
            <div style={{ padding: '12px' }}>
              {/* 设备和账号信息 */}
              <Card
                size="small"
                title={
                  <span style={{
                    fontSize: '13px',
                    fontWeight: 600,
                    color: '#1D2129'
                  }}>
                    设备和账号信息
                  </span>
                }
                style={{
                  marginBottom: '8px',
                  borderRadius: '4px',
                  border: '1px solid #F2F3F5'
                }}
                bodyStyle={{ padding: '8px 12px' }}
              >
                <Row gutter={[16, 8]}>
                  <Col xs={24} sm={24} md={12} lg={12}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                      <span style={{
                        fontSize: '14px',
                        color: '#86909C',
                        fontWeight: 500,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        执行设备
                      </span>
                      {subTaskItem['perf_device'] ? (
                        <div style={{
                          padding: '8px 12px',
                          backgroundColor: '#F0F9FF',
                          borderRadius: '6px',
                          border: '1px solid #D4EDDA'
                        }}>
                          <div style={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', gap: '16px' }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span style={{
                                fontSize: '13px',
                                color: '#86909C',
                                fontWeight: 500,
                                minWidth: '50px'
                              }}>
                                设备名
                              </span>
                              <span style={{
                                fontSize: '15px',
                                color: '#1D2129',
                                fontWeight: 500
                              }}>
                                {subTaskItem['perf_device']['name']}
                              </span>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span style={{
                                fontSize: '13px',
                                color: '#86909C',
                                fontWeight: 500,
                                minWidth: '50px'
                              }}>
                                品牌
                              </span>
                              <Tag
                                color="arcoblue"
                                style={{
                                  fontSize: '13px',
                                  fontWeight: 500,
                                  borderRadius: '4px'
                                }}
                              >
                                {subTaskItem['perf_device']['brand']}
                              </Tag>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span style={{
                                fontSize: '13px',
                                color: '#86909C',
                                fontWeight: 500,
                                minWidth: '50px'
                              }}>
                                型号
                              </span>
                              <span style={{
                                fontSize: '15px',
                                color: '#1D2129',
                                fontWeight: 500
                              }}>
                                {subTaskItem['perf_device']['model'] || '-'}
                              </span>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span style={{
                                fontSize: '13px',
                                color: '#86909C',
                                fontWeight: 500,
                                minWidth: '50px'
                              }}>
                                系统版本
                              </span>
                              <span style={{
                                fontSize: '15px',
                                color: '#1D2129',
                                fontWeight: 500
                              }}>
                                {subTaskItem['perf_device']['sys_version'] || '-'}
                              </span>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span style={{
                                fontSize: '13px',
                                color: '#86909C',
                                fontWeight: 500,
                                minWidth: '50px'
                              }}>
                                UDID
                              </span>
                              <span style={{
                                fontSize: '13px',
                                color: '#4E5969',
                                fontWeight: 400,
                                fontFamily: 'Monaco, Consolas, monospace',
                                wordBreak: 'break-all'
                              }}>
                                {subTaskItem['perf_device']['udid']}
                              </span>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <span style={{
                          fontSize: '15px',
                          color: '#86909C',
                          fontStyle: 'italic'
                        }}>
                          未配置设备
                        </span>
                      )}
                    </div>
                  </Col>

                  <Col xs={24} sm={24} md={12} lg={12}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                      <span style={{
                        fontSize: '14px',
                        color: '#86909C',
                        fontWeight: 500,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        性能账号
                      </span>
                      <div style={{
                        padding: '8px 12px',
                        backgroundColor: '#F0F9FF',
                        borderRadius: '6px',
                        border: '1px solid #D4EDDA'
                      }}>
                        <div style={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', gap: '16px' }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{
                              fontSize: '13px',
                              color: '#86909C',
                              fontWeight: 500,
                              minWidth: '50px'
                            }}>
                              手机号
                            </span>
                            <span style={{
                              fontSize: '15px',
                              color: '#1D2129',
                              fontWeight: 500,
                              fontFamily: 'Monaco, Consolas, monospace'
                            }}>
                              {subTaskItem['perf_account']?.['iphone'] || '-'}
                            </span>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{
                              fontSize: '13px',
                              color: '#86909C',
                              fontWeight: 500,
                              minWidth: '50px'
                            }}>
                              用户名
                            </span>
                            <span style={{
                              fontSize: '15px',
                              color: '#1D2129',
                              fontWeight: 500
                            }}>
                              {subTaskItem['perf_account']?.['username'] || '-'}
                            </span>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{
                              fontSize: '13px',
                              color: '#86909C',
                              fontWeight: 500,
                              minWidth: '50px'
                            }}>
                              UID
                            </span>
                            <span style={{
                              fontSize: '14px',
                              color: '#4E5969',
                              fontWeight: 400,
                              fontFamily: 'Monaco, Consolas, monospace'
                            }}>
                              {subTaskItem['perf_account']?.['uid'] || '-'}
                            </span>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{
                              fontSize: '13px',
                              color: '#86909C',
                              fontWeight: 500,
                              minWidth: '50px'
                            }}>
                              验证码
                            </span>
                            <span style={{
                              fontSize: '15px',
                              color: '#1D2129',
                              fontWeight: 500
                            }}>
                              {subTaskItem['perf_account']?.['captcha'] || '-'}
                            </span>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span style={{
                              fontSize: '13px',
                              color: '#86909C',
                              fontWeight: 500,
                              minWidth: '50px'
                            }}>
                              国家
                            </span>
                            <span style={{
                              fontSize: '15px',
                              color: '#1D2129',
                              fontWeight: 500
                            }}>
                              {subTaskItem['perf_account']?.['country'] || '-'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>

              {/* 执行配置信息 */}
              <Card
                size="small"
                title={
                  <span style={{
                    fontSize: '13px',
                    fontWeight: 600,
                    color: '#1D2129'
                  }}>
                    执行配置信息
                  </span>
                }
                style={{
                  marginBottom: '8px',
                  borderRadius: '4px',
                  border: '1px solid #F2F3F5'
                }}
                bodyStyle={{ padding: '8px 12px' }}
              >
                <Row gutter={[16, 8]}>
                  <Col xs={24} sm={24} md={24} lg={24}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                      <span style={{
                        fontSize: '14px',
                        color: '#86909C',
                        fontWeight: 500,
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        执行用例
                      </span>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                        {subTaskItem['case_list']?.length > 0 ?
                          subTaskItem['case_list'].map((caseItem, caseIndex) => (
                            <Tag
                              key={caseIndex}
                              color="blue"
                              size="small"
                              style={{
                                borderRadius: '4px',
                                fontSize: '15px',
                                fontWeight: 500,
                                border: 'none',
                                lineHeight: '1.2'
                              }}
                            >
                              {caseItem.case_name}
                            </Tag>
                          )) :
                          <span style={{ fontSize: '14px', color: '#86909C' }}>无</span>
                        }
                      </div>
                    </div>
                  </Col>

                  {/* 配置参数水平显示 */}
                  <Col xs={24}>
                    <div style={{
                      padding: '8px 12px',
                      backgroundColor: '#F0F9FF',
                      borderRadius: '6px',
                      border: '1px solid #D4EDDA'
                    }}>
                      <div style={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        gap: '12px 8px'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '120px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            等待时间
                          </span>
                          <span style={{
                            fontSize: '15px',
                            color: '#1D2129',
                            fontWeight: 500
                          }}>
                            {subTaskItem['perf_collect_duration']} 秒
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '120px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            时间间隔
                          </span>
                          <span style={{
                            fontSize: '15px',
                            color: '#1D2129',
                            fontWeight: 500
                          }}>
                            {subTaskItem['perf_collect_interval']} 毫秒
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '120px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            采集方式
                          </span>
                          <span style={{
                            fontSize: '15px',
                            color: '#1D2129',
                            fontWeight: 500
                          }}>
                            {perf_collect_mode_map[subTaskItem['perf_collect_mode']] || '-'}
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '140px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            App安装方式
                          </span>
                          <span style={{
                            fontSize: '15px',
                            color: '#1D2129',
                            fontWeight: 500
                          }}>
                            {app_install_map[subTaskItem['app_install_type']] || '-'}
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '120px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            最低电量
                          </span>
                          <span style={{
                            fontSize: '15px',
                            color: '#1D2129',
                            fontWeight: 500
                          }}>
                            {subTaskItem['perf_device_power_level']}%
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '120px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            执行次数
                          </span>
                          <span style={{
                            fontSize: '15px',
                            color: '#1D2129',
                            fontWeight: 500
                          }}>
                            {subTaskItem['case_run_count']} 次
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '120px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            重试次数
                          </span>
                          <span style={{
                            fontSize: '15px',
                            color: '#1D2129',
                            fontWeight: 500
                          }}>
                            {subTaskItem['case_retry_count']} 次
                          </span>
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flex: '1 1 calc(12.5% - 8px)',
                          minWidth: '140px'
                        }}>
                          <span style={{
                            fontSize: '13px',
                            color: '#86909C',
                            fontWeight: 500,
                            whiteSpace: 'nowrap'
                          }}>
                            测试视频源
                          </span>
                          <Tooltip content={subTaskItem['video_url']}>
                            <Link
                              href={subTaskItem['video_url']}
                              target="_blank"
                              style={{
                                fontSize: '15px',
                                fontWeight: 500
                              }}
                            >
                              查看视频
                            </Link>
                          </Tooltip>
                        </div>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>



              {/* 用例执行详情标题 */}
              <div style={{
                marginBottom: '8px',
                padding: '6px 12px',
                backgroundColor: '#F7F8FA',
                borderRadius: '4px',
                border: '1px solid #F2F3F5'
              }}>
                <span style={{
                  fontSize: '13px',
                  fontWeight: 600,
                  color: '#1D2129'
                }}>
                  用例执行详情
                </span>
              </div>
            </div>
            <Table
              // style={{ marginTop: 20 }}
              border={true}
              borderCell={true} // 显示单元格的内部边框
              hover={false} // 鼠标悬停时显示高亮效果
              stripe={true} // 显示斑马纹效果
              // scroll={{ x: true }} // 设置表格的横向滚动
              loading={tableLoadingMap[subTaskItem['id']]}
              // 筛选下，如果item没有visible这个参数，才显示
              columns={caseRunDetailColumns.filter((item) => (item.visible === true))}
              data={caseRunDetailMap[subTaskItem['id']]}
              pagination={paginationMap[subTaskItem['id']]}
              onChange={(pagination) => fetchCaseRunDetail(pagination, subTaskItem['id'])}
            ></Table>
          </CollapseItem>
        )) : <></>}
      </Collapse>
      <Modal
      visible={screenshotVisible}
      title={<div style={{ textAlign: 'left' }}>{'用例截图详情'}</div>}
      style={{ width: '80%', height: '90%' }}
      footer={null} // 隐藏默认的底部按钮

      onCancel={() => setScreenshotVisible(false)}
      >
         <Row gutter={10} style={{ marginTop: '10px',height: '100%' }}>
              <Col span={7} style={{height: '100%', overflow: 'scroll'}}>
              <Card
                      bordered={true}
                      style={{
                        backgroundColor: '#EBF5FB',
                        height: '75vh',
                        maxHeight: '75vh',
                        width: '100%',
                        overflow: 'scroll',
                      }}
                    >
                      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        
                      <Title heading={6} style={{ marginTop: 0 }}>
                        截图列表，点击查看大图
                      </Title>
        
                      </div>
                        {/* 遍历图片链接列表，展示小图 */}
                        {screenshotTosUrls.map((url, index) => (
                          <div>

                          
                            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                            <img
                                key={index}
                                src={url}
                                alt={`Small Image ${index + 1}`}
                                style={{ width: '60px', height: '120px', margin: '10px', cursor: 'pointer' }}
                                onClick={() => handleSmallImageClick(url)}
                            />
                            
                            </div>
                            <div>
                              {/* <Col span={1}> */}
                              <text style={{fontSize: '12px'}}>
                              {url.split('/')[url.split('/').length-1]}
                            </text>
                            {/* </Col> */}
                            </div>
                            </div>
                            
                        ))}
              
                 </Card>
              </Col>
              <Col span={16} style={{height: '100%', overflow: 'scroll'}}>
              <Card
               bordered={true}
               style={{
                 backgroundColor: '#EBF5FB',
                 height: '75vh',
                 maxHeight: '75vh',
                //  width: '90%',
                 overflow: 'scroll',
               }}
              
              >
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <Title heading={6} style={{ marginTop: 0 }}>
                  {(currentLargeImage!=null && currentLargeImage!='')? currentLargeImage.split('/')[currentLargeImage.split('/').length-1]:('')}
                {/* {currentLargeImage.split('/')[currentLargeImage.split('/').length-1]} */}
                      </Title></div>
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                      
              
                        <div className="large-image" >
                            {/* 展示当前选中的大图 */}
                            <img
                                src={currentLargeImage}
                                alt="大图"
                                style={{ width: '350px', height: '740px', margin: '10px' }}
                            />
                        </div>
                      </div>
                </Card>
              </Col>
        
            </Row>
      </Modal>
    </Card>
  );
}
