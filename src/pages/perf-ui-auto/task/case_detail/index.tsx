import React, { useEffect, useState } from 'react';
import { Button, Card, Form, Grid,Tag, Select, Table, Typography, Tooltip,Space,Image } from '@arco-design/web-react';
import Builder from '@devsre/builder';
import { BackendApis2 } from 'src/utils/backendApis2';
import { useNavigate, useLocation } from 'react-router-dom';
import { set } from 'lodash';
import {IconArrowLeft} from '@arco-design/web-react/icon';


const Option = Select.Option;
const { Title } = Typography;
const { Row, Col } = Grid;
export default function CurdCard() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [businessList, setBusinessList] = useState([]);
  const [ruleList, setRuleList] = useState([]);


// 页面跳转相关
 const navigate = useNavigate();
const location = useLocation();



//---url参数相关---
const query = new URLSearchParams(location.search);
// 处理查询参数名
const QUERY_CASE_ID = 'case_id'
// 处理查询参数值
var queryCaseId = query.get(QUERY_CASE_ID);

// 截图链接相关
const [screenshotTosUrls, setScreenshotTosUrls] = useState([
// "https://cloud-page.bytedance.net/platform/api/v1/user/avatar/yinyanting.2022?format=40x40.png"

]);
const [caseDetail,setCaseDetail] = useState({
  "case_level": "none_case_level",
  "case_id": 0,
  "timeout": 0,
  "platform": "1,2",
  "title": null,
  "tags": "",
  "update_time": null,
  "business_id": 3,
  "case_name": null,
  "owner": null,
  "device_count": 2,
  "description": null,
  "create_time": "2024-11-19T18:57:30",
  "dir": "/global_business_perf/business/global_rtc/cases/anchor_co_anchor_1v1.py/AnchorCoAnchor_1V1"
})

  
  // 分页器
  const [pagination, setPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 20,
    current: 1,
    pageSizeChangeResetCurrent: true,
  });
  // 当前展示的图片索引
  const [currentIndex, setCurrentIndex] = useState(0);

  // 切换到下一张图片
  const nextImage = () => {
      setCurrentIndex((prevIndex) => (prevIndex === screenshotTosUrls.length - 1 ? 0 : prevIndex + 1));
  };

  // 切换到上一张图片
  const prevImage = () => {
      setCurrentIndex((prevIndex) => (prevIndex === 0 ? screenshotTosUrls.length - 1 : prevIndex - 1));
  };

  // 当前展示的大图链接，初始为列表中的第一张图片
  const [currentLargeImage, setCurrentLargeImage] = useState(screenshotTosUrls[0]);

  // 处理小图点击事件，更新当前展示的大图链接
  const handleSmallImageClick = (imageUrl) => {
      setCurrentLargeImage(imageUrl);
  };
  // 回退到上个页面的逻辑？
const handleBack = () => {
  navigate(-1);
}

  // 获取case详情
  const fetchCaseDetail = async () => {
    const response = await BackendApis2.getPerfCaseDetail({ payload:{case_id: queryCaseId} }).finally(() => { });
    setCaseDetail(response.case);
  };


  // 初始化数据
  useEffect(() => {
    // fetchBusinessList();
    if (location.state) {
//   先获取截图列表
      setScreenshotTosUrls(location.state.data.screenshot_tos_urls);
      setCurrentLargeImage(location.state.data.screenshot_tos_urls[0]);

    }
    if (queryCaseId != null && /^\d+$/.test(queryCaseId)){
// 获取case详情
      fetchCaseDetail();
    }
    setCurrentLargeImage(screenshotTosUrls[0]);

  }, []);
  
  return (
    <Card bordered={false}>
      <Row>
      <Button style={{ float: 'left', width: '60x' }}
             type="text"  
            icon={<IconArrowLeft />}
            onClick={() => handleBack()}>
                返回上个页面
              </Button>
      </Row>
     
       <Row>
          <Col span={24}>
            <Card bordered={false} style={{ backgroundColor: '#EBF5FB' }}>
              <Title heading={6} style={{ marginTop: 0 }}>
                用例基础信息
              </Title>
              <Row>
                <Col span={8}>
                  <span>
                    <b>用例id：</b>
                    {caseDetail.case_id}
                  </span>
                </Col>
                <Col span={8}>
                  <span>
                    <b>用例负责人：</b>
                    <Space>
                      <Image
                        preview={false}
                        style={{ borderRadius: '50%' }}
                        height="20"
                        src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${caseDetail.owner}?format=40x40.png`}
                      ></Image>
                      <span>{caseDetail.owner}</span>
                      {/* <Tooltip mini content={dialogData.qa_owner}> */}
                      {/* {dialogData.qa_owner.length > 8 ? dialogData.qa_owner.substring(0, 8) + '...' : dialogData.qa_owner} */}
                      {/* </Tooltip> */}
                    </Space>
                  </span>
                </Col>
              </Row>
              <Row>
                <Col span={8}>
                  <span>
                    <b>用例名称：</b>
                    {caseDetail.title}
                  </span>
                </Col>
                <Col span={8}>
                  <span>
                    <b>用例描述：</b>
                    {caseDetail.description}
                  </span>
                </Col>
                </Row>
            </Card>
          </Col>
        </Row>
        {/* <Row gutter={10} style={{ marginTop: '10px' }}>
          <Col span={12}>
            <Card
              bordered={false}
              style={{
                backgroundColor: '#EBF5FB',
                height: 250,
                maxHeight: 450,
                overflow: 'scroll',
              }}
            >
              <Title heading={6} style={{ marginTop: 0 }}>
                执行情况
              </Title>
              <Row>
                <Col span={6}>
                  <span>
                    <b>执行类型：</b>
                    <span>
                      test 执行类型
                    </span>
                  </span>
                </Col>
                <Col span={6}>
                  <span>
                    <b>端类型：</b>
                    <span>
                      {caseDetail.platform.includes("1")? (
                        <Tag
                        >
                          {
                            "Android"
                          }
                        </Tag>
                      ) :  <span />}
                      {caseDetail.platform.includes("2")? (
                        <Tag
                        >
                          {
                            "iOS"
                          }
                        </Tag>
                      ):<span />}
                    </span>
                  </span>
                </Col>
              
              </Row>
              <Row>
                <Col span={14}>
                  <span>
                    <b>测试时间：</b>
                   {"test 测试时间"}
                  </span>
                </Col>
                <Col span={10}>
                  <span>
                    <b>执行耗时：</b>
                    test 执行耗时
                  </span>
                </Col>
              </Row>
              <Row>
                
              </Row>
              <Row>
                <Col span={24}>
                  <span>
                    <b>设备信息</b>
                  </span>
                </Col>
              </Row>
          
            </Card>
          </Col>
          <Col span={12}>
            <Card
              bordered={false}
              style={{
                backgroundColor: '#EBF5FB',
                height: 250,
                maxHeight: 450,
                overflow: 'scroll',
              }}
            >
              <Title heading={6} style={{ marginTop: 0 }}>
                执行结果信息
              </Title>
              <Row>
                <Col span={12}>
                  <span>
                    <b>错误码：</b>
                   
                  </span>
                </Col>
                <Col span={12}>
                  <span>
                    <b>错误码描述：</b>
                   
                  </span>
                </Col>
              </Row>
          </Card>
    </Col>
    </Row> */}

    <Row gutter={10} style={{ marginTop: '10px' }}>
      <Col span={5}>
      <Card
              bordered={true}
              style={{
                backgroundColor: '#EBF5FB',
                height: 950,
                maxHeight: 950,
                width: '100%',
                overflow: 'scroll',
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>

              <Title heading={6} style={{ marginTop: 0 }}>
                截图列表，点击查看大图
              </Title>

              </div>
                {/* 遍历图片链接列表，展示小图 */}
                {screenshotTosUrls.map((url, index) => (
              
                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <img
                        key={index}
                        src={url}
                        alt={`Small Image ${index + 1}`}
                        style={{ width: '100px', height: '100px', margin: '10px', cursor: 'pointer' }}
                        onClick={() => handleSmallImageClick(url)}
                    />
                    </div>
                    
                ))}
      
         </Card>
      </Col>
      <Col span={16} >
      <Card
       bordered={true}
       style={{
         backgroundColor: '#EBF5FB',
         height: 950,
         maxHeight: 950,
        //  width: '70%',
         overflow: 'scroll',
       }}
      
      >
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Title heading={6} style={{ marginTop: 0 }}>
                截图详情
              </Title></div>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              
      
                <div className="large-image" >
                    {/* 展示当前选中的大图 */}
                    <img
                        src={currentLargeImage}
                        alt="大图"
                        style={{ width: '600px', height: '800px', margin: '10px' }}
                    />
                </div>
              </div>
        </Card>
      </Col>

    </Row>
  
    </Card>
  );
}