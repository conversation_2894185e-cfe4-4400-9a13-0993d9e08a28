
import { Line<PERSON>hart, ILineChartSpec } from '@visactor/react-vchart';
import React, { useEffect, useState } from 'react';
import { Button, Card, Message, Typography } from '@arco-design/web-react';
import { BackendApis2 } from 'src/utils/backendApis2';
import { useNavigate, useLocation } from 'react-router-dom';
import { unitsMap, constants } from '../../../../utils/const';
import { IconArrowLeft } from '@arco-design/web-react/icon';
import { getFirstPerfDataUrl } from 'src/utils/urlFormatUtils';


export default function CurdCard() {
const { Title } = Typography;
const [loading, setLoading] = useState(false);
const navigate = useNavigate();
const location = useLocation();
// const [menPerfData,setMenPerfData] = useState([]);
// const [tempData,setTempData] = useState([]); 

//---url参数相关---
const query = new URLSearchParams(location.search);
// 处理查询参数名
const QUERY_TASK_ID = 'task_id'
// 处理查询参数值
var QUERY_APP_ID = 'app_id'
var queryAppId = query.get(QUERY_APP_ID);
// var QUERY_APP_TYPE = 'app_type'
// var queryAppType = query.get(QUERY_APP_TYPE);
var QUERY_PLATFORM = 'platform'
var queryPlatform = query.get(QUERY_PLATFORM);
var QUERY_CASE_ID = 'case_id'
var queryCaseId = query.get(QUERY_CASE_ID);
var QUERY_CASE_TITLE = 'case_title'
var queryCaseTitle = query.get(QUERY_CASE_TITLE);
var QUERY_SUB_TASK_ID = 'sub_task_id'
var querySubTaskId = query.get(QUERY_SUB_TASK_ID);
var QUERY_CONFIG_ID = 'config_id'
var queryConfigId = query.get(QUERY_CONFIG_ID);
const QUERY_PERF_TOOL_TYPE = 'perf_tool_type'
var queryPerfToolType = query.get(QUERY_PERF_TOOL_TYPE);


// 安卓和iOS具体的数据内容：
const [totalDataAndroid,setTotalDataAndroid] = useState({
  cpuNormalizedTotalUsage:[],
  cpuNormalizedProcUsage:[],
  gpuUsage:[],
  gpuLoad:[],
  fps:[],
  memPSS:[],
  memUSS:[],
  memDalvikHeap:[],
  memDalvikOther:[],
  batteryCurrent:[],
  batteryVoltage:[],
  batteryPower:[],
  netTotalReceive:[],
  netTotalSent:[],
  netProcReceive:[],
  netProcSent:[],
  fdCount:[],
  threadCount:[],
  diskRead:[],
  diskWritten:[],
  frame_rate_sent:[],
  mali_bus_read:[]
  });
const [totalDataIos,setTotalDataIos] = useState({
  cpuTotalUsage:[],
  cpuProcUsage:[],
  frame_rate_sent:[],
  gpuDevice:[],
  gpuRender:[],
  gpuTiler:[],
  fps:[],
  memTotalFootprint:[],
  memResident:[],
  memFootprint:[],
  batteryTemp:[],
  batteryVoltage:[],
  batteryAmperage:[],
  batteryPower:[],
  netProcReceive:[],
  netProcSent:[],
  threadCount:[],
  ctxSwitch:[],
  intWakeups:[],
  diskRead:[],
  diskWritten:[],
  energyCost:[],
  energyCPUCost:[],
  energyGPUCost:[],
  energyNetCost:[],
  energyLocationCost:[],
  energyThermalCost:[],
  energyOverheadCost:[]
})
const [caseTitle,setCaseTitle] = useState("用例");
// 任务类型，默认是版本回归任务
const [taskType,setTaskType] = useState(1);
// 常数
const appNameMap = new Map([
    [1,'性能包'],
    [2,'基准包'],
    [3,'辅助包']
]);

const platformMap = new Map([
    [1,'Android'],
    [2,'iOS']
]);




const [platform,setPlatform] = useState(1);

const bottomUnit = [{
  orient: 'bottom',
  type: 'band',
  domain: { dataId: 'barData', field: 'category' },
  label: {
    visible: true,
    formatMethod: (text: string) => `${text} ms`, // 在这里添加单位
  },
}
];
// 表格表头样式：
const [specs, setSpecs] = useState([]);
const specsAndroid = [
    {
      type: 'line',
      visible: true,
      data: {
        values: totalDataAndroid.cpuNormalizedTotalUsage,
      },
      title: {
        visible: true,
        text: unitsMap.get('cpu_normalized_total_usage') + '（cpuNormalizedTotalUsage）', 
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position: 'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.cpuNormalizedProcUsage,
      },
      title: {
        visible: true,
        text: unitsMap.get('cpu_normalized_proc_usage') + '（cpuNormalizedProcUsage）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position: 'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.fps,
      },
      title: {
        visible: true,
        text: 'FPS',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position: 'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.frame_rate_sent,
      },
      title: {
        visible: true,
        text: unitsMap.get('frame_rate_sent') + '（frame_rate_sent）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position: 'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.memPSS,
      },
      title: {
        visible: true,
        text: unitsMap.get('mem_pss') + '（memPSS）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],  
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.memUSS,
      },
      title: {
        visible: true,
        text: unitsMap.get('mem_uss') + '（memUSS）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.gpuUsage,
      },
      title: {
        visible: true,
        text:unitsMap.get('gpu_usage') + '（gpuUsage）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.gpuLoad,
      },
      title: {
        visible: true,
        text:unitsMap.get('gpu_load') + '（gpuLoad）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.batteryPower,
      },
      title: {
        visible: true,
        text:unitsMap.get('battery_power') + '（batteryPower）',      

      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line', 
      data: {
        values: totalDataAndroid.netTotalReceive,   
      },
      title: {
        visible: true,
        text:unitsMap.get('net_total_receive') + '（netTotalReceive）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.netTotalSent,
      },
      title: {
        visible: true,
        text:unitsMap.get('net_total_sent') + '（netTotalSent）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.netProcReceive,  
      },
      title: {
        visible: true,
        text:unitsMap.get('net_proc_receive') + '（netProcReceive）',  
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.netProcSent,
      },
      title: {
        visible: true,
        text:unitsMap.get('net_proc_sent') + '（netProcSent）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.fdCount,
      },
      title: {
        visible: true,
        text:unitsMap.get('fd_count') + '（fdCount）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.threadCount,
      },
      title: {
        visible: true,
        text:unitsMap.get('thread_count') + '（threadCount）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.diskRead,
      },
      title: {
        visible: true,
        text:unitsMap.get('disk_read') + '（diskRead）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.diskWritten, 
      },
      title: {
        visible: true,
        text:unitsMap.get('disk_written') + '（diskWritten）', 
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.memDalvikHeap,
      },
      title: {
        visible: true,
        text:unitsMap.get('mem_dalvik_heap') + '（memDalvikHeap）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.memDalvikOther,
      },
      title: {
        visible: true,
        text:unitsMap.get('mem_dalvik_other') + '（memDalvikOther）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.batteryCurrent,
      },
      title: {
        visible: true,
        text:unitsMap.get('battery_current') + '（batteryCurrent）',  
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataAndroid.batteryVoltage,
      },
      title: {
        visible: true,
        text:unitsMap.get('battery_voltage') + '（batteryVoltage）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    }
  ];
  const specsIos = [
    {
      type: 'line',
      data: {
        values: totalDataIos.cpuTotalUsage,
      },
      title: {
        visible: true,
        text: unitsMap.get('cpu_total_usage') + '（cpuTotalUsage）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.cpuProcUsage,
      },
      title: {
        visible: true,
        text: unitsMap.get('cpu_proc_usage') + '（cpuProcUsage）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.fps,
      },
      title: {
        visible: true,
        text: 'FPS',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.frame_rate_sent,
      },
      title: {
        visible: true,
        text: unitsMap.get('frame_rate_sent') + '（frameRateSent）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.gpuDevice,
      },
      title: {
        visible: true,
        text:unitsMap.get('gpu_device') + '（gpuDevice）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.gpuRender,
      },
      title: {
        visible: true,
        text:unitsMap.get('gpu_render') + '（gpuRender）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.gpuTiler,
      },
      title: {
        visible: true,
        text:unitsMap.get('gpu_tiler') + '（gpuTiler）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.memTotalFootprint,
      },
      title: {
        visible: true,
        text:unitsMap.get('mem_total_footprint') + '（memTotalFootprint）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.memResident,
      },
      title: {
        visible: true,
        text:unitsMap.get('mem_resident') + '（memResident）', 
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.memFootprint,
      },
      title: {
        visible: true,
        text:unitsMap.get('mem_footprint') + '（memFootprint）',  
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.batteryTemp,
      },
      title: {
        visible: true,
        text:unitsMap.get('battery_temp') + '（batteryTemp）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.batteryVoltage,
      },
      title: {
        visible: true,
        text:unitsMap.get('battery_voltage') + '（batteryVoltage）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.batteryAmperage,
      },
      title: {
        visible: true,
        text:unitsMap.get('battery_amperage') + '（batteryAmperage）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.batteryPower,
      },
      title: {
        visible: true,
        text:unitsMap.get('battery_power') + '（batteryPower）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.netProcReceive,
      },
      title: {
        visible: true,
        text:unitsMap.get('net_proc_receive') + '（netProcReceive）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.netProcSent,
      },
      title: {
        visible: true,
        text:unitsMap.get('net_proc_sent') + '（netProcSent）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.threadCount,
      },
      title: {
        visible: true,
        text:unitsMap.get('thread_count') + '（threadCount）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.ctxSwitch,
      },
      title: {
        visible: true,
        text:unitsMap.get ('ctx_switch') ,
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.intWakeups,
      },
      title: {
        visible: true,
        text:unitsMap.get('int_wakeups') + '（intWakeups）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.diskRead,
      },
      title: {
        visible: true,
        text:unitsMap.get('disk_read') + '（diskRead）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.diskWritten,
      },
      title: {
        visible: true,
        text:unitsMap.get('disk_written') + '（diskWritten）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.energyCost,
      },
      title: {
        visible: true,
        text:unitsMap.get('energy_cost') + '（energyCost）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.energyCPUCost,
      },
      title: {
        visible: true,
        text:unitsMap.get('energy_cpu_cost') + '（energyCPUCost）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.energyGPUCost,
      },
      title: {
        visible: true,
        text:unitsMap.get('energy_gpu_cost') + '（energyGPUCost）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.energyNetCost,
      },
      title: {
        visible: true,
        text:unitsMap.get('energy_net_cost') + '（energyNetCost）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.energyLocationCost,
      },
      title: {
        visible: true,
        text:unitsMap.get('energy_location_cost') + '（energyLocationCost）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.energyThermalCost,
      },
      title: {
        visible: true,
        text:unitsMap.get('energy_thermal_cost') + '（energyThermalCost）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    },
    {
      type: 'line',
      data: {
        values: totalDataIos.energyOverheadCost,
      },
      title: {
        visible: true,
        text:unitsMap.get('energy_overhead_cost') + '（energyOverheadCost）',
      },
      stack: false,
      xField: 'time',
      yField: 'value',
      axes: bottomUnit,
      seriesField: 'type',
      invalidType: 'link',
      animationAppear: {
        duration: 1500,
        easing: 'linear',
      },
      legends: [{ visible: true, position:'middle', orient: 'bottom' }],
    }
  ]
  // 定义 ChartGroup 组件
  interface ChartGroupProps {
    specs: ILineChartSpec[];
  }
  const ChartGroup: React.FC<ChartGroupProps> = ({ specs }) => {
    return (
      <div>
        {specs.map((spec, index) => (
          <LineChart key={index} {...spec} />
        ))}
      </div>
    );
  };
// 获取json链接中的json数据
  const fetchJsons = async (body) => {
    const tempJsons = [[],[]]; // 分别存放性能包和基准包
     // 获取json链接
    const response = await BackendApis2.getPerfDataDetail({ body }).finally(() => {
      setLoading(false);
    });
  
     if (response.code!=200) {
          Message.error("用例数据不存在或数据查询失败")
        }
    let tasktype = 1;
    for (let i = 0; i < response.data.length; i++) {
      // 使用兼容性函数获取第一个性能数据URL
      const perfDataUrl = getFirstPerfDataUrl(response.data[i]);
      if (!perfDataUrl) {
        Message.info("未找到性能数据URL");
        continue;
      }

      const json_result = await fetch(perfDataUrl);
      if (!json_result.ok) {
      Message.info("获取json数据失败");
      throw new Error(`HTTP error! status: ${json_result.status}`);
      }
    const result = await json_result.json();
    
    // 如果这次的version type不是null，不是0，说明是libra
    if (response.data[i].version_type != null && response.data[i].version_type != 0) {
      console.log("libra")
      setTaskType(constants.TASK_TYPE_2);
      tasktype = 2;
    tempJsons[response.data[i].version_type-1].push(result);
    }
    else{
    tempJsons[response.data[i].app_type-1].push(result);
    }
    }
    return {
      tempJsons:tempJsons,
      tasktype:tasktype
    }
      ;
  };

const getDictValue = async(processData, key: string, defaultValue) => {
  if(processData.length == 0){
    return defaultValue;
  }
  else{
    return processData[0].hasOwnProperty(key)? processData[0][key] : defaultValue;
  }
 
};
// 处理android的processdata，预处理，它可能为0的情况
const getProcessDataAndroid = async (json) => {
  const temp_json = json;
  if(json.processData.length == 0){
    json.processData.push({
        "pid": 0,
        "processName": "com.zhiliaoapp.musically",
        "cpuProcUsage": 0.0,
        "cpuNormalizedProcUsage": 0.0,
        "memPSS": 0,
        "memSWAP": 0,
        "memVirtual": 0,
        "memHeap": 0,
        "memNative": 0,
        "memGraphics": 0,
        "memGfx": 0,
        "memGL": 0,
        "memUnknown": 0,
        "diskRead": 0,
        "diskWritten": 0,
        "netTotalReceive": 0,
        "netTotalSent": 0,
        "netProcReceive": 0,
        "netProcSent": 0,
        "fdCount": 0,
        "threadCount": 0,
        "memUSS": 0,
        "memDalvikHeap": 0,
        "memDalvikOther": 0,
        "memAshmem": 0,
        "memGfxDev": 0,
        "memOtherDev": 0,
        "memSoMmap": 0,
        "memJarMmap": 0,
        "memApkMmap": 0,
        "memTtfMmap": 0,
        "memDexMmap": 0,
        "memOatMmap": 0,
        "memArtMmap": 0,
        "memOtherMmap": 0,
        "memEglMtrack": 0,
        "memNativeHeapSummary": 0,
        "memCode": 0,
        "memStack": 0,
        "memPrivateOther": 0,
        "memSystem": 0
    })
    
  }
  return temp_json
}
// 处理ios的processdata，预处理，它可能为0的情况
const getProcessDataIos = async (json) => {
  const temp_json = json;
  if(json.processData.length == 0){
    json.processData.push({
      
        "pid": 0,
        "processName": "TikTok",
        "cpuProcUsage": 0,
        "memResident": 0,
        "memFootprint": 0,
        "memVirtual": 0,
        "threadCount": 0,
        "ctxSwitch": 0,
        "intWakeups": 0,
        "diskRead": 0,
        "diskWritten": 0,
        "netProcReceive": 0,
        "netProcSent": 0,
        "energyCost": 0,
        "energyCPUCost": 0,
        "energyGPUCost": 0,
        "energyNetCost": 0,
        "energyLocationCost": 0,
        "energyThermalCost": 0,
        "energyOverheadCost": 0
      
    })
  }
}
// 填充数据到totalData，中间过程
const pushDataToTotalData = (tempTotalData,json,app_name,i,platform,metric_items) => {
  // 遍历所有metric_items，每个metric item都处理一次，把json中的内容填充到totaldata中
  for (let metric_item of metric_items){
    let metric_key = metric_item.metric_key
    // key需要从下划线变成驼峰格式
    let metric_key_camel = metric_key
    if(parseInt(queryPerfToolType) == constants.PERF_TOOL_TYPE_DS){
      metric_key_camel = metric_key.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase());

    }
    // 如果是memPss，和memUss，需要把后面两个s替换成a
    if(metric_key_camel == "memPss" || metric_key_camel == "memUss"){
      metric_key_camel = metric_key_camel.replace("ss","SS");
    }
    let tmp_value = 0;
         // value的值:如果json key的值是数字，则取数字。是列表则取第一个值;如果
    if (json.hasOwnProperty(metric_key_camel)){
    if(typeof(json[metric_key_camel])=="number"){
      tmp_value = json[metric_key_camel];
    }
    else{if(typeof(json[metric_key_camel])=="object"){
      tmp_value = json[metric_key_camel][0];
    }
    else{
      tmp_value = 0;
    }
    }
  }
  else{
    // 如果json中有processdata，从processdata中找
    if(json.hasOwnProperty("processData")){
      // 如果processdata是超过1长度的列表，并且processdata【0】中有metric_key_camel
      if(json.processData.length > 0 && json.processData[0].hasOwnProperty(metric_key_camel)){
        tmp_value = json.processData[0][metric_key_camel];
      }
    }
  }

    if(!tempTotalData.hasOwnProperty(metric_key)){
      tempTotalData[metric_key] = [];
    }
    tempTotalData[metric_key].push({
      time:json.time,
      type:app_name+"第"+(i+1)+"次",
      value:tmp_value
  });

}
  return tempTotalData;

}

// 把json数据处理成图表需要的数据
const dealJsonsData = (temp_jsons,platform,task_type,metric_items) => {
  // let tempTotalData = ((platform == "1")||(platform == 1))?totalDataAndroid:totalDataIos; // 平台为1时为android
  let tempTotalData = {
    cpuNormalizedTotalUsage:[],
    cpuNormalizedProcUsage:[],
    gpuUsage:[],
    gpuLoad:[],
    fps:[],
    memPSS:[],
    memUSS:[],
    memDalvikHeap:[],
    memDalvikOther:[],
    batteryCurrent:[],
    batteryVoltage:[],
    batteryPower:[],
    netTotalReceive:[],
    netTotalSent:[],
    netProcReceive:[],
    netProcSent:[],
    fdCount:[],
    threadCount:[],
    diskRead:[],
    diskWritten:[],
    frame_rate_sent:[],
    mali_bus_read:[]
  };
  // 处理json数据
  for (let m = 0; m < temp_jsons.length; m++) { // 分别处理基准包和性能包
  let app_name = appNameMap.get(m+1) || '未定义包体';
  console.log("task_type")
  console.log(task_type)
  if (task_type == constants.TASK_TYPE_2){
    console.log("libra实验")
   app_name = constants.version_type_map.get(m+1) || '未定义包体';
  }
  for (let i = 0; i < temp_jsons[m].length; i++) {
    // const tempData = [];
    for (const json of temp_jsons[m][i]) {
      // 每1000ms，取所有需要的字段值，填充到字段的数组中：
        // const json1 = await getProcessDataAndroid(json);
        tempTotalData = pushDataToTotalData(tempTotalData,json,app_name,i,platform,metric_items);
    // setTempData(prevList=>[...prevList,jsons[i][j].cpuNormalizedTotalUsage]);

  };
    console.log("第"+i+"次请求的tempTotalData为")
    console.log(tempTotalData)
  }; 
  };
setTotalDataAndroid(tempTotalData);
let temp_specs = [
    ]
for (let metric_item of metric_items){
      let metric_key = metric_item.metric_key
      temp_specs.push({
        type: 'line',
        visible: true,
        data: {
          values: tempTotalData[metric_key],
          
        },
        title: {
          visible: true,
          text: metric_item.metric_name + '（'+metric_key+'）', 
        },
        stack: false,
        xField: 'time',
        yField: 'value',
        axes: bottomUnit,
        seriesField: 'type',
        invalidType: 'link',
        animationAppear: {
          duration: 1500,
          easing: 'linear',
        },
        legends: [{ visible: true, position: 'middle', orient: 'bottom' }],
      })
    }
    setSpecs(temp_specs)
};

// 根据当前端是安卓还是ios，判断此字段是否是该端无用的字段，无须展示
const judge_platform_para_visible = (metric_type,platform) => {
  // return true
  if (metric_type ===1){
    return true
  }

  if (platform === "1") {
    // 任务类型是安卓，
    if(metric_type === 2){
      return true
    }
     return false
    }
  if (platform === "2"){
  if(metric_type === 3){
    return true
  }
  return false;
}
  return true;
}
// 总的获取报告数据的函数
const fetchReportData = async (body) => {
      Message.info('任务数据加载中...');
      const temp_jsons =  await fetchJsons(body)
      let payload = {
      "config_id": queryConfigId
    }
      const config_data = await BackendApis2.configDetail({ payload }).finally(() => {
      })
      // 过滤一下当前的任务采集方式类型，根据采集方式类型来展示字段
      let metrics_filterd = config_data.metrics.filter(item => item.metric_category ===  parseInt(queryPerfToolType)).filter(item=>judge_platform_para_visible(item.metric_type,queryPlatform))
      dealJsonsData(temp_jsons.tempJsons,body.platform,temp_jsons.tasktype,metrics_filterd)
      // getConfigMetric(queryConfigId)
      Message.success('任务数据加载完成');
  };

// 回退到上个页面的逻辑？
const handleBack = () => {
  navigate(-1);
}
// 根据config id 获取metric
const getConfigMetric = async(queryConfigId)=>{
    let payload = {
      "config_id": queryConfigId
    }
    const config_data = await BackendApis2.configDetail({ payload }).finally(() => {
    })
    let temp_specs = [
    ]
    for (let metric_item of config_data.metrics){
      let metric_key = metric_item.metric_key
      temp_specs.push({
        type: 'line',
        visible: true,
        data: {
          values: totalDataAndroid[metric_key],
          
        },
        title: {
          visible: true,
          text: metric_item.metric_name + '（'+metric_key+'）', 
        },
        stack: false,
        xField: 'time',
        yField: 'value',
        axes: bottomUnit,
        seriesField: 'type',
        invalidType: 'link',
        animationAppear: {
          duration: 1500,
          easing: 'linear',
        },
        legends: [{ visible: true, position: 'middle', orient: 'bottom' }],
      })
    }
    setSpecs(temp_specs)
    
}


// 初始化数据
useEffect(() => {
  // Message.warning("请从性能报告中点击具体的数据列查看详情")
  // 如果是跳转过来的，则从location的任务id，请求具体的数据
 if (queryCaseId != null && /^\d+$/.test(queryCaseId)&&queryAppId!= null && /^\d+$/.test(queryAppId)&&querySubTaskId!= null && /^\d+$/.test(querySubTaskId)&&queryPlatform!= null && /^\d+$/.test(queryPlatform)&&queryConfigId!= null && /^\d+$/.test(queryConfigId)&&queryPerfToolType!= null && /^\d+$/.test(queryPerfToolType)) {
    setCaseTitle(queryCaseTitle)
    // 这里需要弄一下metric的内容 给处理json参数的时候用
    fetchReportData({"app_id":queryAppId,"case_id":queryCaseId,"sub_task_id":querySubTaskId,"platform":queryPlatform});
      // setSpecs(queryPlatform=='1'?specsAndroid:specsIos);
  // 如果config id 不为空，那么获取config中的metric；如果config id为空，则获取所有metric

        } 
    else {
          Message.warning('链接参数有误');
        }
    }, []);

  // 页面
  return (
    <Card bordered={false}>
      <Button style={{ float: 'left', width: '60x' }}
       type="text"  
      icon={<IconArrowLeft />}
      onClick={() => handleBack()}>
          返回上个页面
        </Button>
      <div>
        <Title heading={3} style={{ marginTop: 0 ,textAlign: 'center'}}>
          {caseTitle}性能数据详情
        </Title>
        <ChartGroup specs={specs}/>
        </div>
    </Card>
  );
}
