import React, { useCallback, useEffect, useState } from 'react';
import debounce from 'lodash/debounce';
import {Popover, Button, Card, Grid, Table,Tag, Message,Typography} from '@arco-design/web-react';
import { BackendApis2 } from 'src/utils/backendApis2';
import { useUser } from '@devsre/react-utils'
import { useNavigate, useLocation } from 'react-router-dom';
import { Switch } from '@arco-design/web-react'
import { IconQuestionCircle,IconArrowLeft} from '@arco-design/web-react/icon';
import { platform_map,app_type_map,INCREASE,DECREASE,PERCENTAGE,ABSOLUTE,value_type_list ,comparison_type_list,version_type_map,EXPERIMENT_GROUP} from '../../../utils/const';



const { Title } = Typography;
const { Row } = Grid;

export default function CurdCard() {
  const [loading, setLoading] = useState(true); // 加载状态
 const navigate = useNavigate();
  const location = useLocation();

  const APP_TYPE_PERF = 1
  const APP_TYPE_STANDARD = 2
  const APP_TYPE_ASSIST = 3


  
 
  const sceneList = [
    '主播连麦1V1',
    '主播嘉宾连麦1V1（主播端）',
    '主播嘉宾连麦1V3（主播端）',
    '游戏直播',
    '主播网直播',
  ];
  const [vitalHover, setVitalHover] = useState(`暂无配置数据，无重要字段`);

  const [conclusionSubtask, setConclusionSubtask] = useState([]);
  // 重要字段
  const [metricsStandard, setMetricsStandard] = useState([]);
  // 可见字段
  const [metricsVibible, setMetricsVibible] = useState([]);
  const [versionList, setVersionList] = useState([]);
  const [formValues, setFormValues] = useState({
    version: '',
    platform: [],
    machine: [],
    app_type: [],
    scene: [],
  });
  const VitalList= ['platform_str','case_title','app_type_str','detail','cpu_total_usage','cpu_proc_usage','mem_total_footprint','mem_footprint','cpu_normalized_total_usage','cpu_normalized_proc_usage','mem_pss','mem_uss','fps','battery_power','frame_rate_sent']
  const AndroidInvisible = ['cpu_total_usage','cpu_proc_usage','mem_total_footprint','mem_footprint','gpu_device','gpu_render','gpu_tiler','mem_resident','battery_temp','battery_amperage','ctx_switch','int_wakeups','energy_cost','energy_cpu_cost','energy_gpu_cost','energy_net_cost','energy_location_cost','energy_overhead_cost','energy_thermal_cost'];
  const IosInvisible = ['cpu_normalized_total_usage','cpu_normalized_proc_usage','mem_pss','mem_uss','gpu_usage','gpu_load','mem_dalvik_heap','mem_dalvik_other','battery_current','net_total_receive','net_total_sent','fd_count'];
  const commonParaList = ['platform_str','case_title','app_type_str']
  const [pagination, setPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 20,
    current: 1,
    pageSizeChangeResetCurrent: true,
  });
  
  // 这是控制是否只展示重要字段的开关
  const [vitalVisibleOnly,setVitalVisible] = useState(false);

      //---url参数相关---
    const query = new URLSearchParams(location.search);
    // 处理查询参数名
    const QUERY_TASK_ID = 'task_id'
    const QUERY_CONFIG_ID = 'config_id'
    // 处理查询参数值
    var queryTaskId = query.get(QUERY_TASK_ID);
    var queryConfigId = query.get(QUERY_CONFIG_ID);

    const QUERY_TASK_NAME = 'task_name'
    var queryTaskName = query.get(QUERY_TASK_NAME);

    const QUERY_PERF_TOOL_TYPE = 'perf_tool_type'
    var queryPerfToolType = query.get(QUERY_PERF_TOOL_TYPE);
  const QUERY_TYPE = 'type'
  var queryType = parseInt(query.get(QUERY_TYPE));
  // 这是版本类型
  // const [firstVersionType,setFirstVersionType] = useState(queryType);


 
  // 判断这是否是重要字段
  const judgeVisible = (paramName,metricsVibibles,metricsStandards) => {

    // if(paramName == "net_proc_sent"){
        console.log(paramName)
        console.log(metricsStandards)
        console.log(vitalVisibleOnly)
        // console.log("firstVersionType")
        // console.log(firstVersionType)
      
    // 如果存在version type，说明是实验任务
    if(paramName == "app_type_str"){
      // 如果type是1，说明是版本回归
      if(queryType == 1){
        return true;
      }
      return false;
    }
    if (paramName == "version_type_str"){
      if(queryType == 2){
        return true;
      }
      return false;
    }
    // 如果vitalvisibleonly是false，看当前字段是否是配置内字段
    if (!vitalVisibleOnly){
      console.log("metricsVibible")
      console.log(metricsVibibles)
      if ((metricsVibibles.find((item) => item.metric_key === paramName))){
        return true;
      }
      return false;
      // return true;
    }
    else{
      // 如果只展示重要字段，判断metricsStandard中的metric key字段是否包含paramName
    if (metricsStandards.find((item) => item.metric_key === paramName)){
      
      return true;
    }
    }
    return false;
  };

   // 动态合并单元格的逻辑
  const mergeCells = (value, rowIndex, dataIndex, data) => {
    if (rowIndex === 0 || value !== data[rowIndex - 1][dataIndex]) {
      // 如果是第一行或者当前值和上一行不同，正常显示
      let rowSpan = 1;
      // 计算连续相同的单元格数量
      for (let i = rowIndex + 1; i < data.length; i++) {
        if (data[i][dataIndex] === value) {
          rowSpan++;
        } else {
          break;
        }
      }
      return rowSpan;
    }
    // 如果当前值和上一行相同，则不显示（合并在上一个单元格）
    return 0;
  };
  const initialColumns = [

    {
      title: '用例名称',
      dataIndex: 'case_title',
      fixed: 'left',
      width: 200,
      align: 'center',
      visible: true,
      render: (value,record) => (
              <div style={{ display: 'flex', flexDirection: 'column' }}>
             <span style={{ marginBottom: 8 }}>{record.case_title}</span>
             <Button
              size="small"
              type="text"
              onClick={() => onClickReportDetail(navigate, record) }// 这里还需要修改一下
            >
              查看详情
            </Button>
          </div>
     
        //  {
        // children: (
        //   <div style={{ display: 'flex', flexDirection: 'column' }}>
        //     {/* <span style={{ marginBottom: 8 }}>{value}</span> */}
        //     <Button
        //       size="small"
        //       type="text"
        //       onClick={() => onClickReportDetail(navigate, record) }// 这里还需要修改一下
        //     >
        //       查看详情
        //     </Button>
        //   </div>
        // ),
        // props: {
        //   rowSpan: mergeCells(value, rowIndex, 'case_title',reportDataBySubtask[record.subtask_id] ),
        // },
      // }
      )

    //   render: (value, record) => (
    //     {
    //     children: (
    //       <div style={{ display: 'flex', flexDirection: 'column' }}>
    //         <span style={{ marginBottom: 8 }}>{value}</span>
    //         <Button
    //           size="small"
    //           type="text"
    //           onClick={() => onClickReportDetail(navigate, record) }// 这里还需要修改一下
    //         >
    //           查看详情
    //         </Button>
    //       </div>
    //     ),
    //     // props: {
    //     //   rowSpan: mergeCells(value, rowIndex, 'case_title',reportDataBySubtask[record.subtask_id] ),
    //     // },
    //   }
    // )
    },
    
    {
      title: '执行包体',
      dataIndex: 'app_type_str',
      fixed: 'left',
      width: 200,
      align: 'center',
      visible: judgeVisible('app_type_str',metricsVibible,metricsStandard),
      // render: (value, record, rowIndex) => ({
      //   children: value,
      //   props: {
      //     rowSpan: mergeCells(value, rowIndex, 'app_type_str',reportDataBySubtask[record.subtask_id] ),
      //   },
      // }),
    },
    {
      title: '实验组别',
      dataIndex: 'version_type_str',
      fixed: 'left',
      width: 200,
      align: 'center',
      // 如果record中的version type字段是1，visible为true，如果是2，visible为false
      visible: judgeVisible('version_type_str',metricsVibible,metricsStandard),
      // render: (value, record, rowIndex) => ({
      //   children: value,
      //   props: {
      //     rowSpan: mergeCells(value, rowIndex, 'app_type_str',reportDataBySubtask[record.subtask_id] ),
      //   },
      // }),
    }
  ]
  const[reportColumns,setReportColumns] = useState([]);

  const [reportDataList, setReportDataList] = useState([]);
  const [androidTableData, setAndroidTableData] = useState(reportDataList);
  const [reportName,setReportName] = useState('任务报告详情');
 
  const { email, name } = useUser();
  const[id_data_map,setId_data_map] = useState({});
  const [reportDataBySubtask,setReportDataBySubtask] = useState({});
  const [pass, setPass] = useState(true);  // 报告是否通过,最后的总结论
  const [configData,setConfigData] = useState({});


  

  // 判断字段，为null时显示为/
  const changeNull = (col, record, index, param) => {
    // return <Tag color='green'>/</Tag> 
    if (record[param] === null){
       return <Tag color='gray'>/</Tag> 
    }
    else{
      return <div><Tag color={record[param+'_diff']}>{record['metrics_data'][param]} </Tag>  
      {
        // 如果diff num不为空 ，就显示diff num：如果diff num type是百分比，就显示%，如果是绝对值，就显示数字
      record[param+'_diff_num']?
      
        record[param+'_diff_num_type'] === PERCENTAGE?
        '('+(parseFloat(record[param+'_diff_num'] )).toFixed(2)+'%)':
        '('+parseFloat(record[param+'_diff_num']).toFixed(2)+')'
      
      // '('+parseFloat(record[param+'_diff_num']).toFixed(2)+')'
      : ''
      } </div>
      {/* {record[param+'_diff_num']} */}
     
    }
  }



  
  // 获取版本列表API调用
  const fetchVersionList = async (version) => {
    const response = await BackendApis2.setVersionList({
      payload: { page: 1, page_size: 10, version: version },
    }).finally(() => {});
    setVersionList(response);
  };

  // 获取报告详情API调用
  const fetchReportDetail = async (body) => {
    // setLoading(true);
    const response = await BackendApis2.getReportData({ body }).finally(() => {
      // setLoading(false);
    });
    setReportDataList(response.android_items);
    // setIOSDataList(response.ios_items);
  };



  // 获取任务结果报告（纯数据）,并初步处理
  const fetchReportData = async (body) => {
    const response = await BackendApis2.getPerfDataList({ body }).finally(() => {
      // setLoading(false);
    });
    // 设置本次报告的版本类型
    // setFirstVersionType(response[0].version_type);
    // ————————————————————————————————————————————————————————————————————————————————————————获取配置信息，拿到配置信息和reportcolunm
    let important_metrics = [];
    if(queryConfigId != null && /^\d+$/.test(queryConfigId)){
    let payload = {
      "config_id": queryConfigId
    }
    const config_data = await BackendApis2.configDetail({ payload }).finally(() => {
    })
    // console.log("queryPerfToolType")
    // console.log(typeof(queryPerfToolType))
  //  把config_data.metrics中的metric_category为queryPerfToolType的字段筛选出来，queryPerfToolType字段需转成数字

    let metric_visible = config_data.metrics.filter(item => item.metric_category ===  parseInt(queryPerfToolType));
    // metric_visible = metric_visible.filter(item => item.metric_category === queryPerfToolType);
     setMetricsVibible(metric_visible)
    important_metrics = metric_visible.filter(item => item.is_important === true);
    setMetricsStandard(important_metrics);

    let standard = ``
    for (let i = 0; i < important_metrics.length; i++) {
      
      standard += `-${important_metrics[i].metric_name}:
  ${value_type_list.find(item => item.key === important_metrics[i].value_type).label} ${comparison_type_list.find(item => item.key === important_metrics[i].comparison_type).label}${important_metrics[i].threshold_value}以上  标红;
  `
    }
    setVitalHover(standard)
    // 定义reportcolunm
    let newReportColumns = [...initialColumns];
    for(let i =0; i<metric_visible.length;i++){
      // 如果newReportColumns中的所有元素的dataindex没有response.items[i].metric_key,则增加
      if(newReportColumns.every((item) => item.dataIndex !== config_data.metrics[i].metric_key)){
      newReportColumns =
        [...newReportColumns,
        {
          title: metric_visible[i].metric_name,
          dataIndex: metric_visible[i].metric_key,
          fixed: '',
          width: 200,
          align: 'center',
          visible: judgeVisible(metric_visible[i].metric_key,metric_visible,important_metrics),
          render: (value, record) => (changeNull(value, record, 0, metric_visible[i].metric_key))
        }]
      }
    }
    setReportColumns(newReportColumns);
  }
 // ———————————————————————————————————————————————————————————————————————————————————————— 初步处理，拿到对比数值和对比的颜色
    // let response = response;
    let id_data_map =new Map();
    for (let i = 0; i < response.length; i++) {
      //  在这里判断下platform，如果是android，就把platform改为android，否则就改为ios，修改下
      response[i].platform_str = platform_map.get(response[i].platform)?platform_map.get(response[i].platform):'others';
      response[i].app_type_str = app_type_map.get(response[i].app_type)?app_type_map.get(response[i].app_type):'others';
      response[i].version_type_str = version_type_map.get(response[i].version_type)?version_type_map.get(response[i].version_type):'others';
      if (queryType == 2 && response[i].version_type!= 0){
      // 如果是libra实验任务
              // 这里把系统_子任务id_case id_version_type和数据映射起来，用于后面的数据比对
      id_data_map.set(response[i].platform+'_'+response[i].subtask_id+'_'+response[i].case_id+'_'+response[i].version_type,response[i]);
      }
      else{
      // 在这里创建一个map，把系统_子任务id_case_id_apptype和数据映射起来，用于后面的数据比对
      id_data_map.set(response[i].platform+'_'+response[i].subtask_id+'_'+response[i].case_id+'_'+response[i].app_type,response[i]);
      }
    }
    setId_data_map(id_data_map); 


    if (queryConfigId){
    // 根据response的字段和id_data_map中的映射，得到每个字段, 性能包和基准包的比对情况, 以data的id 记录重要值的变化，每个字段附件一个diff作为记录变化的字段
    for (let i = 0; i < response.length; i++) {
      
        // 当这条记录是版本回归时的性能包；或者是libra实验时的实验组
      if ((response[i].app_type === APP_TYPE_PERF&& response[i].version_type === null) || (response[i].app_type === APP_TYPE_PERF && response[i].version_type === EXPERIMENT_GROUP)) {
        let base_app_id = response[i].platform+'_'+response[i].subtask_id+'_'+response[i].case_id+'_'+APP_TYPE_STANDARD
        // // 如果是实验类型的任务，那么就是实验组比对对照组
        // if (response[i].version_type === EXPERIMENT_GROUP){
        //    base_app_id = response[i].platform+'_'+response[i].subtask_id+'_'+response[i].case_id+'_'+CONTROL_GROUP
        // }
        console.log("base_app_id");
        console.log(base_app_id);
        // 如果对应的基准包存在:
        if (id_data_map.get(base_app_id)){
          // 根据metrics standard中的字段，对性能包和基准包的数据进行比对
          // 增加子任务结论字段，初始是空
          response[i].subtask_conclusion = '';
      
          for (let j = 0; j < important_metrics.length; j++) {
            // console.log("metric一次");
            // console.log(important_metrics[j]["metric_key"]);
            const base_value = id_data_map.get(base_app_id).metrics_data[important_metrics[j]["metric_key"]];
            const perf_value = response[i].metrics_data[important_metrics[j]["metric_key"]];
            // 如果value type是数值比对，则diff是数值；如果是百分比比对，则diff是百分比
            const value_type = important_metrics[j]["value_type"];
            if (value_type === ABSOLUTE){
              response[i][important_metrics[j]["metric_key"]+'_diff_num_type'] = ABSOLUTE;
              response[i][important_metrics[j]["metric_key"]+'_diff_num'] = perf_value - base_value;
            }
            else if (value_type === PERCENTAGE){
              response[i][important_metrics[j]["metric_key"]+'_diff_num_type'] = PERCENTAGE;
              response[i][important_metrics[j]["metric_key"]+'_diff_num'] = ((perf_value - base_value)/base_value)*100;
            }
            // 如果comparison_type是增长，则判断diff-threshold_value是否大于0，大于0则是超过标准，标红； 如果是减少，则判断diff+threshold_value是否小于0，小于0，说明是低于标准，标红；
            const comparison_type = important_metrics[j]["comparison_type"];
            const threshold_value = important_metrics[j]["threshold_value"];
            if (comparison_type === INCREASE){
              if (response[i][important_metrics[j]["metric_key"]+'_diff_num'] - threshold_value >0){
                response[i][important_metrics[j]["metric_key"]+'_diff'] = 'red';
                setPass(false);
              }
              else{
                response[i][important_metrics[j]["metric_key"]+'_diff'] = 'green';
              }
            }
            else if (comparison_type === DECREASE){
              if (response[i][important_metrics[j]["metric_key"]+'_diff_num'] + threshold_value < 0){
                response[i][important_metrics[j]["metric_key"]+'_diff'] ='red';
                setPass(false);

              }
              else{
                response[i][important_metrics[j]["metric_key"]+'_diff'] = 'green';
              }
            }
            // 如果标红了，就增加conclusion字段，记录结论
            if (response[i][important_metrics[j]["metric_key"]+'_diff'] === 'red'){
              // 如果是百分比类型比较，就乘以100
              if (value_type === PERCENTAGE){
                response[i].subtask_conclusion += important_metrics[j]["metric_name"];
                response[i].subtask_conclusion += '：';

                response[i].subtask_conclusion += (response[i][important_metrics[j]["metric_key"]+'_diff_num']).toFixed(2);
                response[i].subtask_conclusion += '% 不通过;  ';
              }
              //  如果不是百分比类型比较，就直接比对
              else{
                response[i].subtask_conclusion += important_metrics[j]["metric_name"];
                response[i].subtask_conclusion += '：';
                response[i].subtask_conclusion += (response[i][important_metrics[j]["metric_key"]+'_diff_num']).toFixed(2);
                response[i].subtask_conclusion += ' 不通过 ;   ';
              }
              
            }
          }
        }
      }
      console.log("response 排查")
      console.log(response[i])
    }
      
    }
    // const processed_data = processData(response);
    console.log('processed_data');
    console.log(response);
    setReportDataList(response);
// ————————————————————————————————————————————————————————————————————————————初步处理结束，拿到了初步处理的数据

// ——————————————————————————————————————————————————————————————————————————————分组数据，把数据按照subtask_id分组
    groupDataBySubtask(response,important_metrics);
    // 分组完后，每组子任务分别出子结论
    Message.success("加载成功")

  };

//初步处理数据，把数据的比较和标红等颜色处理了，需要在这里修改适配报告
  const processData =  (data) => {
    const response = data;
    const id_data_map =new Map();
    for (let i = 0; i < response.length; i++) {
      //  在这里判断下platform，如果是android，就把platform改为android，否则就改为ios，修改下
      // 在这里创建一个map，把系统_子任务id_case id_apptype和数据映射起来，用于后面的数据比对
      response[i].platform_str = platform_map.get(response[i].platform)?platform_map.get(response[i].platform):'others';
      response[i].app_type_str = app_type_map.get(response[i].app_type)?app_type_map.get(response[i].app_type):'others';
      // switch (response[i].platform) {
      //   case 1:
      //     response[i].platform_str = 'Android';
      //     break;
      //   case 2:
      //     response[i].platform_str = 'iOS';
      //     break;
      //   default:
      //     response[i].platform_str = 'others';
      // };
      // switch(response[i].app_type){
      //   case 1:
      //     response[i].app_type_str = '性能包';
      //     break;
      //   case 2:
      //     response[i].app_type_str = '基准包';
      //     break;
      //   case 3:
      //     response[i].app_type_str = '辅助包';
      //     break;
      //   default:
      //     response[i].app_type_str = 'others';
      // }
      // 把id_data_map的id和数据映射起来
      id_data_map.set(response[i].platform+'_'+response[i].subtask_id+'_'+response[i].case_id+'_'+response[i].app_type,response[i]);
    
    }
    setId_data_map(id_data_map); 
    // 根据response的字段和iddatamap中的映射，得到每个字段, 性能包和基准包的比对情况, 以data的id 记录重要值的变化，每个字段附件一个diff作为记录变化的字段
    for (let i = 0; i < response.length; i++) {
      if (response[i].app_type === APP_TYPE_PERF) {
        //  基准包的标识：
        const base_app_id = response[i].platform+'_'+response[i].subtask_id+'_'+response[i].case_id+'_'+APP_TYPE_STANDARD
        // 如果对应的基准包存在:
        if (id_data_map.get(base_app_id)){
          // 根据metrics standard中的字段，对性能包和基准包的数据进行比对
          console.log('metricsStandard')
          console.log(metricsStandard)
          // let payload = {
          //   "config_id": queryConfigId
          // }
          // const response = await BackendApis2.configDetail({ payload }).finally(() => {
            
          // })
          // let important_metrics = response.config.metrics.filter(item => item.is_important === true);
          // setMetricsStandard(important_metrics);
        
          for (const metric in metricsStandard) {
            const base_value = id_data_map.get(base_app_id).metrics_data[metric["metric_key"]];
            const perf_value = response[i].metrics_data[metric["metric_key"]];
            // 如果value type是数值比对，则diff是数值；如果是百分比比对，则diff是百分比
            const value_type = metric["value_type"];
            if (value_type === ABSOLUTE){
              response[i][metric["metric_key"]+'_diff_num'] = perf_value - base_value;
            }
            else if (value_type === PERCENTAGE){
              response[i][metric["metric_key"]+'_diff_num'] = (perf_value - base_value)/base_value;
            }
            // 如果comparison_type是增长，则判断diff-threshold_value是否大于0，大于0则是超过标准，标红； 如果是减少，则判断diff+threshold_value是否小于0，小于0，说明是低于标准，标红；
            const comparison_type = metric["comparison_type"];
            const threshold_value = metric["threshold_value"];
            if (comparison_type === INCREASE){
              if (response[i][metric["metric_key"]+'_diff_num'] - threshold_value >0){
                response[i][metric["metric_key"]+'_diff'] = 'red';
              }
              else{
                response[i][metric["metric_key"]+'_diff'] = 'green';
              }
            }
            else if (comparison_type === DECREASE){
              if (response[i][metric["metric_key"]+'_diff_num'] + threshold_value < 0){
                response[i][metric["metric_key"]+'_diff'] ='red';
              }
              else{
                response[i][metric["metric_key"]+'_diff'] = 'green';
              }
            }
          }
    }
    }
  }
    return response;
  }

  // 通过子任务，把数据分组，用于表格分开展示.并得到子任务
  const groupDataBySubtask = (data,important_metrics) => {
    const groupedData = {};
    for (let i = 0; i < data.length; i++) {
      // 如果子任务没有数组，就创建一个空数组
      if (!groupedData[data[i].subtask_id]) {
        groupedData[data[i].subtask_id] = [];
      }
      groupedData[data[i].subtask_id].push(data[i]);
    }

    setReportDataBySubtask(groupedData);
  }

  const getSubClusion = (subTaskData) => {
    let conclusion = '';
    for (let i = 0; i < subTaskData.length; i++) {
      let case_conclusion = '';
      // 如果是基准包，跳过
      if (subTaskData[i].app_type === APP_TYPE_STANDARD) {
        continue;
      }
      case_conclusion += subTaskData[i].subtask_conclusion;
    
    
    
      if (case_conclusion !==''){
        case_conclusion = subTaskData[i].case_title +' 测试结果:'+ case_conclusion;
      }
        else{
        case_conclusion = subTaskData[i].case_title +' 测试结果一切正常';
      }
      conclusion += case_conclusion;
      conclusion += '\n';
    }
    return conclusion
  }
// 切换是否展示重要字段
const changeVitalVisibleOnly = () => {
  setVitalVisible(!vitalVisibleOnly);
}

// 点击进入报告详情
const onClickReportDetail = (navigate,record) => {
  navigate(`/perf-ui-auto/report/detail?app_id=${record.app_id}&case_id=${record.case_id}&sub_task_id=${record.subtask_id}&app_type=${record.app_type}&platform=${record.platform}&case_title=${record.case_title}&perf_tool_type=${queryPerfToolType}&config_id=${queryConfigId}`,{state:{record:record}});
  // navigate(`/perf-ui-auto/report/detail`, { state: { data: { app_id: record.app_id,case_id:record.case_id,sub_task_id:record.subtask_id,app_type:record.app_type,platform:record.platform,case_title:record.case_title} } });
  // Message.info("查看详情"+ record.case_title)
};

// 根据当前端是安卓还是ios，判断此字段是否是该端无用的字段，无须展示
const judge_platform_para_visible = (platform,para) => {
  if (commonParaList.includes(para)){
    return true;
  }
  // return true
  console.log("metric_now")
  console.log(metricsVibible)
  if (platform === 1) {
    // 任务类型是安卓，判断当前字段在config_detail中的metric_type是1还是2
    for (let i = 0; i < metricsVibible.length; i++) {
      if (metricsVibible[i].metric_key === para){
        if (metricsVibible[i].metric_type === 1 || metricsVibible[i].metric_type === 2){
          return true;
        }
      }
    }
     return false
    }
  if (platform === 2){
    for (let i = 0; i < metricsVibible.length; i++) {
      if (metricsVibible[i].metric_key === para){
        if (metricsVibible[i].metric_type === 1 || metricsVibible[i].metric_type === 3){
          return true;
        }
      }
    }
  return false;
}
  return true;
}
  // 搜索版本 300ms 防抖时间
  const debouncedFetchVersionList = useCallback(
    debounce((query) => {
      fetchVersionList(query);
    }, 300),
    []
  );

 

  // 更新状态的函数
  function handleFromValuesChange(value, fieldName) {
    const updatedFormValues = {
      ...formValues,
      [fieldName]: value,
    };
    setFormValues(updatedFormValues);
    fetchReportDetail(updatedFormValues);
  }

  // 版本搜索
  const handleVersionSearch = (value) => {
    setVersionList(value);
    debouncedFetchVersionList(value);
  };

  // 分页切换 不要换
  function handlerTableChange(pagination) {
    const { current, pageSize } = pagination;
    setLoading(true);
    setTimeout(() => {
      setAndroidTableData(androidTableData.slice((current - 1) * pageSize, current * pageSize));
      setPagination((pagination) => ({ ...pagination, current, pageSize }));
      setLoading(false);
    }, 1000);
  };
  // 回退到上个页面的逻辑
const handleBack = () => {
  navigate(-1);

}
// 获取对应的配置，请求配置详情接口
const fetchConfigDetail = async (config_id) => {
  let payload = {
    "config_id": config_id
  }
  const response = await BackendApis2.configDetail({ payload }).finally(() => {

  })
  setMetricsVibible(response.metrics)
  const important_metrics = response.metrics.filter(item => item.is_important === true)
  setMetricsStandard(important_metrics);
  let standard = ``
  for (let i = 0; i < important_metrics.length; i++) {
    
    standard += `-${important_metrics[i].metric_name}:
 ${value_type_list.find(item => item.key === important_metrics[i].value_type).label} ${comparison_type_list.find(item => item.key === important_metrics[i].comparison_type).label}${important_metrics[i].threshold_value}以上  标红;
 `
  }
  setVitalHover(standard)
}



  // 初始化数据
  useEffect(() => {
    setLoading(true)
     if (queryTaskId != null && /^\d+$/.test(queryTaskId)) {
      Message.info("加载中")
      fetchReportData({"id":queryTaskId})
        } 
    else {
          Message.warning('任务id不存在或非法');
        }
    setLoading(false);
  }, [vitalVisibleOnly]);
  useEffect(() => {
    setReportName(queryTaskName)

  }, [])
  // 页面
  return (
    <Card bordered={false}>
      {
      }
      <Row>
            <Button style={{ float: 'left', width: '60x' }}
             type="text"  
            icon={<IconArrowLeft />}
            onClick={() => handleBack()}
            >
                返回
              </Button>
      </Row>
     
       <Switch  style={{ float: 'right', width: '60px' }}
                checked={vitalVisibleOnly}
                onChange={changeVitalVisibleOnly}
                checkedText='ON' uncheckedText='OFF'
            />    
             <div style={{ position: 'relative', display: 'inline - block' }}>
            <label style={{ float: 'right' }}>
                {"只展示重要字段 "}
            </label>
            <Popover
        trigger='hover'
        title='重要字段比对标准'
        content={
          <span>
            <pre style={{whiteSpace: 'pre - wrap'}} > {vitalHover} </pre>
            <p ></p>
          </span>
        }
      >
        <IconQuestionCircle style={{fontSize: 15,position: 'absolute',color: 'gray',float:"right",
            top: 0,right: 160}}/>
      </Popover>
            
            </div>
      <Title heading={3} style={{marginTop: 0,marginLeft:15 }}>
      {"任务 " + reportName+" 性能报告结论: " + (pass ? '通过' : '不通过')}
          {/* 这里假设使用了一个自定义的图标组件，你可以替换为实际的图标组件，如来自Material - UI或Ant Design等库的图标 */}
          
      
      </Title>
{/*       
      <div>
      {Object.keys(reportDataBySubtask).map((subtask_id, index) => (
        <div>
      
        </div>
      ))}
      </div> */}
      <div>
          {Object.keys(reportDataBySubtask).map((subtask_id, index) => (

              <div key={subtask_id}>
                  <h3>  {"子任务 " + reportDataBySubtask[subtask_id][0]['subtask_name'] + " 测试结论如下："}</h3>
                  <h4 style={{whiteSpace: 'pre-wrap'}}> {getSubClusion(reportDataBySubtask[subtask_id])}</h4>
              <h2 style={{ marginTop: 0, color: 'black'}}>
                {reportDataBySubtask[subtask_id][0]['subtask_name'] + ' 子任务性能报告详情 （' +reportDataBySubtask[subtask_id][0]['platform_str']+'）'}
              </h2>
              <Table
                border={true} // 显示表格外部边框
                borderCell={true} // 显示单元格的内部边框
                hover={false} // 鼠标悬停时显示高亮效果
                stripe={true} // 显示斑马纹效果
                scroll={{ x: true }} // 设置表格的横向滚动
                loading={loading} // 显示加载状态
                columns={reportColumns.filter((reportColumn) => reportColumn.visible).filter((reportColumn) => judge_platform_para_visible(reportDataBySubtask[subtask_id][0]['platform'],reportColumn.dataIndex))} // 表格的列配置
                data={reportDataBySubtask[subtask_id] || {}} // 表格的数据源,如果是undefined，则为空列表
                onChange={handlerTableChange}
                pagination={pagination}
   
              />
            </div>
              
          ))}
        </div>
    </Card>
  );
}
