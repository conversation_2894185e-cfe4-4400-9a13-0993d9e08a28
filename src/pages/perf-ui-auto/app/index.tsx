import React, { useCallback, useEffect, useState } from 'react';
import { IconList, IconEdit } from '@arco-design/web-react/icon';
import debounce from 'lodash/debounce';
import { Button, Card, Form, Grid, Select, Table, Typography, Modal, Radio, Input, Message, Link, Tabs, Tooltip,Popconfirm,Space,Image,Alert } from '@arco-design/web-react';
import Builder from '@devsre/builder';
import type { PaginationProps } from '@arco-design/web-react';
import { BackendApis2 } from 'src/utils/backendApis2';
import config from './config.json';
const Option = Select.Option;
const { Title } = Typography;
const { Row, Col } = Grid;
const RadioGroup = Radio.Group;
const TabPane = Tabs.TabPane;
import { useUser } from '@devsre/react-utils';
import { developer_list } from 'src/utils/const';
import { useLocation, useNavigate } from 'react-router-dom';


export default function CurdCard() {
    // const navigate = useNavigate();
    const location = useLocation();
    const { email, name } = useUser();
  
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [businessList, setBusinessList] = useState([])
  const [versionList, setVersionList] = useState([]);
  const [searchFormValues, setSearchFormValues] = useState({}); //查询app/app组的条件
  const [activeTabKey, setActiveTabKey] = useState('1');

  const PLATFORM_ANDROID = 1
  const PLATFORM_IOS = 2
  const APP_TYPE_PERF = 1
  const APP_TYPE_STANDARD = 2
  const APP_TYPE_ASSIST = 3

  // 弹窗类型（添加/编辑）
  const DIALOG_TYPE_ADD = 1
  const DIALOG_TYPE_EDIT = 2

  // App tab--start
  const [appTabList, setAppTabList] = useState([]);
  // const [copyAppName, setCopyAppName] = useState('');


  // 分页器
  const [appPagination, setAppPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 20,
    current: 1,
    total: 10,
    pageSizeChangeResetCurrent: true,
  });
  // 获取app列表
  const fetchAppTabList = async (pagination,body) => {
    setLoading(true)
    const payload = {
      'page': pagination.current,
      'page_size': pagination.pageSize
    };
    const response = await BackendApis2.getAppList({ body,payload }).finally(() => {
      setLoading(false)
      
    });
    setAppTabList(response.items);
    // appPagination.current = response.page;
    //   appPagination.pageSize = response.page_size;
      appPagination.total = response.total;
      setAppPagination(appPagination);
  };

  // 根据业务/版本搜索app
  const handleAppTabSearch = (value, fieldName) => {
    const updatedFormValues = {
      ...searchFormValues,  // 历史的搜索信息
      [fieldName]: value,
    };
  
    setSearchFormValues(updatedFormValues)
    // 需要设置localstate，值筛选器参数
    localStorage.setItem('appListSearchFormValues', JSON.stringify(updatedFormValues))
    fetchAppTabList(appPagination,updatedFormValues);
  };
  // App tab--end

  // App组 tab--start 
  const [appGroupTabList, setAppGroupTabList] = useState([]);

  // app组的分页器
  const [appGroupPagination, setAppGroupPagination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 20,
    current: 1,
    total:20,
    pageSizeChangeResetCurrent: true,
  });
   const onChangeAppGroup = (pagination: PaginationProps) => {
      setAppGroupPagination(pagination);
    };
  // App组 tab--end 

  // tab 切换
  function onTabChange(tabKey) {
    setActiveTabKey(tabKey);
    if (tabKey == 1) {  //app tab
      fetchAppTabList(appPagination,searchFormValues)
    } else {  //app group tab
      fetchAppGroupTabList(appGroupPagination,searchFormValues);
    }
  }

  // App组详情弹窗相关常量--start
  const [appGroupDetailLoading, setAppGroupDetailLoading] = useState(false);
  const [showAppGroupDetailDialog, setShowAppGroupDetailDialog] = useState(false);

  const [appGroupDetailDialogData, setAppGroupDetailDialogData] = useState({
    id: 0,
    business_id: 0,
    name: '',
    version: '',
    create_time: '',
    update_time: '',
    android_perf_app_id_list: [],
    android_assist_app_id_list: [],
    ios_perf_app_id_list: [],
    ios_assist_app_id_list: [],
  });

  // 获取App组详情
  const fetchAppGroupDetail = async (id) => {
    setAppGroupDetailLoading(true)
    const response = await BackendApis2.getAppGroupDetail({
      payload: { app_group_id: id }
    }).finally(() => {
      setAppGroupDetailLoading(false)
    });
    setAppGroupDetailDialogData(response);
  };
  // 关闭弹窗
  function handleCloseAppGroupDetailDialog() {
    setShowAppGroupDetailDialog(false)
  }
  // App详情弹窗相关常量--end

  // 添加/编辑App弹窗相关常量--start
  const [appDialogType, setAppDialogType] = useState(1);
  const [addOrEditAppForm] = Form.useForm();
  const [dialogLastAddValue, setDialogLastAddValue] = useState({});
  const [showAddOrEditAppDialog, setShowAddOrEditAppDialog] = useState(false);
  const [jenkinsBuildResultUrlVisible, setJenkinsBuildResultUrlVisible] = useState(false);
  const [repackageCertVisible, setRepackageCertVisible] = useState(false);
  jenkinsBuildResultUrlVisible
  const platformList = [
    { key: PLATFORM_ANDROID, label: 'Android' },
    { key: PLATFORM_IOS, label: 'ios' }
  ];
  const repackageCertList = [
    {
      key: 'https://tosv.boe.byted.org/obj/global-rtc-test-platform/certificates/com.zhiliaoapp.musically/musically.mobileprovision',
      label: 'TikTok'
    }
  ];

  // 添加app接口
  const addApp = async (body) => {
    const response = await BackendApis2.addApp({ body }).finally(() => { });
    if (response.code == 200) {
      Message.success('添加App成功');
      handleCloseAddOrEditAppDialog(body)
      fetchAppTabList(appPagination,searchFormValues)
      fetchVersionList();
    }
    else
      Message.error('添加App失败');
  };

  // 更新app接口
  const updateApp = async (body) => {
    const response = await BackendApis2.updateApp({ body }).finally(() => { });
    if (response.code == 200) {
      Message.success('编辑App成功');
      handleCloseAddOrEditAppDialog()
      fetchAppTabList(appPagination,searchFormValues)
      fetchVersionList();
    }
    else
      Message.error('编辑App失败');
  };

  // app列表的分页实现
  function handlerAppTableChange(pagination) {
    const { current, pageSize } = pagination;
    setLoading(true);
    setTimeout(() => {
      // setAndroidTableData(androidTableData.slice((current - 1) * pageSize, current * pageSize));
      setAppPagination((pagination) => ({ ...pagination, current, pageSize }));
      setLoading(false);
    }, 1000);
  };

  // form取值变化
  function handleAddOrEditAppParamsChange(value, values) {
    if ('platform' in value) {
      if (value.platform == PLATFORM_ANDROID) { //android 控制必选
        setJenkinsBuildResultUrlVisible(true)
        setRepackageCertVisible(false)
        addOrEditAppForm.resetFields('repackage_cert')// 清空iOS的重打包文件
      }
      else if (value.platform == PLATFORM_IOS) { //ios
        setJenkinsBuildResultUrlVisible(false)
        setRepackageCertVisible(true)
        addOrEditAppForm.resetFields('jenkins_build_result_url')
      }
    }


    const body_form = {
      // version:addOrEditAppForm.getFieldValue("version"),
      business_id:addOrEditAppForm.getFieldValue("business_id")
    };
    if ('business_id' in value) {//业务变化，请求版本列表
      fetchVersionListAppGroup({ payload: {page:1,page_size:10, business_id: value.business_id} })
      
      // fetchAddOrEditAppGroupAppList(body_form)
    }
  }
  // 编辑app按钮 控制混淆文件和重打包文件的显示和可否编辑
  const handleFileVisibleONEditStatus = () => {
      
        // addOrEditAppForm.getFieldValue("platform")
        if (addOrEditAppForm.getFieldValue("platform") == PLATFORM_ANDROID) { //android 控制必选
          setJenkinsBuildResultUrlVisible(true)
          setRepackageCertVisible(false)
          // addOrEditAppForm.resetFields('repackage_cert')// 清空iOS的重打包文件
        }
        else if (addOrEditAppForm.getFieldValue("platform") == PLATFORM_IOS) { //ios
          setJenkinsBuildResultUrlVisible(false)
          setRepackageCertVisible(true)
          // addOrEditAppForm.resetFields('jenkins_build_result_url')
        }
        else{
          setJenkinsBuildResultUrlVisible(false)
          setRepackageCertVisible(false)
        }
      

  }

  // 提交弹窗
  const handleSubmitAddOrEditAppDialog = async (value) => {
    const body = {
      ...value,
      "creator":name
    }
    try {
      console.log("handleSubmitAddOrEditAppDialog value")
      console.log(body)
      await addOrEditAppForm.validate();
      if (appDialogType == DIALOG_TYPE_ADD) {
        // Message.info('添加');
        addApp(body)
      } else {
        // Message.info('更新');
        updateApp(body)
      }
    } catch (e) {
      Message.error('校验失败');
    }
  }

  // 关闭弹窗
  function handleCloseAddOrEditAppDialog(dialogLastAddValue={}) {
    addOrEditAppForm.resetFields();
    setDialogLastAddValue(dialogLastAddValue)
    setShowAddOrEditAppDialog(false)
  }
  // 添加/编辑App弹窗相关常量--end


  // 添加/编辑App组弹窗相关常量--start
  const [appGroupDialogType, setAppGroupDialogType] = useState(1);
  const [addOrEditAppGroupForm] = Form.useForm();
  const [showAddOrEditAppGroupDialog, setShowAddOrEditAppGroupDialog] = useState(false);
  const [addOrEditAppGroupAppList, setAddOrEditAppGroupAppList] = useState([]);
  const [addOrEditVersionList,setAddOrEditVersionList] = useState([]);

  // 获取添加/编辑app组弹窗的app列表
  const fetchAddOrEditAppGroupAppList = async (body,version=null) => {
    const body_form = {
      version:addOrEditAppGroupForm.getFieldValue("version"),
      business_id:addOrEditAppGroupForm.getFieldValue("business_id")
    };
    // if (addOrEditAppGroupForm.getFieldValue("version")!=undefined){
    //   body.version = addOrEditAppGroupForm.getFieldValue("version")
    // }
    const payload = {
      'page': appPagination.current,
      'page_size': appPagination.pageSize
    };

    const response = await BackendApis2.getAppList({ body,payload }).finally(() => {
    });
    setAddOrEditAppGroupAppList(response.items);
  };

  // 获取添加/编辑app组弹窗的版本列表
  const fetchVersionListAppGroup = async (payload) => {
    console.log('business_id:')
      console.log(payload)
    const response = await BackendApis2.getAppVersionList( payload ).finally(() => { });
    setVersionList(response);
  };

  // 添加app组接口
  const addAppGroup = async (body) => {
    const response = await BackendApis2.addAppGroup({ body }).finally(() => { });
    if (response.code == 200) {
      Message.success('添加App组成功');
      handleCloseAddOrEditAppGroupDialog()
      fetchAppGroupTabList(appGroupPagination,searchFormValues);
    }
    else
      Message.error('添加App组失败');
  };

  // 添加app组接口
  const updateAppGroup = async (body) => {
  
    const response = await BackendApis2.updateAppGroup({ body }).finally(() => { });
    if (response.code == 200) {
      Message.success('编辑App组成功');
      handleCloseAddOrEditAppGroupDialog()
      fetchAppGroupTabList(appGroupPagination,searchFormValues);
    }
    else
      Message.error('编辑App组失败');
  };

  // app组列表的分页实现
  function handlerAppGroupTableChange(pagination: PaginationProps) {
    const { current, pageSize } = pagination;
    setLoading(true);
    setTimeout(() => {
      // setAndroidTableData(androidTableData.slice((current - 1) * pageSize, current * pageSize));
      setAppGroupPagination((pagination) => ({ ...pagination, current, pageSize }));
      setLoading(false);
    }, 1000);
    setAppGroupPagination(pagination);

  };

  
  // form取值变化
  function handleAddOrEditAppGroupParamsChange(value, values) {
    const body_form = {
      version:addOrEditAppGroupForm.getFieldValue("version"),
      business_id:addOrEditAppGroupForm.getFieldValue("business_id")
    };
    if ('business_id' in value) {//业务变化，就请求一次app列表，根据业务id.3月10日编辑：需要额外增加额外请求版本列表
      
      fetchVersionListAppGroup({ payload: {page:1,page_size:10, business_id: value.business_id} })
      
      fetchAddOrEditAppGroupAppList(body_form)
    }
    if('version' in value){ //版本变化，就请求一次app列表
      fetchAddOrEditAppGroupAppList(body_form)
      // fetchVersionListAppGroup({ payload: {page:1,page_size:10, business_id: value.business_id,version:value.version } })
    }
  }
  

  function perfAppValidator(value, callback) {
    console.log('android_perf_app_id_list')
    console.log(addOrEditAppGroupForm.getFieldValue("android_perf_app_id_list"))
    console.log('ios_perf_app_id_list')
    console.log(addOrEditAppGroupForm.getFieldValue("ios_perf_app_id_list"))
    if (addOrEditAppGroupForm.getFieldValue("android_perf_app_id_list")?.length>0 || addOrEditAppGroupForm.getFieldValue("ios_perf_app_id_list")?.length>0) {
      callback();
    } else {
      callback(new Error('Android/ios性能测试包必选其一'));
    }
  }
  

  // 提交弹窗
  const handleSubmitAddOrEditAppGroupDialog = async (value) => {
    const body = {
      ...value,
      "creator":name
    }
    try {
      console.log("handleSubmitAddOrEditAppGroupDialog value")
      console.log(body)
      await addOrEditAppGroupForm.validate();
      if (appGroupDialogType == DIALOG_TYPE_ADD) {
        // Message.info('添加');
        addAppGroup(body)
      } else {
        // Message.info('更新');
        updateAppGroup(body)
      }
    } catch (e) {
      Message.error('校验失败');
    }
  }

  // 关闭弹窗
  function handleCloseAddOrEditAppGroupDialog() {
    addOrEditAppGroupForm.resetFields();
    setAddOrEditAppGroupAppList([]);
    setVersionList([]);
    setShowAddOrEditAppGroupDialog(false)
  }
  // 添加App组弹窗相关常量--end

  // 获取业务列表
  const fetchBusinessList = async () => {
    const response = await BackendApis2.fetchBusinessList().finally(() => { });
    setBusinessList(response.items);
  };


  // 获取版本列表API调用
  const fetchVersionList = async (version = null) => {
    let payload = { page: 1, page_size: 50 }
    if (version != null) {
      const payload = { page: 1, page_size: 50, version: version,businessList:addOrEditAppForm.getFieldValue("business_id") };
    }
    const response = await BackendApis2.getAppVersionList({ payload }).finally(() => { });
    setVersionList(response);
  };

  // 搜索版本 300ms 防抖时间
  const debouncedFetchVersionList = useCallback(
    debounce((query) => {
      fetchVersionList(query);
    }, 300),
    []
  );

  // 版本搜索
  const handleVersionSearch = (value) => {
    setVersionList(value);
    debouncedFetchVersionList(value);
  };


  // 获取app组
  const fetchAppGroupTabList = async (pagination,body) => {
    setLoading(true)
    // const payload = {
    //   'page': appGroupPagination.current,
    //   'page_size': appGroupPagination.pageSize
    // }
    const payload = { 'page': pagination.current, 'page_size': pagination.pageSize };
    const response = await BackendApis2.getAppGroupList({ payload, body }).finally(() => {
      setLoading(false)
    });
    setAppGroupTabList(response.items);
    // appGroupPagination.current = response.page;
    // appGroupPagination.pageSize = response.page_size;
    appGroupPagination.total = response.total;
    setAppGroupPagination(appGroupPagination)
  };

  // 根据业务/版本搜索app组
  const handleAppGroupSearch = (value, fieldName) => {
    const updatedFormValues = {
      ...searchFormValues,
      [fieldName]: value,
    };
    setSearchFormValues(updatedFormValues)
    // 需要设置localstate，值筛选器参数
    localStorage.setItem('appListSearchFormValues', JSON.stringify(updatedFormValues))
    fetchAppGroupTabList(appGroupPagination,updatedFormValues);
  };

  // 展示App组详情弹窗
  function onClickAppGroupDetail(appGroupData) {
    setShowAppGroupDetailDialog(true);
    setAppGroupDetailDialogData(appGroupData);
    fetchAppGroupDetail(appGroupData.id);
  }

  // 展示编辑App弹窗
  function onClickEditApp(appData) {
    console.log("onClickEditApp appData")
    console.log(appData)
    addOrEditAppForm.setFieldsValue(appData)
    // addOrEditAppForm.setFieldValue("id",appData.id)
    console.log(addOrEditAppForm.getFieldsValue())
    setAppDialogType(DIALOG_TYPE_EDIT)
    setShowAddOrEditAppDialog(true)
    handleFileVisibleONEditStatus()

    // Message.info("暂未完成")
  }


 // 复制app
  const onCopyApp = async(record) => {
    // 在这里处理复制操作
    // console.log('复制操作:', record);
    const body = {
    ...record,
      "creator":name,
      "name":record.name + "_副本"
    }
    console.log("body")
    console.log(body)
    const response = await BackendApis2.addApp({body}).finally(() => {
    })
     // 刷新配置列表
     fetchAppTabList(appPagination,searchFormValues);
  }


  // 删除app
  const onClickDeleteApp = async (record) => {
    // Message.info("删除")
     const payload = { "app_id": record.id }
        const response = await BackendApis2.deleteApp({ payload }).finally(() => {
        });
        if (response.code != 200) {
          Message.error("删除失败")
        } else {
          Message.success("删除成功")
          fetchAppTabList(appPagination,searchFormValues)
        }
  }

  // 展示编辑App组弹窗
  function onClickEditAppGroup(appGroupData) {
    console.log("onClickEditAppGroup appGroupData")
    console.log(appGroupData)
    addOrEditAppGroupForm.setFieldsValue(appGroupData)
    fetchAddOrEditAppGroupAppList({"business_id":appGroupData.business_id})
    // console.log(addOrEditAppGroupForm.getFieldsValue())
    setAppGroupDialogType(DIALOG_TYPE_EDIT)
    setShowAddOrEditAppGroupDialog(true)
    // Message.info("暂未完成")
  }
  // 删除app组
  const onClickDeleteAppGroup = async (record) => {
    // Message.info("删除")
     const payload = { "app_group_id": record.id }
        const response = await BackendApis2.deleteAppGroup({ payload }).finally(() => {
        });
        if (response.code!= 200) {
          Message.error("删除失败")
        } else {
          Message.success("删除成功")
          fetchAppGroupTabList(appGroupPagination,searchFormValues)
        }
  }

  // 展示添加App弹窗
  function onClickAddApp() {
    setAppDialogType(DIALOG_TYPE_ADD) // 定义是添加还是编辑弹窗
    for(const key in dialogLastAddValue){   
      if (["business_id","platform","version"].includes(key)){
        addOrEditAppForm.setFieldValue(key,dialogLastAddValue[key])
      }
    }
    console.log("addOrEditAppForm")
    console.log(addOrEditAppForm.getFieldsValue())
    setShowAddOrEditAppDialog(true)
    handleFileVisibleONEditStatus()
  }

  // 展示添加App组弹窗
  function onClickAddAppGroup() {
    setAppGroupDialogType(DIALOG_TYPE_ADD)
    setShowAddOrEditAppGroupDialog(true)
    addOrEditAppGroupForm.setFieldsValue(
      {
       "android_perf_app_id_list":[], 
       "android_assist_app_id_list":[], 
       "ios_perf_app_id_list":[], 
       "ios_assist_app_id_list":[] 
      }
    )
  }

  // 初始化数据
  useEffect(() => {
    fetchBusinessList();
    fetchVersionList();
  // 先判断localstate中是否有筛选参数，有的话，设置筛选参数
  console.log('appListSearchFormValues');
  const storedFilter = localStorage.getItem('appListSearchFormValues')
  console.log(storedFilter)
  if (location.state) {
    setActiveTabKey('2')
    fetchAppGroupTabList(appGroupPagination,{
      "app_group_name":location.state.data.app_group_name
    })
   }
   else{
    fetchAppGroupTabList(appGroupPagination,searchFormValues)
   }

   
  if (storedFilter) {
    form.setFieldsValue(JSON.parse(storedFilter))
    setSearchFormValues(JSON.parse(storedFilter))
    fetchAppTabList(appPagination,JSON.parse(storedFilter))
  }
  else{
      fetchAppTabList(appPagination,searchFormValues)
    }
    // 如果是通过点击进入
  
    // fetchAppTabList(searchFormValues)
  }, [appPagination.current, appPagination.pageSize,appGroupPagination.current, appGroupPagination.pageSize]);

  // app列表页表格的列配置
  const appColumns = [
    {
      title: 'id', dataIndex: 'id', width: '3%', ellipsis: true
    },
    {
      title: '业务', dataIndex: 'business_name', width: '5%', ellipsis: true
    },
    {
      title: 'APP名称', dataIndex: 'name', width: '15%', ellipsis: true
    },
    // {
    //   title: '平台', dataIndex: 'platform', width: '10%', ellipsis: true
    // },
    // {
    //   title: '版本', dataIndex: 'version', width: '10%', ellipsis: true
    // },
    // {
    //   title: '包类型', dataIndex: 'app_type', width: '10%', ellipsis: true
    // },
    {
      title: 'APP链接', dataIndex: 'url', width: '5%', ellipsis: true,
      render: (col, record, index) => (
        <Link href={record.url}>
          <Tooltip mini content={record.url}>链接</Tooltip>
        </Link>
      ),
    },
    {
      title: 'mapping文件链接', dataIndex: 'jenkins_build_result_url', width: '7%', ellipsis: true,
      render: (col, record, index) => (
        <span>{
          (record.platform == PLATFORM_ANDROID) ?
            <Link href={record.jenkins_build_result_url}>
              <Tooltip mini content={record.jenkins_build_result_url}>链接</Tooltip>
            </Link> :
            <span style={{ color: 'gray' }}>ios无需关注</span>
        }
        </span>
      ),
    },
    {
      title: '重打包证书', dataIndex: 'repackage_cert', width: '7%', ellipsis: true,
      render: (col, record, index) => (
        <span>{
          (record.platform == PLATFORM_IOS) ?
            <Link href={record.repackage_cert}>
              <Tooltip mini content={record.repackage_cert}>链接</Tooltip>
            </Link> :
            <span style={{ color: 'gray' }}>Android无需关注</span>
        }
        </span>
      ),
    },
    {
      title: '创建人', dataIndex: 'creator', width: '7%', ellipsis: true,
      render: (col, record, index) => (
        <Space>
          <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.creator}?format=40x40.png`}></Image>
          <Tooltip mini content={record.creator}>
            {/* {record.owner.length > 18 ? record.owner.substring(0, 18) + '...' : record.owner} */}
            <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{record.creator}</div>
          </Tooltip>
        </Space>
      )
    },
    {
      title: '创建时间', dataIndex: 'create_time', width: '8%', ellipsis: true,
      render: (text) => (
        <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>
      )
    },
    {
      title: '操作', dataIndex: 'operation', width: '10%', ellipsis: true,
      render: (col, record, index) => (
        <div>
          {/* <Button
            type="text"
            icon={<IconList />}
            onClick={() => onClickAppGroupDetail(record)}>
            详情
          </Button> */}
          {
            (record.owner == null)?
            <Popconfirm
                            focusLock
                            title='复制确认'
                            content={`确认复制app "${record.name}" ？`}
                            onOk={() => {
                              onCopyApp(record)
                            }}
                            onCancel={() => {
                              Message.info({
                                content: '取消复制',
                              });
                            }}
                          >
                        <Button
                          type="text"
                          // icon={<IconEdit />}
                          // onClick={() => onCopyApp(record)}
                          >
                          复制
                        </Button>
           </Popconfirm>:null
          }
           
            
           {
            (record.creator == "system"||record.creator == name||developer_list.includes(name))?
          <Button
            type="text"
            // icon={<IconEdit />}
            onClick={() => onClickEditApp(record)}>
            编辑
          </Button>:null
          }
           
           { (record.creator == "system"||record.creator == name||developer_list.includes(name))?

          <Popconfirm
                            focusLock
                            title='删除确认'
                            content={`删除后无法恢复，请再次确认删除app "${record.name}" ？`}
                            onOk={() => {
                              onClickDeleteApp(record)
                            }}
                            onCancel={() => {
                              Message.info({
                                content: '取消删除',
                              });
                            }}
                          >
               <Button
                type="text"
                // icon={<IconList />}
                // onClick={() => onClickDeleteAccount(record)}
                >删除
               </Button>
              </Popconfirm>
              :null
            }
        </div>
      ),
    },
  ]

  // app组列表页表格的列配置
  const appGroupColumns = [
    {
      title: 'id', dataIndex: 'id', width: '5%', ellipsis: true
    },
    {
      title: '业务', dataIndex: 'business_name', width: '8%', ellipsis: true
    },
    {
      title: '版本', dataIndex: 'version', width: '10%', ellipsis: true
    },
    {
      title: 'APP组名称', dataIndex: 'name', width: '20%', ellipsis: true
    },
    {
      title: '创建人', dataIndex: 'creator', width: '7%', ellipsis: true,
      render: (col, record, index) => (
        <Space>
          <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.creator}?format=40x40.png`}></Image>
          <Tooltip mini content={record.creator}>
            {/* {record.owner.length > 18 ? record.owner.substring(0, 18) + '...' : record.owner} */}
            <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{record.creator}</div>
          </Tooltip>
        </Space>
      )
    },
    {
      title: '创建时间', dataIndex: 'create_time', width: '10%', ellipsis: true
    },
    {
      title: '操作', dataIndex: 'operation', width: '10%', ellipsis: true,
      render: (col, record, index) => (
        <div>
          <Button
            type="text"
            // icon={<IconList />}
            onClick={() => onClickAppGroupDetail(record)}>
            详情
          </Button>
          {
          (record.creator == "system"||record.creator == name||developer_list.includes(name))?
          <Button
            type="text"
            // icon={<IconEdit />}
            onClick={() => onClickEditAppGroup(record)}>
            编辑
          </Button>:null}
          {
           (record.creator == "system"||record.creator == name||developer_list.includes(name))?
           <Popconfirm
                            focusLock
                            title='删除确认'
                            content={`删除后无法恢复，请再次确认删除app组 "${record.name}" ？`}
                            onOk={() => {
                              onClickDeleteAppGroup(record)
                            }}
                            onCancel={() => {
                              Message.info({
                                content: '取消删除',
                              });
                            }}
                          >
               <Button
                type="text"
                // icon={<IconList />}
                // onClick={() => onClickDeleteAccount(record)}
                >删除
               </Button>
              </Popconfirm>
          :null}
        </div>
      ),
    },
  ]

  return (
    <div>
      <Card bordered={false}>
        <Tabs activeTab={activeTabKey}
          onChange={(key) => onTabChange(key)}>
          <TabPane key='1' title='App列表'>
            <Form
              style={{ marginTop: '20px' }}
              wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
              form={form}
            >
              <Row gutter={20}>
                <Col span={3}>
                  <Form.Item field="business_id">
                    <Select
                      placeholder={'业务'}
                      // options={businessList}
                      allowClear
                      maxTagCount={1}
                      size="large"
                      showSearch
                      onChange={(value) => handleAppTabSearch(value, 'business_id')}
                    >
                      {businessList.map((item, index) => (
                        <Option key={item.id} value={item.id}> {item.business_name} </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item field="version">
                    <Select
                      placeholder={'版本'}
                      options={versionList}
                      allowClear
                      maxTagCount={1}
                      size="large"
                      showSearch
                      onSearch={(value) => handleVersionSearch(value)}
                      onChange={(value) => handleAppTabSearch(value, 'version')}
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={2} offset={16}>
                  <Button type='primary' style={{ float: 'right' }} onClick={() => onClickAddApp()}>
                    添加APP
                  </Button>
                </Col>
              </Row>
            </Form>
            <Table
              border={true} // 显示表格外部边框
              borderCell={true} // 显示单元格的内部边框
              hover={false} // 鼠标悬停时显示高亮效果
              stripe={true} // 显示斑马纹效果
              loading={loading} // 显示加载状态
              onChange={handlerAppTableChange} // 表格变化时的回调函数
              columns={appColumns} // 表格的列配置
              data={appTabList} // 表格的数据源
              pagination={appPagination} // 分页器
            />
          </TabPane>
          <TabPane key='2' title='App组列表'>
            <Form
              style={{ marginTop: '20px' }}
              wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
              form={form}
            >
              <Row gutter={20}>
                <Col span={3}>
                  <Form.Item field="business_id">
                    <Select
                      placeholder={'业务'}
                      // options={businessList}
                      allowClear
                      maxTagCount={1}
                      size="large"
                      showSearch
                      onChange={(value) => handleAppGroupSearch(value, 'business_id')}
                    >
                      {businessList.map((item, index) => (
                        <Option key={item.id} value={item.id}> {item.business_name} </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item field="version">
                    <Select
                      placeholder={'版本'}
                      options={versionList}
                      allowClear
                      maxTagCount={1}
                      size="large"
                      showSearch
                      onSearch={(value) => handleVersionSearch(value)}
                      onChange={(value) => handleAppGroupSearch(value, 'version')}
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={2} offset={16}>
                  <Button type='primary' style={{ float: 'right' }} onClick={() => onClickAddAppGroup()}>
                    添加APP组
                  </Button>
                </Col>
              </Row>
            </Form>
            <Table
              border={true} // 显示表格外部边框
              borderCell={true} // 显示单元格的内部边框
              hover={false} // 鼠标悬停时显示高亮效果
              stripe={true} // 显示斑马纹效果
              loading={loading} // 显示加载状态
              columns={appGroupColumns} // 表格的列配置
              data={appGroupTabList} // 表格的数据源
              onChange={handlerAppGroupTableChange} // 表格变化时的回调函数
              pagination={appGroupPagination} // 分页器
            />
          </TabPane>
        </Tabs>
      </Card>
      {/* App组详情弹窗 */}
      <Modal
        visible={showAppGroupDetailDialog}
        title={<div style={{ textAlign: 'left' }}>{appGroupDetailDialogData.name} APP组详情</div>}
        style={{ width: '80%', height: '95%' }}
        onOk={() => handleCloseAppGroupDetailDialog()}
        onCancel={() => handleCloseAppGroupDetailDialog()}
        autoFocus={false}
        focusLock={true}
        footer={null}
      >
        <b>Android性能包</b>
        <Table
          border={true} // 显示表格外部边框
          borderCell={true} // 显示单元格的内部边框
          hover={false} // 鼠标悬停时显示高亮效果
          stripe={true} // 显示斑马纹效果
          loading={appGroupDetailLoading} // 显示加载状态
          columns={appColumns.filter(item => item.title != '操作')} // 表格的列配置
          data={appGroupDetailDialogData.android_perf_app_id_list} // 表格的数据源
          noDataElement={<span>暂无数据</span>}
          style={{marginBottom:'20px'}}
        // pagination={appGroupPagination} // 分页器
        />

        <b>Android辅助包</b>
        <Table
          border={true} // 显示表格外部边框
          borderCell={true} // 显示单元格的内部边框
          hover={false} // 鼠标悬停时显示高亮效果
          stripe={true} // 显示斑马纹效果
          loading={appGroupDetailLoading} // 显示加载状态
          columns={appColumns.filter(item => item.title != '操作')} // 表格的列配置
          data={appGroupDetailDialogData.android_assist_app_id_list} // 表格的数据源
          noDataElement={<span>暂无数据</span>}
          style={{marginBottom:'20px'}}
        // pagination={appGroupPagination} // 分页器
        />

        <b>ios性能包</b>
        <Table
          border={true} // 显示表格外部边框
          borderCell={true} // 显示单元格的内部边框
          hover={false} // 鼠标悬停时显示高亮效果
          stripe={true} // 显示斑马纹效果
          loading={appGroupDetailLoading} // 显示加载状态
          columns={appColumns.filter(item => item.title != '操作')} // 表格的列配置
          data={appGroupDetailDialogData.ios_perf_app_id_list} // 表格的数据源
          noDataElement={<span>暂无数据</span>}
          style={{marginBottom:'20px'}}
        // pagination={appGroupPagination} // 分页器
        />

        <b>ios辅助包</b>
        <Table
          border={true} // 显示表格外部边框
          borderCell={true} // 显示单元格的内部边框
          hover={false} // 鼠标悬停时显示高亮效果
          stripe={true} // 显示斑马纹效果
          loading={appGroupDetailLoading} // 显示加载状态
          columns={appColumns.filter(item => item.title != '操作')} // 表格的列配置
          data={appGroupDetailDialogData.ios_assist_app_id_list} // 表格的数据源
          noDataElement={<span>暂无数据</span>}
          style={{marginBottom:'20px'}}
        // pagination={appGroupPagination} // 分页器
        />
      </Modal>
      {/* 添加/编辑App弹窗 */}
      <Modal
        visible={showAddOrEditAppDialog}
        title={
          <div style={{ textAlign: 'left' }}>
            {appDialogType == DIALOG_TYPE_ADD ? <span>添加APP</span> : <span>编辑APP</span>}
          </div>}
        style={{ width: '40%' }}
        onOk={() => handleCloseAddOrEditAppDialog()}
        onCancel={() => handleCloseAddOrEditAppDialog()}
        autoFocus={false}
        focusLock={true}
        footer={null}
      >
        <Form
          form={addOrEditAppForm}
          labelCol={{ span: 6 }}
          labelAlign={'right'}
          wrapperCol={{ span: 15 }}
          validateMessages={{
            required: (_, { label }) => `必须填写 ${label}`
          }}
          onChange={(value, values) => handleAddOrEditAppParamsChange(value, values)} // 表单值变化响应函数
          onSubmit={(value) => handleSubmitAddOrEditAppDialog(value)} // 提交表单响应函数
        >
          <Form.Item field="id" hidden>
            <Input placeholder={'id'}></Input>
          </Form.Item>
          <Row>
            <Col span={24}>
              <Form.Item
                label="业务"
                field="business_id"
                // disabled={appDialogType == DIALOG_TYPE_EDIT}
                required
                rules={[
                  {
                    type: 'number',
                    required: true,
                  },
                ]}>
                <Select
                  placeholder={'业务'}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                >
                  {businessList.map((item, index) => (
                    <Option key={item.id} value={item.id}> {item.business_name} </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="平台"
                field="platform"
                // disabled={appDialogType == DIALOG_TYPE_EDIT}
                required
                rules={[
                  {
                    type: 'number',
                    required: true,
                  },
                ]}
              >
                <Select
                  placeholder={'平台'}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                >
                  {platformList.map((item, index) => (
                    <Option key={item.key} value={item.key}> {item.label} </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="版本"
                field="version"
                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}
              >
                <Select
                  placeholder={'版本，支持输入和选择'}
                  options={versionList}
                  allowCreate
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                  onSearch={(value) =>{
                    // console.log('value如下')
                    // console.log(value);
                      fetchVersionListAppGroup({ payload: {page:1,page_size:10, business_id: addOrEditAppForm.getFieldValue('business_id'),version:value} })
                  }}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="包类型"
                field="app_type"
                // disabled={appDialogType == DIALOG_TYPE_EDIT}
                required
                rules={[
                  {
                    type: 'number',
                    required: true,
                  },
                ]}
                tooltip={<div>解释一下性能包、基准包、辅助包的区别</div>}>
                <RadioGroup style={{ marginBottom: 20 }}>
                  <Radio value={APP_TYPE_PERF}>性能包</Radio>
                  <Radio value={APP_TYPE_STANDARD}>基准包</Radio>
                  <Radio value={APP_TYPE_ASSIST}>辅助包</Radio>
                </RadioGroup>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="APP链接"
                field="url"
                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}>
                <Input placeholder={'请输入APP链接'}></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="混淆文件链接"
                field="jenkins_build_result_url"
                required={jenkinsBuildResultUrlVisible}
                rules={jenkinsBuildResultUrlVisible ? [
                  {
                    type: 'string',
                    required: true,
                  }
                ] : []}
                disabled={!jenkinsBuildResultUrlVisible}
                tooltip={<div>即jenkins_build_result.json的文件链接</div>}>
                <Input placeholder={'平台为Android时，请输入混淆文件链接'}></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="重打包证书"
                field="repackage_cert"
                required={repackageCertVisible}
                rules={repackageCertVisible ? [
                  {
                    type: 'string',
                    required: true,
                  }
                ] : []}
                disabled={!repackageCertVisible}
                tooltip={<div>即ios的重打包证书</div>}>
                <Select
                  placeholder={'平台为ios时，选择重打包证书'}
                  allowCreate
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                >
                  {repackageCertList.map((item, index) => (
                    <Option key={item.key} value={item.key}> {item.label} </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item wrapperCol={{ offset: 5 }}>
                <Button type='primary' htmlType='submit' style={{ marginRight: 24 }}>
                  提交
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      {/* 添加/编辑App组弹窗 */}
      <Modal
        visible={showAddOrEditAppGroupDialog}
        title={
          <div style={{ textAlign: 'left' }}>
            {appGroupDialogType == DIALOG_TYPE_ADD ? <span>添加APP组</span> : <span>编辑APP组</span>}
          </div>
        }
        style={{ width: '40%' }}
        onOk={() => handleCloseAddOrEditAppGroupDialog()}
        onCancel={() => handleCloseAddOrEditAppGroupDialog()}
        autoFocus={false}
        focusLock={true}
        footer={null}
      >
        <Form
          form={addOrEditAppGroupForm}
          labelCol={{ span: 6 }}
          labelAlign={'right'}
          wrapperCol={{ span: 15 }}
          validateMessages={{
            required: (_, { label }) => `必须填写 ${label}`
          }}
          onChange={(value, values) => handleAddOrEditAppGroupParamsChange(value, values)}
          onSubmit={(value) => handleSubmitAddOrEditAppGroupDialog(value)}
        >
          <Form.Item field="id" hidden>
            <Input placeholder={'id'}></Input>
          </Form.Item>
          <Row>
            <Col span={24}>
              <Form.Item
                label="名称"
                field="name"
                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}>
                <Input placeholder={'请输入APP组名称'}></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="业务"
                field="business_id"
                // disabled={appGroupDialogType == DIALOG_TYPE_EDIT}
                required
                rules={[
                  {
                    type: 'number',
                    required: true,
                  },
                ]}>
                <Select
                  placeholder={'业务'}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                  // onChange={(value) => updateBusiness(value, 'business')}
                >
                  {businessList.map((item, index) => (
                    <Option key={item.id} value={item.id}> {item.business_name} </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="版本"
                field="version"
                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}
              >
                <Select
                  placeholder={'版本'}
                  options={versionList}
                  // allowCreate
                  showSearch
                  onSearch={(value) =>{
                    // console.log('value如下')
                    // console.log(value);
                      fetchVersionListAppGroup({ payload: {page:1,page_size:10, business_id: addOrEditAppGroupForm.getFieldValue('business_id'),version:value} })
                  }}
                  // onChange={(value) => {
                  // }}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="Android性能测试包"
                field="android_perf_app_id_list"
                // required
                rules={[
                  // {
                  //   type: 'array',
                  //   required: true,
                  //   minLength: 1,
                  // },    
                  { validator: perfAppValidator, message: 'Android/ios性能测试包必选其一' }
                ]}
              >
                <Select
                  placeholder={'选择业务后，才会显示测试包选项'}
                  // options={addOrEditAppGroupAppList}
                  allowClear
                  mode='multiple'
                  maxTagCount={2}
                  allowCreate
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                  renderFormat={(option, value) => {
                    return option ? (<span> {` ${option.extra} `}  </span>) : (<span></span>);
                  }}
                >
                  {addOrEditAppGroupAppList.filter(item => item.platform == PLATFORM_ANDROID && (item.app_type == APP_TYPE_PERF|| item.app_type == APP_TYPE_STANDARD)).map((item, index) => (
                    <Option key={item.id} value={item.id} extra={item.name}>
                      {`${item.name} | `} <Link href={item.url}>App链接</Link>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="Android辅助测试包"
                field="android_assist_app_id_list"
                // required
                // rules={[
                //   {
                //     type: 'array',
                //     required: true,
                //     minLength: 1,
                //   },
                // ]}
              >
                <Select
                  placeholder={'选择业务后，才会显示测试包选项'}
                  // options={addOrEditAppGroupAppList}
                  allowClear
                  mode='multiple'
                  maxTagCount={2}
                  allowCreate
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                  renderFormat={(option, value) => {
                    return option ? (<span> {` ${option.extra} `}  </span>) : (<span></span>);
                  }}
                >
                  {addOrEditAppGroupAppList.filter(item => item.platform == PLATFORM_ANDROID && (item.app_type == APP_TYPE_ASSIST)).map((item, index) => (
                    <Option key={item.id} value={item.id} extra={item.name}>
                      {`${item.name} | `} <Link href={item.url}>App链接</Link>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="ios性能测试包"
                field="ios_perf_app_id_list"
                // required
                rules={[
                  // {
                  //   type: 'array',
                  //   required: true,
                  //   minLength: 1,
                  // },
                  { validator: perfAppValidator, message: 'Android/ios性能测试包必选其一' }
                ]}
              >
                <Select
                  placeholder={'选择业务后，才会显示测试包选项'}
                  // options={addOrEditAppGroupAppList}
                  allowClear
                  mode='multiple'
                  maxTagCount={2}
                  allowCreate
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                  renderFormat={(option, value) => {
                    return option ? (<span> {` ${option.extra} `}  </span>) : (<span></span>);
                  }}
                >
                  {addOrEditAppGroupAppList.filter(item => item.platform == PLATFORM_IOS && (item.app_type == APP_TYPE_PERF|| item.app_type == APP_TYPE_STANDARD)).map((item, index) => (
                    <Option key={item.id} value={item.id} extra={item.name}>
                      {`${item.name} | `} <Link href={item.url}>App链接</Link>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="ios辅助测试包"
                field="ios_assist_app_id_list"
                // required
                // rules={[
                //   {
                //     type: 'array',
                //     required: true,
                //     minLength: 1,
                //   },
                // ]}
              >
                <Select
                  placeholder={'选择业务后，才会显示测试包选项'}
                  // options={addOrEditAppGroupAppList}
                  allowClear
                  mode='multiple'
                  maxTagCount={2}
                  allowCreate
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                  renderFormat={(option, value) => {
                    return option ? (<span> {` ${option.extra} `}  </span>) : (<span></span>);
                  }}
                >
                  {addOrEditAppGroupAppList.filter(item =>  (item.app_type == APP_TYPE_ASSIST)).map((item, index) => (
                    <Option key={item.id} value={item.id} extra={item.name}>
                      {`${item.name} | `} <Link href={item.url}>App链接</Link>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item wrapperCol={{ offset: 5 }}>
                <Button type='primary' htmlType='submit' style={{ marginRight: 24 }}>
                  提交
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div >
  );
}
