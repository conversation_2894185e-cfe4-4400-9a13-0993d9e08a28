import React, { useState, useEffect,useCallback } from 'react';
import { Card, Form, Input,Select, Table, Grid, Space, Image, Tooltip, Tag, Modal, Button, Message, Popconfirm } from '@arco-design/web-react';
import Builder from '@devsre/builder';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@devsre/react-utils'
import {
  IconCopy,
  IconEdit,
  IconPlayArrow,
  IconRefresh,
  IconList,
  IconDelete,
  IconFile
} from '@arco-design/web-react/icon';
import { BackendApis2 } from 'src/utils/backendApis2';
import config from './config.json';
import {defaultCountrys,developer_list} from 'src/utils/const'
const Option = Select.Option;
const { Row, Col } = Grid;
import debounce from 'lodash/debounce';

export default function CurdCard() {
  const { email, name } = useUser()
  const navigate = useNavigate();
  const [accountListForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [businessList, setBusinessList] = useState([])
  const [statusList, setStatusList] = useState([]);
  const [searchFormValues, setSearchFormValues] = useState({}); //查询任务的条件
  const [accountList, setAccountList] = useState([])

  // 是否展示添加/编辑账号弹窗
  const [showAddOrEditAccountDialog, setShowAddOrEditAccountDialog] = useState(false);
   // 弹窗类型分类
   const DIALOG_TYPE_ADD = 1
   const DIALOG_TYPE_EDIT = 2
   const DIALOG_TYPE_DETAIL = 3
  // 弹窗类型（添加/编辑），默认添加
  const [accountDialogType, setAccountDialogType] = useState(DIALOG_TYPE_ADD);
  // 添加/编辑账号弹窗表格
  const [addOrEditAccountForm] = Form.useForm();
  const account_type_map = [
    {account_type:1,account_type_name:'性能账号',color:"green"},
    {account_type:2,account_type_name:'辅助账号',color:"gray"}
  ];
  // 账号状态分类
  const ACCOUNT_STATUS_OCCUPIED = 1
  const ACCOUNT_STATUS_FREE = 0

  const is_occupied_map = [
    {is_occupied:ACCOUNT_STATUS_OCCUPIED,account_status_name:'被占用',color:"red"},
    {is_occupied:ACCOUNT_STATUS_FREE,account_status_name:'空闲',color:"green"}
  ];

 
  // 任务类型分类
  const taskTypeMap: Map<number, Map<string, string>> = new Map([
    [1, new Map([['name', '版本回归'], ['color', 'purple']])],
    [2, new Map([['name', '专项测试'], ['color', 'gray']])],
  ]);

  
  const occupiedMap: Map<number, Map<string, string>> = new Map([
    [ACCOUNT_STATUS_FREE, new Map([['name', '未被占用'], ['color', 'green']])],
    [ACCOUNT_STATUS_OCCUPIED, new Map([['name', '被占用'], ['color', 'red']])]
  ]);
  

  // 任务结果分类
  const TASK_RESULT_NOT_COMPLETE = 0
  const TASK_RESULT_SUCCESS = 1
  const TASK_RESULT_FAIL = 2
  const taskResultMap: Map<number, Map<string, string>> = new Map([
    [TASK_RESULT_NOT_COMPLETE, new Map([['name', '暂无结果'], ['color', 'gray']])],
    [TASK_RESULT_SUCCESS, new Map([['name', '通过'], ['color', 'green']])],
    [TASK_RESULT_FAIL, new Map([['name', '不通过'], ['color', 'red']])],

  ]);

  // 分页器
  const [accountListPagination, setTaskListPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 20,
    current: 1,
    pageSizeChangeResetCurrent: true,
    sizeOptions: [10, 20, 50, 100, 200],
  });

  // 获取业务列表
  const fetchBusinessList =  async() => {
    const response = await BackendApis2.fetchBusinessList().finally(() => { });
    setBusinessList(response.items);
  };
  

  // 延时请求，查询条件变化时
    const onSearchFormDataChanges = useCallback(
       debounce((_, values) => {
         console.log('values');
         console.log(values);
         accountListPagination.current = 1;
         setTaskListPagination(accountListPagination);
         setSearchFormValues(values);
       }, 500),
       [],
     );


  // 获取账号列表
  const fetchAccountList = async (pagination,payload) => {
    setLoading(true)
    const body= { page: pagination.current, page_size: pagination.pageSize, ...payload};
    const response = await BackendApis2.getAccountList({body}).finally(() => {
      setLoading(false)
    });
      // 获取业务列表
    const response_bu = await BackendApis2.fetchBusinessList().finally(() => { });
    const businessList1 = response_bu.items;
 
    // 处理，把每个items的business id 替换成business_name
    const final_items = response.items.map(item => {
      const business_name = businessList1.find(business => business.id === item.business_id)?.business_name;
      return {
        ...item,
        business_name,
      };
    });
    // 处理，每个items 增加一个account_typename的字段，存储账号类型的名称
    setAccountList(final_items);
    accountListPagination.current = response.page;
    accountListPagination.pageSize = response.page_size;
    accountListPagination.total = response.total;
    setTaskListPagination(accountListPagination);
  };



  // 删除任务+子任务
  const deleteAccount = async (account_id) => {
    const payload = { "account_id": account_id }
    const response = await BackendApis2.deletePerfAccount({ payload }).finally(() => {
    });
    if (response.code != 200) {
      Message.error("删除失败")
    } else {
      Message.success("删除成功")
      fetchAccountList(accountListPagination, searchFormValues)
    }
  };

  // 根据条件搜索任务
  const handleTaskListSearch = (value, fieldName) => {
    const updatedFormValues = {
      ...searchFormValues,
      [fieldName]: value,
    };
    setSearchFormValues(updatedFormValues)
    localStorage.setItem('accountListSearchFormValues', JSON.stringify(updatedFormValues))
    fetchAccountList(accountListPagination, updatedFormValues);
  };

  const clickSearch = () => {
    fetchAccountList(accountListPagination, searchFormValues);
  }


  // 点击编辑，弹窗可见，弹窗状态为编辑
  const onClickEditAccount = (record) => {
    // Message.info("编辑")
    addOrEditAccountForm.setFieldsValue(record)
    setShowAddOrEditAccountDialog(true);
    setAccountDialogType(DIALOG_TYPE_EDIT);
  };


  // 点击删除账号
  const onClickDeleteAccount = (record) => {
    // Message.info("删除")
    deleteAccount(record.id)
  }

// 控制创建/编辑账号表单 关闭后的消失
  const handleCloseAddOrEditAccountDialog = () => {
    if (accountDialogType == DIALOG_TYPE_EDIT || accountDialogType == DIALOG_TYPE_DETAIL) {
      addOrEditAccountForm.resetFields();
    }
 

    setShowAddOrEditAccountDialog(false);
    // addOrEditAccountForm.resetFields();
  };
  

  //  控制创建/编辑账号表单 关闭或者提交后的变更
  const  handleSubmitAddOrEditAccountDialog = async (value) => {
    // 如果状态是创建，则调用创建接口
    if (accountDialogType == DIALOG_TYPE_ADD) {
      const body = {
        ...value,
        "owner":name,
        "email": "",
        "pwd": "",
        "app": 0,
        "is_occupied": 0
      }
      const response = await BackendApis2.createAccount({body}).finally(() => {
      })
    }
    // 如果状态是编辑，则调用编辑接口
    if (accountDialogType == DIALOG_TYPE_EDIT) {
      const body = {...value}
      const response = await BackendApis2.updateAccount({body}).finally(() => { 
      })
    }
    // 删除掉表单，并关闭弹窗
    addOrEditAccountForm.resetFields();
    setShowAddOrEditAccountDialog(false);
    // 刷新列表
    fetchAccountList(accountListPagination, searchFormValues);
  }


  // 点击创建任务
  const onClickCreateTask = () => {
    // Message.info("创建任务")
    setShowAddOrEditAccountDialog(true);
    setAccountDialogType(DIALOG_TYPE_ADD)
    // addOrEditAccountForm.resetFields()
    // addOrEditAccountForm.setFieldsValue({
    //   "phone_area_code":"86",
    //   "country_code_alpha2":"CN",
    //   "country":"中国大陆"
    // })
    addOrEditAccountForm.setFieldsValue(defaultCountrys)
    };

  // 任务列表分页器变化
  const onChange = (pagination) => {
    fetchAccountList(pagination, searchFormValues)
  };

  // 初始化数据
  useEffect(() => {
    fetchBusinessList();
 // 先判断localstate中是否有筛选参数，有的话，设置筛选参数
 console.log('accountListSearchFormValues');
 const storedFilter = localStorage.getItem('accountListSearchFormValues')
 console.log(storedFilter)
 if (storedFilter) {
   accountListForm.setFieldsValue(JSON.parse(storedFilter))
   setSearchFormValues(JSON.parse(storedFilter))
   fetchAccountList(accountListPagination, JSON.parse(storedFilter))
 }
 else{
    fetchAccountList(accountListPagination, searchFormValues)
   }
    // fetchAccountList(accountListPagination, searchFormValues)
  }, []);

  
  // 列表页表格的列配置
  const taskListColumns = [
    // {
    //   title: 'id', dataIndex: 'id', width: '3.5%', ellipsis: true
    // },
    {
      title: '业务', dataIndex: 'business_name', width: '5%', ellipsis: true
    },
    {
      title: 'UID', dataIndex: 'uid', width: '13%', ellipsis: true
    },
    {
      title: '号码', dataIndex: 'iphone', width: '8%', ellipsis: true,
    },
    {
      title: '账号用户名', dataIndex: 'username', width: '8%', ellipsis: true
    },
    {
      title: '账号状态', dataIndex: 'is_occupied', width: '5%', ellipsis: true,
      // 根据账号类型，显示账号类型的名称
      render: (col, record, index) => (
        <Tag color={is_occupied_map.find(item => item.is_occupied === record.is_occupied)?.color}>
          {is_occupied_map.find(item => item.is_occupied === record.is_occupied)?.account_status_name}
        </Tag>
        
      )
    },
    {
      title: '创建人', dataIndex: 'owner', width: '8%', ellipsis: true,
            render: (col, record, index) => (
              <Space>
                <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.owner}?format=40x40.png`}></Image>
                <Tooltip mini content={record.owner}>
                  {record.owner.length > 18 ? record.owner.substring(0, 18) + '...' : record.owner}
                </Tooltip>
              </Space>
            )
    },
    {
      title: '国家或地区', dataIndex: 'country', width: '6%', ellipsis: true,
    },
    {
      title: '账号类型', dataIndex: 'account_type', width: '6%', ellipsis: true,
      // 根据账号类型，显示账号类型的名称
      render: (col, record, index) => (
        <Tag color={account_type_map.find(item => item.account_type === record.account_type)?.color}>
          {account_type_map.find(item => item.account_type === record.account_type)?.account_type_name}
        </Tag>
        
      )
    },
    {
      title: '创建时间', dataIndex: 'create_time', width: '10%', ellipsis: true
    },
     {
          title: '操作', dataIndex: 'operation', width: developer_list.includes(name)?'10%':'8%', ellipsis: true,
          render: (col, record, index) => (
            <div>
             {(record.owner == name||developer_list.includes(name))?
              <Button
                type="text"
                // icon={<IconEdit />}
                onClick={() => onClickEditAccount(record)}>
                编辑
              </Button>:null}
              {(record.owner == name||developer_list.includes(name))?
               <Popconfirm
                            focusLock
                            title='删除确认'
                            content={`删除任务后无法恢复，请再次确认删除账号 "${record.username}" ？`}
                            onOk={() => {
                              onClickDeleteAccount(record)
                            }}
                            onCancel={() => {
                              Message.info({
                                content: '取消删除',
                              });
                            }}
                          >
               <Button
                type="text"
                // icon={<IconList />}
                // onClick={() => onClickDeleteAccount(record)}
                >删除
               </Button>
              </Popconfirm>
              :null}
            </div>
          ),
        },
  ]

  return (
    <Card bordered={false}>

      <Form
        style={{ marginTop: '20px' }}
        wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
        form={accountListForm}
        // onChange={onSearchFormDataChanges}
      >
        <Row gutter={20}>
          <Col span={3}>
            <Form.Item field="business_id">
              <Select
                placeholder={'业务'}
                // options={businessList}
                allowClear
                maxTagCount={1}
                size="large"
                showSearch
                onChange={(value) => handleTaskListSearch(value, 'business_id')}
              >
                {businessList.map((item, index) => (
                  <Option key={item.id} value={item.id}> {item.business_name} </Option>
                ))}
              </Select>
              
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item field="is_occupied">
              <Select
                placeholder={'是否被占用'}
                // options={businessList}
                allowClear
                maxTagCount={1}
                size="large"
                showSearch
                onChange={(value) => handleTaskListSearch(value, 'is_occupied')}
              >
                {Array.from(occupiedMap.entries()).map(([statusKey, innerMap]) => {
                const name = innerMap.get('name');
                // console.info(statusKey,name)
                return (
                    <Option key={statusKey} value={statusKey}>
                        {name}
                    </Option>
                );
            })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item field="account_type">
              <Select
                placeholder={'账号类型'}
                // options={businessList}
                allowClear
                maxTagCount={1}
                size="large"
                showSearch
                onChange={(value) => handleTaskListSearch(value, 'account_type')}
              >
                {account_type_map.map((item, index) => (
                  <Option key={item.account_type} value={item.account_type}> {item.account_type_name} </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
         
          {/* <Col span={2} >
            <Button type='primary' style={{ float: 'right' }} onClick={() => clickSearch()}>
              查询
            </Button>
          </Col> */}
          <Col span={2} offset={12}>
            <Button type='primary' style={{ float: 'right' }} onClick={() => onClickCreateTask()}>
              创建账号
            </Button>
          </Col>
        </Row>
      </Form>
      {/* 创建账号的弹窗 */}
      <Modal
        visible={showAddOrEditAccountDialog}
        title={
          <div style={{ textAlign: 'left' }}>
            {accountDialogType == DIALOG_TYPE_ADD ? <span>添加账号</span> : <span>编辑账号</span>}
          </div>}
        style={{ width: '40%' }}
        onOk={() => handleCloseAddOrEditAccountDialog()}
        onCancel={() => handleCloseAddOrEditAccountDialog()}
        autoFocus={false}
        focusLock={true}
        footer={null}
      >
        <Form
          form={addOrEditAccountForm}
          labelCol={{ span: 6 }}
          labelAlign={'right'}
          wrapperCol={{ span: 15 }}
          validateMessages={{
            required: (_, { label }) => `必须填写 ${label}`
          }}
          // onChange={(value, values) => handleAddOrEditAccountParamsChange(value, values)} // 表单值变化响应函数
          onSubmit={(value) => handleSubmitAddOrEditAccountDialog(value)} // 提交表单响应函数
        >
          <Form.Item field="id" hidden>
            <Input placeholder={'id'}></Input>
          </Form.Item>
          <Form.Item field="owner" hidden>
            <Input placeholder={'owner'}></Input>
          </Form.Item>
          <Form.Item field="email" hidden>
            <Input placeholder={'email'}></Input>
          </Form.Item>
          <Form.Item field="app" hidden>
            <Input placeholder={'app'}></Input>
          </Form.Item>
          <Form.Item field="is_occuiped" hidden>
            <Input placeholder={'is_occuiped'}></Input>
          </Form.Item>
          <Form.Item field="pwd" hidden>
            <Input placeholder={'pwd'}></Input>
          </Form.Item>
          <Row>
            <Col span={24}>
              <Form.Item
                label="业务"
                field="business_id"
                disabled={accountDialogType == DIALOG_TYPE_EDIT || accountDialogType == DIALOG_TYPE_DETAIL}
                required
                rules={[
                  {
                    type: 'number',
                    required: true,
                  },
                ]}>
                <Select
                  placeholder={'业务'}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                >
                  {businessList.map((item, index) => (
                    <Option key={item.id} value={item.id}> {item.business_name} </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="UID"
                field="uid"
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}
                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}>
                <Input placeholder={'请输入账号UID'}></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="号码"
                field="iphone"
                required
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}

                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}>
                <Input placeholder={'请输入账号号码'}></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="账号用户名"
                field="username"
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}

                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}>
                <Input placeholder={'请输入账号用户名'}></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="验证码"
                field="captcha"
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}

                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}>
                <Input placeholder={'请输入账号验证码'}></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="账号类型"
                field="account_type"
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}

                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  },
                ]}>
                <Select
                  placeholder={'账号类型'}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                >
                  {account_type_map.map((item, index) => (
                    <Option key={item.account_type} value={item.account_type}> {item.account_type_name} </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="国家或地区"
                field="country"
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}

                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  }
                  
                ]}>
                <Input placeholder={'请输入国家或地区'} ></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="国家代码"
                field="country_code_alpha2"
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}

                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  }
                  
                ]}>
                <Input placeholder={'请输入国家代码'} ></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="区号"
                field="phone_area_code"
                disabled = {accountDialogType == DIALOG_TYPE_DETAIL}

                required
                rules={[
                  {
                    type: 'string',
                    required: true,
                  }
                  
                ]}>
                <Input placeholder={'请输入区号'} ></Input>
              </Form.Item>
            </Col>
          </Row>
          <Row></Row>
          <Row>
          {accountDialogType != DIALOG_TYPE_DETAIL ?
            <Col span={24}>
              <Form.Item wrapperCol={{ offset: 5 }}>
                <Button type='primary' htmlType='submit' style={{ marginRight: 24 }}>
                  提交
                </Button>
              </Form.Item>
            </Col>
            :null}

          </Row>
        </Form>
      </Modal>
      <Table
        border={true} // 显示表格外部边框
        borderCell={true} // 显示单元格的内部边框
        hover={false} // 鼠标悬停时显示高亮效果
        stripe={true} // 显示斑马纹效果
        loading={loading} // 显示加载状态
        columns={taskListColumns} // 表格的列配置
        data={accountList} // 表格的数据源
        pagination={accountListPagination} // 分页器
        onChange={onChange}
      />
    </Card>
  );
}
