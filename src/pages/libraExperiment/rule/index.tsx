import React, { useEffect, useState } from 'react';
import { Button, Card, Form, Grid, Select, Table, Typography, Tooltip } from '@arco-design/web-react';
import Builder from '@devsre/builder';
import { BackendApis2 } from 'src/utils/backendApis2';
import config from './config.json';
const Option = Select.Option;
const { Title } = Typography;
const { Row, Col } = Grid;
export default function CurdCard() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [businessList, setBusinessList] = useState([]);
  const [ruleList, setRuleList] = useState([]);

  // 检查规则分类
  const checkWayMap: Map<number, string> = new Map([
    [0, '检查值本身'],
    [1, '检查值个数'],
    [2, '检查是否符合某规则'],
    [3, '框架问题'],
    [4, '环境问题'],
    [5, '机器问题'],
    [6, '网络问题'],
  ]);

  // 检查规则类型
  const operationTypeMap: Map<number, string> = new Map([
    [1, '等于'],
    [2, '大于'],
    [3, '大于等于'],
    [4, '小于'],
    [5, '小于等于'],
    [6, '正则'],
    [7, '是某个列表的子集'],
    [8, '与某个列表完全匹配'],
    [9, '是某个列表的父集'],
    [10, '是否为空'],
    [11, '忽略大小写'],
    [12, '评审人里至少有n个是RD'],
    [13, '至少包含其一'],
    [14, '实验组的key要在特定取值内'],
  ]);

  // 分页器
  const [pagination, setPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 20,
    current: 1,
    pageSizeChangeResetCurrent: true,
  });

  // 获取业务列表
  const fetchBusinessList = async () => {
    const response = await BackendApis2.fetchBusinessList().finally(() => { });
    setBusinessList(response.items);
  };

  // 获取检查规则
  const fetchLatestCheckRuleByBusiness = async (business_id) => {
    setLoading(true)
    const response = await BackendApis2.fetchLatestCheckRuleByBusinessId({ business_id: business_id }).finally(() => {
      setLoading(false)
    });
    console.log("fetchLatestCheckRuleByBusiness")
    console.log(response)
    setRuleList(response);
  };

  // 初始化数据
  useEffect(() => {
    fetchBusinessList();
  }, []);

  // 根据业务搜索检查规则
  const handleCheckRuleSearch = (value) => {
    console.log("handleCheckRuleSearch value")
    console.log(value)
    fetchLatestCheckRuleByBusiness(value);
  };

  function getExpectValueByCheckWayAndOperationType(expect_value, check_way, operator_type) {
    if (check_way == 0 && operator_type == 10) {
      return expect_value == 0 ? "为空" : "非空"
    }
    else if (check_way == 2 && operator_type == 11) {
      return expect_value == 0 ? "不忽略大小写（值非空时检查）" : "忽略大小写（值非空时检查）"
    }
    else if (check_way == 2 && operator_type == 12) {
      return "至少有" + expect_value + "个评审人是RD"
    }
    else {
      return expect_value
    }
  }

  // 规则表格的列配置
  const ruleColumns = [
    {
      title: '序号', dataIndex: 'index', width: '5%', ellipsis: true,
      render: (col, record, index) => (
        <span>{index + 1}</span>
      )
    },
    {
      title: '字段名称', dataIndex: 'field_name', width: '10%', ellipsis: true
    },
    {
      title: '检查方式', dataIndex: 'is_check_value', width: '15%', ellipsis: true,
      render: (col, record, index) => (
        <span>{checkWayMap.get(record.is_check_value)}</span>
      )
    },
    {
      title: '规则类型', dataIndex: 'operator_type', width: '15%', ellipsis: true,
      render: (col, record, index) => (
        <span>{operationTypeMap.get(record.operator_type)}</span>
      )
    },
    {
      title: '预期值', dataIndex: 'expect_value', width: '30%', ellipsis: true,
      render: (col, record, index) => (
        <span>
          {(getExpectValueByCheckWayAndOperationType(record.expect_value, record.is_check_value, record.operator_type).length > 50) ?
            <Tooltip mini content={getExpectValueByCheckWayAndOperationType(record.expect_value, record.is_check_value, record.operator_type)}>
              {getExpectValueByCheckWayAndOperationType(record.expect_value, record.is_check_value, record.operator_type)}
            </Tooltip> :
            <span>{getExpectValueByCheckWayAndOperationType(record.expect_value, record.is_check_value, record.operator_type)} </span>
          }
        </span>

      )
    },
    {
      title: '是否必须', dataIndex: 'is_must', width: '10%', ellipsis: true,
      render: (col, record, index) => (
        <span>{record.is_must == true ? "是" : "否"}</span>
      )
    }
  ]
  return (
    <Card bordered={false}>
      <Form
        style={{ marginTop: '20px' }}
        wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
        form={form}
      >
        <Row gutter={20}>
          <Col span={3}>
            <Form.Item field="business">
              <Select
                placeholder={'业务'}
                // options={businessList}
                allowClear
                maxTagCount={1}
                size="large"
                showSearch
                onChange={(value) => handleCheckRuleSearch(value)}
              >
                {businessList.map((item, index) => (
                  <Option key={item.id} value={item.id}> {item.business_name} </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        border={true} // 显示表格外部边框
        borderCell={true} // 显示单元格的内部边框
        hover={false} // 鼠标悬停时显示高亮效果
        stripe={true} // 显示斑马纹效果
        loading={loading} // 显示加载状态
        columns={ruleColumns} // 表格的列配置
        data={ruleList} // 表格的数据源
        pagination={pagination} // 分页器
      />
    </Card>
  );
}
