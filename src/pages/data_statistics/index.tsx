import React, { useEffect } from 'react';
import { Card, Tabs, Typography } from '@arco-design/web-react';
import { useMenu } from '@devsre/react-utils';
import Builder from '@devsre/builder';

import config from './config.json';

const TabPane = Tabs.TabPane;

export default function CurdCard() {
  // 隐藏面包屑
  const { hideBreadcrumbs } = useMenu();
  useEffect(() => { return hideBreadcrumbs(); }, []);
  return (
    <Card bordered={false}>
      <Tabs defaultActiveTab='1'>
        <TabPane key='1' title='用例大盘'>
          <iframe width={'100%'} height={'800px'} src={'https://data.bytedance.net/aeolus#/dashboard/814480?appId=1001447&sheetId=911037'} />
        </TabPane>
        <TabPane key='2' title='质量大盘'>
          <iframe width={'100%'} height={'800px'} src={'https://bits.bytedance.net/datamind/employee/dashboard/7408091570173544484?q=%28%27modeAreadBnodeIdA7200646779833239610BgranularityAQBcycleAstandardBrange%21%5B%272024-01-01%2000%3A00%3A00B2024-03-31%2023%3A59%3A59%27%5D.IdA%27.Filter%21%5B%5D~m%2ASpaceTypeAbusinessBm%2AFilterA%27%29%2AeasureObj.~domainM%2AA%21%27B%27~%01BA.%2A_#fa193368-16a7-4bf7-922c-26c643120228'} />
        </TabPane>
        <TabPane key='3' title='版本大盘'>
          <Typography.Title heading={5}>待建设，敬请期待</Typography.Title>
        </TabPane>
        <TabPane key='4' title='CI/CD大盘'>
          <iframe width={'100%'} height={'800px'} src={'https://data.bytedance.net/aeolus/#/dashboard/1120245?appId=1000056&sheetId=1437827'} />
        </TabPane>
      </Tabs>
    </Card>
  );
}
