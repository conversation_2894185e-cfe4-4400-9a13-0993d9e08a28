import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Typography, Table, Space, Image, Tooltip, Button, Message, Tag } from '@arco-design/web-react';
import { BackendApis2 } from '../../../utils/backendApis2';
import Builder from '@devsre/builder';
import config from './config.json';
import { useNavigate } from 'react-router-dom';
import {colorList} from '../../../utils/const';
const { Title } = Typography;


export default function CurdCard() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  function onClickVersionDetail(data) {
    navigate(`/versionManager/versionDetail?version_id=${data.id}`);
  }
  const columns = [
    {
      title: '序号', dataIndex: 'index', width: '5%', ellipsis: true,
      render: (col, record, index) => (
        <span>{index + 1}</span>
      )
    },
    {
      title: '版本号', dataIndex: 'version_name', width: '10%', ellipsis: true
    },
    {
      title: 'QA负责人', dataIndex: 'version_qa_owner', width: '15%', ellipsis: true,
      render: (col, record, index) => (
        <Space>
          <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.version_qa_owner}?format=40x40.png`}></Image>
          <Tooltip mini content={record.version_qa_owner}>
            {record.version_qa_owner.length > 18 ? record.version_qa_owner.substring(0, 18) + '...' : record.version_qa_owner}
          </Tooltip>
        </Space>
      )
    },
    {
      title: '端类型', dataIndex: 'platforms', width: '25%', ellipsis: true,
      render: (col, record, index) => (
        <span>
          {record.platforms.map((item, index) => {
            return <Tag key={index} style={{"marginRight":5}} color={colorList[(item.charCodeAt(0)) % colorList.length]}>{item}</Tag>
          })
          }
        </span>
      )
    },
    {
      title: '时间', dataIndex: 'start_time', width: '20%', ellipsis: true,
      render: (col, record, index) => (
        <span>
          {record.start_time.indexOf(' ') > 0 ? record.start_time.substring(0, record.start_time.indexOf(' ')) : record.start_time} - {record.end_time.indexOf(' ') > 0 ? record.end_time.substring(0, record.end_time.indexOf(' ')) : record.end_time}
        </span>
      )
    },
    {
      title: '当前阶段', dataIndex: 'current_stage', width: '15%', ellipsis: true,
      render: (col, record, index) => (
        <span>{record.current_stage == null ? '未封板' : record.current_stage.name}</span>
      )
    },
    {
      title: '操作', dataIndex: 'operation', ellipsis: true,
      render: (col, record, index) => (
        <Button type='text' onClick={() => onClickVersionDetail(record)}>查看详情</Button>
      )
    }
  ];
  const [pagination, setPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 10,
    current: 1,
    pageSizeChangeResetCurrent: true,
    sizeOptions: [10, 20, 50, 100, 200]
  });

  const onChange = (pagination) => {
    console.log("+++")
    fetchData(pagination)
  };

  const fetchData = async (pagination) => {
    console.log(pagination)
    setLoading(true);
    const payload = { page: pagination.current, page_size: pagination.pageSize }
    const response = await BackendApis2.getVersionList({ payload }).finally(() => {
      setLoading(false);
    });
    setData(response.items)
    pagination.current = response.page
    pagination.pageSize = response.page_size
    pagination.total = response.total
    setPagination(pagination)
  };

  useEffect(() => {
    fetchData(pagination);
  }, []);

  return (
    <div>
      <Card bordered={false}>
        <Title heading={6} style={{ marginTop: 0 }}>
          版本列表
        </Title>
        <Table
          style={{ marginTop: '0px' }}
          border={true}
          loading={loading}
          columns={columns}
          data={data}
          pagination={pagination}
          // onChange={() => fetchData(pagination, formData)}
          onChange={onChange}
        ></Table>
      </Card>
    </div>
  );
}
