import React, { useState, useEffect, useRef } from 'react';
import {
  Card, Typography, Form, Grid, Select, Space, Image, Tooltip, Steps, Button, Empty, Popconfirm, Input, Tag,
  Message
} from '@arco-design/web-react';
import { BackendApis2 } from '../../../utils/backendApis2';
import { IconEdit, IconClockCircle } from '@arco-design/web-react/icon';
import { useLocation } from 'react-router-dom';
import Builder from '@devsre/builder';
import config from './config.json';
import dayjs from 'dayjs';
import { useUser } from '@devsre/react-utils'
import { colorList } from '../../../utils/const';
const { Title } = Typography;
const { Row, Col } = Grid;
const Step = Steps.Step;


export default function CurdCard() {
  const { email, name } = useUser()
  const [form] = Form.useForm();
  const [formData, setFormData] = useState();
  const [checklistLoading, setChecklistLoading] = useState(false);
  const [versionList, setVersionList] = useState([])
  const [current, setCurrent] = useState(1);
  const [stageChecklists, setStageChecklists] = useState([]);
  const [checklistContent, setChecklistContent] = useState();
  const [versionDetail, setVersionDetail] = useState({
    id: 0,
    version_name: "",
    version_qa_owner: "",
    platforms: [],
    start_time: "",
    end_time: "",
    stage_list: [],
    current_stage: {
      "id": 0,
      "version_id": 0,
      "stage": 0,
      "name": "",
      "stage_order": 0,
      "start_time": "",
      "end_time": ""
    }
  })

  const default_current_stage = {
    "id": 0,
    "version_id": 0,
    "stage": 0,
    "name": "",
    "stage_order": 0,
    "start_time": "",
    "end_time": ""
  }

  //---url参数相关---
  const location = useLocation();
  // 通过 location.search 获取查询字符串
  const query = new URLSearchParams(location.search);
  // 处理查询参数名
  const QUERY_VERSION_ID = 'version_id'
  // 处理查询参数值
  var queryVersionId = query.get(QUERY_VERSION_ID);

  // -----
  // 自定义点击指定元素之外的区域执行传入的 callback 函数 hook
  const useClickOutside = (ref, callback) => {
    console.log("ref")
    console.log(ref)
    const handleClick = e => {
      if (ref.current && ref.current.innerHTML && !ref.current.innerHTML.includes(e.target.innerHTML)) {
        callback();
      }
    };
    useEffect(() => {
      document.addEventListener('click', handleClick);
      return () => {
        document.removeEventListener('click', handleClick);
      };
    });
  };
  const clickInputRef = useRef();
  const handleOutClickInput = () => {
    console.log("handleOutClickInput")
    setChecklistContent(null)
    // 选择指定区域之外时的处理函数 
  };
  // 监听点击事件，当点击绑定 clickCaseListRef 的子元素之外的区域时，会触发 handleClickOut 函数
  useClickOutside(clickInputRef, handleOutClickInput);
  // ------

  function setVersionNameByQuery() {
    if (queryVersionId != null) {
      var data = {}
      data[`${QUERY_VERSION_ID}`] = Number(queryVersionId)
      setFormData(data)
    }
  }

  function onFormDataChanges(values) {
    console.log("values");
    console.log(values);
    setFormData(values);
  }

  const [pagination, setPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 10,
    current: 1,
    pageSizeChangeResetCurrent: true,
    sizeOptions: [10, 20, 50, 100, 200]
  });

  const fetchVersionList = async (pagination) => {
    console.log(pagination)
    const payload = { page: pagination.current, page_size: pagination.pageSize }
    const response = await BackendApis2.getVersionList({ payload }).finally(() => {
    });
    const versionsData = response.items
    const tmp_versionList = versionsData.map(item => ({ label: item.version_name, value: item.id }))
    setVersionList(tmp_versionList)
    // console.log(versionsData.map(item => ({ label: item.version_name, value: item.id})))
    pagination.current = response.page
    pagination.pageSize = response.page_size
    pagination.total = response.total
    setPagination(pagination)
    console.log("queryVersionId")
    console.log(queryVersionId)
    if (queryVersionId == null && tmp_versionList.length > 0) {
      var data = {}
      data[`${QUERY_VERSION_ID}`] = tmp_versionList[0].value
      console.log("tmp_versionList")
      console.log(tmp_versionList)
      console.log("data")
      console.log(data)
      setFormData(data);
    }
  };

  const fetchVersionDetail = async (version_id) => {
    const payload = { version_id: version_id }
    var response = await BackendApis2.getVersionDetail({ payload }).finally(() => { });
    if (response.current_stage == null) {
      response.current_stage = default_current_stage
    } else {
      setCurrent(response.current_stage.stage_order)
    }
    setVersionDetail(response)
  };

  function onStepChange(value) {
    const list_index = value - 1
    fetchVersionChecklist(versionDetail.stage_list[list_index].version_id, versionDetail.stage_list[list_index].stage)
    setCurrent(value)
  }


  function handleDailyChecklist(versionChecklist) {
    var newVersionChecklist = []
    if (versionChecklist.length > 0) {
      var last_item = versionChecklist[0]
      for (let i = 1; i < versionChecklist.length; ++i) {
        if (last_item.checklist.id == versionChecklist[i].checklist.id) {
          if (last_item.daily_checklist == undefined) {
            var origin_last_item = last_item
            last_item = JSON.parse(JSON.stringify(origin_last_item));
            last_item.daily_checklist = [origin_last_item, versionChecklist[i]]
          }
          else {
            last_item.daily_checklist.push(versionChecklist[i])
          }
        }
        else {
          newVersionChecklist.push(last_item)
          last_item = versionChecklist[i]
        }
      }
      newVersionChecklist.push(last_item)
    }
    // console.log("newVersionChecklist")
    // console.log(newVersionChecklist)
    return newVersionChecklist
  }

  const fetchVersionChecklist = async (version_id, stage) => {
    setChecklistLoading(true)
    const payload = {
      version_id: version_id,
      stage: stage
    }
    const response = await BackendApis2.getVersionChecklist({ payload }).finally(() => {
      setChecklistLoading(false)
    });
    const data = handleDailyChecklist(response)
    setStageChecklists(data)
  };

  const customDot = (dot, { status, index, title, description }, versionDetail) => {
    const list_index = index - 1
    const visible = index === current;
    const time = versionDetail.stage_list[list_index].start_time.indexOf(' ') > 0 ? versionDetail.stage_list[list_index].start_time.substring(0, versionDetail.stage_list[list_index].start_time.indexOf(' ')) : versionDetail.stage_list[list_index].start_time
    const button_type = index == current ? "primary" : index < versionDetail.current_stage.stage_order ? "secondary" : "outline"
    return (
      <div style={{ margin: '0 auto' }}>
        <Row>
          <Button shape={"round"} type={button_type}>{versionDetail.stage_list[list_index].name}</Button>
        </Row>
        <Row>
          <span>{time}</span>
        </Row>
      </div>

      // <Popover popupVisible={visible} content={<span>Step: {index}</span>}>
      //   {dot}
      // </Popover>
    );
    return dot;
  };

  const updateVersionChecklist = async (body) => {
    const response = await BackendApis2.updateVersionChecklist({ body }).finally(() => { });
    if (response.code == 200) {
      Message.success('修改成功');
      const list_index = current - 1
      fetchVersionChecklist(versionDetail.stage_list[list_index].version_id, versionDetail.stage_list[list_index].stage)
    } else {
      Message.error('修改失败');
    }
    setChecklistContent(null)
  };

  const submitChecklist = (id, comment) => {
    const body = {
      id: id,
      is_finished: true,
      comment: comment,
      finish_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      operator: name
    }
    console.log("body")
    console.log(body)
    updateVersionChecklist(body)
  }

  useEffect(() => {
    if (versionDetail.id != 0 && versionDetail.stage_list.length >= current) {
      const list_index = current - 1
      fetchVersionChecklist(versionDetail.stage_list[list_index].version_id, versionDetail.stage_list[list_index].stage)
    }
  }, [versionDetail]);


  useEffect(() => {
    if (formData != undefined) {
      console.log("===")
      console.log(formData)
      form.setFieldsValue(formData)
      fetchVersionDetail(formData.version_id)
    }
  }, [formData]);

  useEffect(() => {
    setVersionNameByQuery();
    fetchVersionList(pagination);
  }, []);

  return (
    <div>
      <Card bordered={false} style={{ height: '95%' }}>
        <Title heading={6} style={{ marginTop: 0 }}>
          版本详情
        </Title>
        <Form style={{ marginTop: '20px' }} wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
          form={form}
          onChange={onFormDataChanges}>
          <Row gutter={10}>
            <Col span={6}>
              <Row gutter={10}>
                <Col span={12}>
                  <Form.Item field='version_id'>
                    <Select placeholder={'请选择版本名'} options={versionList}></Select>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            {versionDetail.id > 0 ?
              <Col span={6}>
                <Space>
                  QA负责人：
                  <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${versionDetail.version_qa_owner}?format=40x40.png`}></Image>
                  <Tooltip mini content={versionDetail.version_qa_owner}>
                    {versionDetail.version_qa_owner.length > 18 ? versionDetail.version_qa_owner.substring(0, 18) + '...' : versionDetail.version_qa_owner}
                  </Tooltip>
                </Space>
              </Col>
              : <span />}
            {versionDetail.id > 0 ?
              <Col span={6}>
                <Space>
                  端类型：
                  {versionDetail.platforms.map((item, index) => {
                    return <Tag style={{ "marginRight": 5 }} color={colorList[(item.charCodeAt(0)) % colorList.length]}>{item}</Tag>
                  })
                  }
                </Space>
              </Col>
              : <span />}
            {versionDetail.id > 0 ?
              <Col span={6}>
                <span>
                  版本周期：{versionDetail.start_time.indexOf(' ') > 0 ? versionDetail.start_time.substring(0, versionDetail.start_time.indexOf(' ')) : versionDetail.start_time} - {versionDetail.end_time.indexOf(' ') > 0 ? versionDetail.end_time.substring(0, versionDetail.end_time.indexOf(' ')) : versionDetail.end_time}
                </span>
              </Col>
              : <span />}
          </Row>
          {versionDetail.id > 0 ?
            <Card>
              <Title heading={6} style={{ marginTop: 0 }}>版本阶段</Title>
              {versionDetail.stage_list.length > 0 ?
                <div>
                  <Row gutter={10}>
                    <Steps style={{ margin: '0 auto', width: "90%" }} labelPlacement='vertical' current={current} customDot={(e1, e2) => customDot(e1, e2, versionDetail)} onChange={onStepChange}>
                      {versionDetail.stage_list.map((item) => {
                        return <Step key={item.stage_order} />
                      })}
                    </Steps>
                  </Row>
                  <Card loading={checklistLoading} style={{ margin: '0 auto', width: "95%" }}>
                    <Title heading={6} style={{ marginTop: 0, marginBottom: 20 }}>{versionDetail.stage_list[current - 1].name} - Checklist列表</Title>
                    {stageChecklists.length > 0 ?
                      stageChecklists.map((item, index) => {
                        return <div>
                          {item.checklist.remind_type == 2 ?
                            <div>
                              <Row gutter={[24, 24]}>
                                <Col span={12}>
                                  <span>
                                    {index + 1}、
                                    <Tag color='cyan'>每日</Tag>
                                    <b style={{ marginLeft: 5 }}>{item.checklist.name}：</b>{item.checklist.checklist_desc}
                                  </span>
                                </Col>
                                {/* 没有每日checklist时间，按单次checklist处理 */}
                                {item.daily_checklist == undefined ?
                                  <Col span={12}>
                                    {item.is_finished ?
                                      <span>
                                        <span><Button disabled>已完成</Button></span>
                                        <span style={{ "marginLeft": 20 }}>
                                          <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${item.operator}?format=40x40.png`}></Image>
                                          <Tooltip mini content={item.operator}>
                                            {item.operator.length > 18 ? item.operator.substring(0, 18) + '...' : item.operator}
                                          </Tooltip>
                                        </span>
                                        <span style={{ "marginLeft": 10 }}>完成于{item.finish_time}</span>
                                        <span style={{ "marginLeft": 10 }}>备注：{item.comment}</span>
                                      </span>
                                      :
                                      <Popconfirm ref={clickInputRef}
                                        title={
                                          <span>填写备注:
                                            <Input allowClear placeholder='请填写Checklist完成备注，非必填' value={checklistContent} onChange={value => setChecklistContent(value)}></Input>
                                          </span>
                                        }
                                        icon={<IconEdit />}
                                        position='bottom'
                                        onOk={() => submitChecklist(item.id, checklistContent)}
                                        onCancel={() => setChecklistContent(null)}
                                      >
                                        <Button type='primary'>完成</Button>
                                      </Popconfirm>
                                    }
                                  </Col> :
                                  <span />
                                }
                              </Row>
                              {/* 有每日checklist时间，展示格式不同 */}
                              {item.daily_checklist == undefined ? <span /> :
                                item.daily_checklist.map((daily_item, daily_index) => {
                                  return <Row gutter={[24, 10]}>
                                    <Col span={1}></Col>
                                    <Col span={11}>
                                      <IconClockCircle /><span style={{ marginLeft: 2 }}>{daily_item.start_time.substring(0, daily_item.start_time.indexOf(' '))}</span>
                                    </Col>
                                    <Col span={12}>
                                      {daily_item.is_finished ?
                                        <span>
                                          <span><Button disabled>已完成</Button></span>
                                          <span style={{ "marginLeft": 20 }}>
                                            <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${daily_item.operator}?format=40x40.png`}></Image>
                                            <Tooltip mini content={daily_item.operator}>
                                              {daily_item.operator.length > 18 ? daily_item.operator.substring(0, 18) + '...' : daily_item.operator}
                                            </Tooltip>
                                          </span>
                                          <span style={{ "marginLeft": 10 }}>完成于{daily_item.finish_time}</span>
                                          <span style={{ "marginLeft": 10 }}>备注：{daily_item.comment}</span>
                                        </span>
                                        :
                                        <Popconfirm ref={clickInputRef}
                                          title={
                                            <span>填写备注:
                                              <Input allowClear placeholder='请填写Checklist完成备注，非必填' value={checklistContent} onChange={value => setChecklistContent(value)}></Input>
                                            </span>
                                          }
                                          icon={<IconEdit />}
                                          position='bottom'
                                          onOk={() => submitChecklist(daily_item.id, checklistContent)}
                                          onCancel={() => setChecklistContent(null)}
                                        >
                                          <Button type='primary'>完成</Button>
                                        </Popconfirm>
                                      }
                                    </Col>
                                  </Row>
                                })
                              }
                            </div>
                            :
                            <Row gutter={[24, 24]}>
                              <Col span={12}>
                                <span>
                                  {index + 1}、
                                  <Tag color='blue'>单次</Tag>
                                  <b style={{ marginLeft: 5 }}>{item.checklist.name}：</b>{item.checklist.checklist_desc}
                                </span>
                              </Col>
                              <Col span={12}>
                                {item.is_finished ?
                                  <span>
                                    <span><Button disabled>已完成</Button></span>
                                    <span style={{ "marginLeft": 20 }}>
                                      <Image preview={false} style={{ 'borderRadius': '50%' }} height='20' src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${item.operator}?format=40x40.png`}></Image>
                                      <Tooltip mini content={item.operator}>
                                        {item.operator.length > 18 ? item.operator.substring(0, 18) + '...' : item.operator}
                                      </Tooltip>
                                    </span>
                                    <span style={{ "marginLeft": 10 }}>完成于{item.finish_time}</span>
                                    <span style={{ "marginLeft": 10 }}>备注：{item.comment}</span>
                                  </span>
                                  :
                                  <Popconfirm ref={clickInputRef}
                                    title={
                                      <span>填写备注:
                                        <Input allowClear placeholder='请填写Checklist完成备注，非必填' value={checklistContent} onChange={value => setChecklistContent(value)}></Input>
                                      </span>
                                    }
                                    icon={<IconEdit />}
                                    position='bottom'
                                    onOk={() => submitChecklist(item.id, checklistContent)}
                                    onCancel={() => setChecklistContent(null)}
                                  >
                                    <Button type='primary'>完成</Button>
                                  </Popconfirm>

                                }
                              </Col>
                            </Row>
                          }
                        </div>
                      })
                      : <Empty />
                    }
                  </Card>
                </div>
                : <Empty />}
            </Card>
            : <span />}
        </Form>
      </Card>
    </div>
  );
}
