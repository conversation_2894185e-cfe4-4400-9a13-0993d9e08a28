import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  DatePicker,
  Form,
  Grid,
  Image,
  Input,
  Link,
  Message,
  Modal,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from '@arco-design/web-react';
import { IconCaretRight, IconCheck, IconEdit } from '@arco-design/web-react/icon';
import { BackendApis2 } from '../../../utils/backendApis2';
import debounce from 'lodash/debounce';
import dayjs from 'dayjs';
import { colorList } from '../../../utils/const';
import { useLocation, useNavigate } from 'react-router-dom';
// import { CloudContextTransfer } from '@devsre-plugin/byte-cloud-provider/lib/CloudContextTransfer';

const TabPane = Tabs.TabPane;
const { Title } = Typography;
const { Row, Col } = Grid;

export default function CurdCard() {
  const [form] = Form.useForm();
  const [branchList, setBranchList] = useState([]);
  const [branchFetching, setBranchFetching] = useState(false);
  const [resultCodeList, setResultCodeList] = useState([]);
  const [resultCodeFetching, setResultCodeFetching] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [moduleList, setModuleList] = useState([]);
  const [qaOwnerList, setQaOwnerList] = useState([]);
  const [failReasonTypeList, setFailReasonTypeList] = useState([]);
  const [formData, setFormData] = useState();
  const [showEditCaseListFailReasonStrMap, setShowEditCaseListFailReasonStrMap] = useState(
    new Map(),
  );
  const [showEditCaseListImprovementMeasureStrMap, setShowEditCaseListImprovementMeasureStrMap] =
    useState(new Map());
  const [pagination, setPagination] = useState({
    sizeCanChange: true,
    showTotal: true,
    total: 0,
    pageSize: 10,
    current: 1,
    pageSizeChangeResetCurrent: true,
    sizeOptions: [10, 20, 50, 100, 200],
  });
  const [caseUpdateformData, setCaseUpdateFormData] = useState({
    id: 0,
    fail_reason_type: 0,
    fail_reason_str: '',
    improvement_measure_str: '',
    is_closed: false,
    meego_bug_url: '',
  });

  //---url参数相关---
  const location = useLocation();
  const navigate = useNavigate();
  // 通过 location.search 获取查询字符串
  const query = new URLSearchParams(location.search);
  // 处理查询参数
  const QUERY_NAME_DIALOG = 'dialog';
  const QUERY_NAME_TEST_CASE_DETAIL_ID = 'test_case_detail_id';
  const QUERY_NAME_PIPELINE_TYPE = 'pipeline_type';
  const QUERY_NAME_TIME = 'time';
  const QUERY_NAME_MODULE = 'module';
  const QUERY_NAME_FAIL_REASON_TYPE = 'fail_reason_type';
  const QUERY_NAME_CASE_ID = 'case_id';
  const QUERY_NAME_RESULT_CODE = 'result_code';
  const QUERY_NAME_BRANCH = 'branch';
  const QUERY_NAME_PLATFORM = 'platform';
  const QUERY_NAME_QA_OWNER = 'qa_owner';
  const QUERY_NAME_IS_CLOSED = 'is_closed';
  // case详情参数
  const queryDialog = query.get(QUERY_NAME_DIALOG);
  const queryTestCaseDetailId = query.get(QUERY_NAME_TEST_CASE_DETAIL_ID);
  // 列表查询参数
  const queryPipelineType = query.get(QUERY_NAME_PIPELINE_TYPE);
  const queryTime = query.get(QUERY_NAME_TIME);
  const queryModule = query.get(QUERY_NAME_MODULE);
  const queryFailReasonType = query.get(QUERY_NAME_FAIL_REASON_TYPE);
  const queryCaseId = query.get(QUERY_NAME_CASE_ID);
  const queryResultCode = query.get(QUERY_NAME_RESULT_CODE);
  const queryBranch = query.get(QUERY_NAME_BRANCH);
  const queryPlatform = query.get(QUERY_NAME_PLATFORM);
  const queryQaOwner = query.get(QUERY_NAME_QA_OWNER);
  const queryIsClosed = query.get(QUERY_NAME_IS_CLOSED);

  function setDialogByQuery() {
    if (queryDialog == 'true' && queryTestCaseDetailId != null) {
      setShowDialog(true);
      fetchCaseDetail(queryTestCaseDetailId);
    }
  }

  function setFormDataByQuery() {
    let data = {};
    data = setStringToList(queryPipelineType, QUERY_NAME_PIPELINE_TYPE, data);
    data = setStringToList(queryTime, QUERY_NAME_TIME, data);
    data = setStringToList(queryModule, QUERY_NAME_MODULE, data, true);
    data = setStringToList(queryFailReasonType, QUERY_NAME_FAIL_REASON_TYPE, data, true);
    if (queryCaseId != null) data[`${QUERY_NAME_CASE_ID}`] = queryCaseId;
    data = setStringToList(queryPipelineType, QUERY_NAME_PIPELINE_TYPE, data);
    data = setStringToList(queryResultCode, QUERY_NAME_RESULT_CODE, data);
    data = setStringToList(queryBranch, QUERY_NAME_BRANCH, data);
    data = setStringToList(queryPlatform, QUERY_NAME_PLATFORM, data);
    if (queryQaOwner != null) data[`${QUERY_NAME_QA_OWNER}`] = queryQaOwner;
    data = setStringToList(queryIsClosed, QUERY_NAME_IS_CLOSED, data);
    setFormData(data);
    console.log('---data');
    console.log(data);
    console.log('---formData');
    console.log(formData);
    return data;
  }

  function setStringToList(str, name, data, parseInt = false) {
    if (str != null) {
      const tmpStrList = str.split(',');
      if (parseInt) {
        data[`${name}`] = tmpStrList.map((item) => Number(item) as number);
      } else {
        data[`${name}`] = tmpStrList;
      }
      return data;
    } else {
      return data;
    }
  }

  function setListToString(list, name) {
    if (list != undefined && list.length > 0) {
      let str = '';
      for (let i = 0; i < list.length; ++i) {
        str = str + list[i];
        if (i < list.length - 1) {
          str += ',';
        }
      }
      return `${name}=${str}&`;
    } else {
      return '';
    }
  }

  const handleUpdateQueryByFormData = (formData) => {
    let queryStr = '?';
    queryStr += setListToString(formData.pipeline_type, QUERY_NAME_PIPELINE_TYPE);
    queryStr += setListToString(formData.time, QUERY_NAME_TIME);
    queryStr += setListToString(formData.module, QUERY_NAME_MODULE);
    queryStr += setListToString(formData.fail_reason_type, QUERY_NAME_FAIL_REASON_TYPE);
    queryStr += formData.case_id != undefined ? `${QUERY_NAME_CASE_ID}=${formData.case_id}&` : '';
    queryStr += setListToString(formData.result_code, QUERY_NAME_RESULT_CODE);
    queryStr += setListToString(formData.branch, QUERY_NAME_BRANCH);
    queryStr += setListToString(formData.platform, QUERY_NAME_PLATFORM);
    queryStr +=
      formData.qa_owner != undefined ? `${QUERY_NAME_QA_OWNER}=${formData.qa_owner}&` : '';
    queryStr += setListToString(formData.is_closed, QUERY_NAME_IS_CLOSED);
    if (
      query.get(QUERY_NAME_DIALOG) == 'true' &&
      query.get(QUERY_NAME_TEST_CASE_DETAIL_ID) != null
    ) {
      queryStr += `${QUERY_NAME_DIALOG}=true&${QUERY_NAME_TEST_CASE_DETAIL_ID}=${query.get(
        QUERY_NAME_TEST_CASE_DETAIL_ID,
      )}&`;
    }
    queryStr =
      queryStr.endsWith('?') || queryStr.endsWith('&')
        ? queryStr.substring(0, queryStr.length - 1)
        : queryStr;
    navigate(queryStr);
    // console.log(`queryPipelineType=${queryPipelineType}`)
    // console.log(`queryTime=${queryTime}`)
    // console.log(`queryModule=${queryModule}`)
    // console.log(`queryFailReasonType=${queryFailReasonType}`)
    // console.log(`queryCaseId=${queryCaseId}`)
    // console.log(`queryResultCode=${queryResultCode}`)
    // console.log(`queryBranch=${queryBranch}`)
    // console.log(`queryPlatform=${queryPlatform}`)
    // console.log(`queryQaOwner=${queryQaOwner}`)
    // console.log(`queryIsClosed=${queryIsClosed}`)
    // console.log("formData")
    // console.log(formData)
    // console.log("formData.pipeline_type")
    // console.log(formData.pipeline_type)
  };

  // -----
  // 自定义点击指定元素之外的区域执行传入的 callback 函数 hook
  const useClickOutside = (ref, callback) => {
    const handleClick = (e) => {
      if (ref.current && !ref.current.innerHTML.includes(e.target.innerHTML)) {
        callback();
      }
    };
    useEffect(() => {
      document.addEventListener('click', handleClick);
      return () => {
        document.removeEventListener('click', handleClick);
      };
    });
  };
  const clickInputRef = useRef();
  const handleOutClickInput = () => {
    console.log('handleOutClickInput');
    setShowEditCaseListFailReasonStrMap(() => {
      const newMap = new Map();
      return newMap;
    });
    setShowEditCaseListImprovementMeasureStrMap(() => {
      const newMap = new Map();
      return newMap;
    });
    // 选择指定区域之外时的处理函数
  };
  // 监听点击事件，当点击绑定 clickCaseListRef 的子元素之外的区域时，会触发 handleClickOut 函数
  useClickOutside(clickInputRef, handleOutClickInput);
  // ------

  const fetchBranch = async (branch) => {
    const response = await BackendApis2.getBranchs({
      payload: { page: 1, page_size: 10, branch: branch },
    }).finally(() => {
    });
    setBranchList(response);
  };

  const fetchResultCode = async (result_code) => {
    const response = await BackendApis2.getResultCodes({
      payload: { page: 1, page_size: 10, result_code: result_code },
    }).finally(() => {
    });
    setResultCodeList(response);
  };

  const updateCaseDetail = async (body, isDialog) => {
    const response = await BackendApis2.putUpdateCaseDetail({ body }).finally(() => {
      Message.success('修改成功');
      if (isDialog) {
        fetchCaseDetail(body.id);
      }
      fetchData(pagination, formData);
    });
  };

  // const onChange = useCallback(
  //   debounce((pagination, formData) => {
  //     console.log("+++")
  //     fetchData(pagination, formData)
  //   }, 500),
  //   []
  // );

  const onChange = (pagination) => {
    console.log('+++');
    fetchData(pagination, formData);
  };

  const fetchData = async (pagination, formData) => {
    console.log(pagination);
    setLoading(true);
    handleUpdateQueryByFormData(formData);
    const payload = { page: pagination.current, page_size: pagination.pageSize };
    const body = formData;
    // const body = {
    //   "mr_state": "string",//这个不需要

    //   "pipeline_type": "string",
    //   "platform": "string",
    //   "case_id": "string",
    //   //缺一个任务开始时间的区间筛选
    //   "branch": "string",
    //   "module": 0,
    //   "qa_owner": "string",
    //   "result_code": 0,
    //   "fail_reason_type": 0,
    //   "is_closed": 0  //这个应该是bool类型的
    // }
    const response = await BackendApis2.getCaseData({ payload, body }).finally(() => {
      setLoading(false);
    });
    setData(response.items);
    pagination.current = response.page;
    pagination.pageSize = response.page_size;
    pagination.total = response.total;
    setPagination(pagination);
  };

  useEffect(() => {
    setFormDataByQuery();
    setDialogByQuery();
    fetchBranch([]);
    fetchResultCode([]);
    // console.log("---")
    // console.log(formData)
    // fetchData(pagination, formData);
    setModuleList(setListByMap(moduleMap));
    setFailReasonTypeList(setListByMap(failReasonTypeMap));
  }, []);

  useEffect(() => {
    if (formData != undefined) {
      console.log('===');
      console.log(formData);
      form.setFieldsValue(formData);
      fetchData(pagination, formData);
    }
  }, [formData]);

  const searchBranchs = useCallback(
    debounce((inputValue) => {
      setBranchFetching(true);
      setBranchList([]);
      fetchBranch(inputValue);
      setBranchFetching(false);
    }, 500),
    [],
  );

  const searchResultCodes = useCallback(
    debounce((inputValue) => {
      if (typeof inputValue === 'string' && !isNaN(Number(inputValue))) {
        setResultCodeFetching(true);
        setResultCodeList([]);
        fetchResultCode(Number(inputValue));
        setResultCodeFetching(false);
      }
    }, 500),
    [],
  );

  // 更新用例列表数据

  const onClickEditCaseListFailReasonStr = (e, index) => {
    setShowEditCaseListFailReasonStrMap(() => {
      const newMap = new Map();
      newMap.set(index, true); // 根据键切换对应的值
      return newMap;
    });
    setShowEditCaseListImprovementMeasureStrMap(() => {
      const newMap = new Map();
      return newMap;
    });
  };
  const onClickEditCaseListImprovementMeasureStr = (e, index) => {
    setShowEditCaseListFailReasonStrMap(() => {
      const newMap = new Map();
      return newMap;
    });
    setShowEditCaseListImprovementMeasureStrMap(() => {
      const newMap = new Map();
      newMap.set(index, true); // 根据键切换对应的值
      return newMap;
    });
  };

  const onCaseListFailReasonTypeChange = (value, caseData, isDialog) => {
    console.log('caseData');
    console.log(caseData);
    const body = {
      id: caseData.id,
      fail_reason_type: value.value,
      fail_reason_str: caseData.fail_reason_str,
      improvement_measure_str: caseData.improvement_measure_str,
      is_closed: caseData.is_closed,
      meego_bug_url: caseData.meego_bug_url,
      attribution_type: 0,
    };
    updateCaseDetail(body, isDialog);
  };

  const onCaseListFailReasonStrChange = (value, caseData, index, isDialog) => {
    // console.log("value")
    // console.log(value)
    const body = {
      id: caseData.id,
      fail_reason_type: caseData.fail_reason_type,
      fail_reason_str: value,
      improvement_measure_str: caseData.improvement_measure_str,
      is_closed: caseData.is_closed,
      meego_bug_url: caseData.meego_bug_url,
      attribution_type: 0,
    };
    updateCaseDetail(body, isDialog);
    setShowEditCaseListFailReasonStrMap((prevMap) => {
      const newMap = new Map(prevMap);
      newMap.set(index, false);
      return newMap;
    });
  };

  const onCaseListImprovementMeasureStrChange = (value, caseData, index, isDialog) => {
    const body = {
      id: caseData.id,
      fail_reason_type: caseData.fail_reason_type,
      fail_reason_str: caseData.fail_reason_str,
      improvement_measure_str: value,
      is_closed: caseData.is_closed,
      meego_bug_url: caseData.meego_bug_url,
      attribution_type: 0,
    };
    updateCaseDetail(body, isDialog);
    setShowEditCaseListImprovementMeasureStrMap((prevMap) => {
      const newMap = new Map(prevMap);
      newMap.set(index, false);
      return newMap;
    });
  };

  const onCaseListIsCloesdChange = (value, caseData, isDialog) => {
    console.log('value');
    console.log(value);
    const body = {
      id: caseData.id,
      fail_reason_type: caseData.fail_reason_type,
      fail_reason_str: caseData.fail_reason_str,
      improvement_measure_str: caseData.improvement_measure_str,
      is_closed: value,
      meego_bug_url: caseData.meego_bug_url,
      attribution_type: caseData.attribution_type,
    };
    updateCaseDetail(body, isDialog);
  };

  const onCaseListMeegoBugUrlChange = (value, caseData, isDialog) => {
    const body = {
      id: caseData.id,
      fail_reason_type: caseData.fail_reason_type,
      fail_reason_str: caseData.fail_reason_str,
      improvement_measure_str: caseData.improvement_measure_str,
      is_closed: caseData.is_closed,
      meego_bug_url: value.value,
      attribution_type: 0,
    };
    updateCaseDetail(body, isDialog);
  };

  const sumbitBugToMeego = () => {
    Message.info('暂未支持，敬请期待～');
  };

  const relateBugToMeego = () => {
    Message.info('暂未支持，敬请期待～');
  };

  const columns = [
    {
      title: '类型',
      dataIndex: 'pipeline_type',
      width: '5%',
      ellipsis: true,
      render: (col, record, index) => (
        <span>
          {record.pipeline_type?.length > 0 ? (
            <Tag color={colorList[(record.pipeline_type.charCodeAt(0) + 1) % colorList.length]}>
              {record.pipeline_type}
            </Tag>
          ) : (
            <span />
          )}
        </span>
        // <Tag color='blue'>{record.pipeline_type}</Tag>
      ),
    },
    {
      title: '用例id',
      dataIndex: 'case_id',
      width: '10%',
      ellipsis: false,
      render: (col, record, index) => (
        <span>
          <span>{record.case_id}</span>
          <Button type="text" onClick={() => onClickCaseDetail(record)}>
            [详情]
          </Button>
        </span>
      ),
    },
    {
      title: '执行结果',
      dataIndex: 'result_code',
      width: '10%',
      ellipsis: true,
      render: (col, record, index) => (
        <Space>
          {record.result_code > 0 ? (
            <Tag color="orange">{record.result_code}</Tag>
          ) : record.result_code < 0 ? (
            <Tag color="red">{record.result_code}</Tag>
          ) : (
            <Tag color="green">{record.result_code}</Tag>
          )}
          <Tooltip mini content={record.result_message}>
            {record.result_message.length > 20
              ? record.result_message.substring(0, 20) + '...'
              : record.result_message}
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '分支',
      dataIndex: 'branch',
      width: '8%',
      ellipsis: true,
      render: (col, record, index) => (
        <span>
          <Tooltip mini content={record.branch}>
            {record.branch.length > 20 ? record.branch.substring(0, 20) + '...' : record.branch}
          </Tooltip>
        </span>
      ),
    },
    {
      title: '端类型',
      dataIndex: 'platform',
      width: '6%',
      ellipsis: true,
      render: (col, record, index) => (
        <span>
          {record.platform?.length > 0 ? (
            <Tag color={colorList[record.platform.charCodeAt(0) % colorList.length]}>
              {record.platform}
            </Tag>
          ) : (
            <span />
          )}
        </span>
      ),
    },
    {
      title: '任务时间',
      dataIndex: 'pipeline_create_time',
      width: '9%',
      ellipsis: true,
      render: (col, record, index) => (
        <span>
          <Tooltip mini content={record.pipeline_create_time}>
            {record.pipeline_create_time.indexOf(' ') > 0
              ? record.pipeline_create_time.substring(0, record.pipeline_create_time.indexOf(' '))
              : record.pipeline_create_time}
          </Tooltip>
        </span>
      ),
    },
    {
      title: '模块',
      dataIndex: 'module',
      width: '6%',
      ellipsis: true,
      render: (col, record, index) => (
        <Tag color={colorList[record.module % colorList.length]}>
          {moduleMap.get(record.module)}
        </Tag>
      ),
    },
    {
      title: '负责人',
      dataIndex: 'qa_owner',
      width: '8%',
      ellipsis: true,
      render: (col, record, index) => (
        <Space>
          <Image
            preview={false}
            style={{ borderRadius: '50%' }}
            height="20"
            src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${record.qa_owner}?format=40x40.png`}
          ></Image>
          <Tooltip mini content={record.qa_owner}>
            {record.qa_owner.length > 8 ? record.qa_owner.substring(0, 8) + '...' : record.qa_owner}
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '归因类型',
      dataIndex: 'attribution_type',
      width: '8%',
      ellipsis: true,
      render: (col, record, index) => (
        <Tag color={colorList[record.attribution_type % colorList.length]}>
          {attributionMap.get(record.attribution_type)}
        </Tag>
      ),
    },
    {
      title: '失败原因分类',
      dataIndex: 'fail_reason_type',
      width: '8%',
      ellipsis: true,
      render: (col, record, index) => (
        <Select
          options={failReasonTypeList}
          labelInValue={true}
          onChange={(value) => onCaseListFailReasonTypeChange(value, record, false)}
          value={{
            value: record.fail_reason_type,
            label: failReasonTypeMap.get(record.fail_reason_type),
          }}
          triggerProps={{
            autoAlignPopupWidth: false,
            autoAlignPopupMinWidth: true,
            position: 'bl',
          }}
        >
          选项
        </Select>
        // <Tag color='blue'>{failReasonTypeMap.get(record.fail_reason_type)}</Tag>
      ),
    },
    {
      title: '失败原因分析',
      dataIndex: 'fail_reason_str',
      width: '12%',
      ellipsis: true,
      render: (col, record, index) => (
        <div ref={clickInputRef}>
          {showEditCaseListFailReasonStrMap.get(index) == true ? (
            <Input.Search
              size={'mini'}
              searchButton={<IconCheck />}
              onSearch={(value) => onCaseListFailReasonStrChange(value, record, index, false)}
              allowClear
              placeholder={'请输入'}
              defaultValue={record.fail_reason_str}
            />
          ) : (
            <div>
              <Button
                type="text"
                icon={<IconEdit />}
                onClick={(e) => onClickEditCaseListFailReasonStr(e, index)}
              />
              <Tooltip mini content={record.fail_reason_str}>
                {record.fail_reason_str.length > 12
                  ? record.fail_reason_str.substring(0, 12) + '...'
                  : record.fail_reason_str}
              </Tooltip>
              {/* <span>{record.fail_reason_str}</span> */}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '改进措施分析',
      dataIndex: 'improvement_measure_str',
      width: '12%',
      ellipsis: true,
      render: (col, record, index) => (
        <div ref={clickInputRef}>
          {showEditCaseListImprovementMeasureStrMap.get(index) == true ? (
            <Input.Search
              size={'mini'}
              // suffix={<Button type='text' icon={<IconClose />}></Button>}
              searchButton={<IconCheck />}
              onSearch={(value) =>
                onCaseListImprovementMeasureStrChange(value, record, index, false)
              }
              allowClear
              placeholder={'请输入'}
              defaultValue={record.improvement_measure_str}
            />
          ) : (
            <div>
              <Button
                type="text"
                icon={<IconEdit />}
                onClick={(e) => onClickEditCaseListImprovementMeasureStr(e, index)}
              />
              <Tooltip mini content={record.improvement_measure_str}>
                {record.improvement_measure_str.length > 12
                  ? record.improvement_measure_str.substring(0, 12) + '...'
                  : record.improvement_measure_str}
              </Tooltip>
              {/* <span>{record.improvement_measure_str}</span> */}
            </div>
          )}
        </div>
        // <Input placeholder={'请输入'} defaultValue={record.improvement_measure_str}
        //   onPressEnter={(e) => onCaseListImprovementMeasureStrChange(e.target.value, record)}
        // ></Input>
        // <Tag color='blue'>{failReasonTypeMap.get(record.fail_reason_type)}</Tag>
      ),
    },
    {
      title: '是否闭环',
      dataIndex: 'is_closed',
      width: '6%',
      ellipsis: true,
      render: (col, record, index) => (
        <Switch
          checkedText="是"
          uncheckedText="否"
          checked={record.is_closed}
          onChange={(value) => onCaseListIsCloesdChange(value, record, false)}
        />
      ),
    },
    // {
    //   title: '缺陷链接', dataIndex: 'meego_bug_url',
    //   render: (col, record, index) => (
    //     <span>
    //       {record.meego_bug_url == '' ?
    //         (<span>
    //           <Button type='text' onClick={sumbitBugToMeego}>一键提单</Button>
    //           <Button type='text' onClick={relateBugToMeego} >关联已有缺陷</Button>
    //         </span>) :
    //         (<Link href={record.meego_bug_url} target="_blank">缺陷链接</Link>)
    //       }
    //     </span>
    //   )
    // },
  ];
  // const defaultData = [
  //   {
  //     id: 0,
  //     platform: 'android',
  //     pipeline_type: 'CI',
  //     branch: 'main',
  //     pipeline_create_time: '2024/5/7 12:00:00',
  //     case_id: 'case_001',
  //     module: 4,
  //     qa_owner: 'maweijia.1211',
  //     result_code: -1,
  //     result_message: '断言失败',
  //     fail_reason_type: 1,
  //     fail_reason_str: '发现bug',
  //     improvement_measure_str: '暂无',
  //     is_closed: true,
  //     meego_bug_url: ''
  //   }
  // ];
  // 失败原因分类
  const failReasonTypeMap: Map<number, string> = new Map([
    [0, '未分类'],
    [1, '发现bug'],
    [2, '用例问题'],
    [3, '框架问题'],
    [4, '环境问题'],
    [5, '机器问题'],
    [6, '网络问题'],
  ]);

  const moduleMap: Map<number, string> = new Map([
    [-1, '未分类'],
    [1, '视频'],
    [2, '音频'],
    [3, '房间流'],
    [4, '网络'],
  ]);

  const attributionMap: Map<number, string> = new Map([
    [0, '人工分析'],
    [1, '自动归因'],
  ]);
  // const colorList = ['gray', 'blue', 'cyan', 'orange', 'green', 'purple']

  const typeList = ['CI', 'Daily'];

  const platformList = ['android', 'ios', 'linux', 'windows', 'mac'];

  const isClosedList = [
    {
      label: '是',
      value: true,
    },
    {
      label: '否',
      value: false,
    },
  ];

  function handleUpdateQueryByDialogAndCaseId(showDialog, caseDetailId) {
    console.log('query.toString()');
    console.log(query.toString());
    let queryStr = query.toString();
    queryStr =
      '?' +
      queryStr +
      (queryStr != undefined && queryStr.length > 0 ? '&' : '') +
      `${QUERY_NAME_DIALOG}=${showDialog}&${QUERY_NAME_TEST_CASE_DETAIL_ID}=${caseDetailId}`;
    navigate(queryStr);
  }

  function handleDeleteQueryByDialogAndCaseId() {
    setShowDialog(false);
    // console.log("query.toString()")
    // console.log(query.toString())
    query.delete(QUERY_NAME_DIALOG);
    query.delete(QUERY_NAME_TEST_CASE_DETAIL_ID);
    // console.log("query.toString()")
    // console.log(query.toString())
    const queryStr = '?' + query.toString();
    navigate(queryStr);
  }

  function onClickCaseDetail(caseData) {
    console.log(caseData);
    setShowDialog(true);
    handleUpdateQueryByDialogAndCaseId('true', caseData.id);
    setDialogData(caseData);
    fetchCaseDetail(caseData.id);
  }

  function setListByMap(map) {
    const list = [];
    for (const [key, value] of map) {
      list.push({ label: value, value: key });
    }
    return list;
  }

  // 要做延时请求
  const onFormDataChanges = useCallback(
    debounce((_, values) => {
      console.log('values');
      console.log(values);
      pagination.current = 1;
      setPagination(pagination);
      setFormData(values);
    }, 500),
    [],
  );

  // Dialog
  const [showDialog, setShowDialog] = useState(false);

  const fetchCaseDetail = async (id) => {
    const response = await BackendApis2.getCaseDetail({
      payload: { test_case_detail_id: id },
    }).finally(() => {
    });
    response.id = id;
    setDialogData(response);
    // setCaseDetail(response)
  };

  // function setCaseDetail(response) {
  //   dialogData.start_time = response.start_time
  //   dialogData.end_time = response.end_time
  //   dialogData.device_info_list = response.device_info_list
  //   dialogData.test_platform.job_id = response.test_platform.job_id
  //   dialogData.test_platform.job_url = response.test_platform.job_url
  //   dialogData.test_platform.test_task.pipeline_id = response.test_platform.test_task.pipeline_id
  //   dialogData.test_platform.test_task.task_url = response.test_platform.test_task.task_url
  // }

  const [dialogData, setDialogData] = useState({
    test_platform: {
      platform: '',
      watchcat_demo_url: '',
      case_log_url: '',
      start_time: '',
      job_url: '',
      job_id: 0,
      test_task: {
        pipeline_id: 0,
        task_url: '',
        branch: '',
        pipeline_type: '',
      },
    },
    id: 0,
    case_id: '',
    module: 0,
    qa_owner: '',
    result_code: 0,
    result_message: '',
    fail_reason_type: 0,
    fail_reason_str: '',
    improvement_measure_str: '',
    is_closed: true,
    meego_bug_url: '',
    start_time: '',
    end_time: '',
    cost_time: '',
    device_info_list: [],
    expect_assert: [],
    actual_assert: [],
    case_log_url: '',
  });

  const deviceTableColumns = [
    {
      title: 'demoDeviceId',
      dataIndex: 'device_id',
    },
    {
      title: 'rtcSid',
      dataIndex: 'rtc_sid',
    },
    {
      title: 'roomId',
      dataIndex: 'room_id',
    },
    {
      title: 'userId',
      dataIndex: 'user_id',
    },
    {
      title: 'trace链接',
      dataIndex: 'trace',
      render: (col, record, index) => (
        <Link href={record.trace} target="_blank">
          trace链接
        </Link>
      ),
    },
  ];

  return (
    <div>
      <Card bordered={false}>
        <Title heading={6} style={{ marginTop: 0 }}>
          CI&Daily失败用例排查
        </Title>
        <Form
          style={{ marginTop: '20px' }}
          wrapperCol={{ offset: 0, style: { marginRight: 0 } }}
          form={form}
          onChange={onFormDataChanges}
        >
          <Row gutter={10}>
            <Col span={3}>
              <Form.Item field="pipeline_type">
                <Select
                  placeholder={'类型'}
                  options={typeList}
                  allowClear
                  maxTagCount={1}
                  mode="multiple"
                >
                  选项
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item field="case_id">
                <Input placeholder={'用例id'}></Input>
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item field="result_code">
                <Select
                  placeholder={'错误码'}
                  options={resultCodeList}
                  allowClear
                  maxTagCount={1}
                  mode="multiple"
                  notFoundContent={
                    resultCodeFetching ? (
                      <div
                        style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                      >
                        <Spin style={{ margin: 12 }} />
                      </div>
                    ) : null
                  }
                  onSearch={searchResultCodes}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                >
                  选项
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item field="branch">
                <Select
                  placeholder={'分支'}
                  options={branchList}
                  allowClear
                  maxTagCount={1}
                  mode="multiple"
                  notFoundContent={
                    branchFetching ? (
                      <div
                        style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                      >
                        <Spin style={{ margin: 12 }} />
                      </div>
                    ) : null
                  }
                  onSearch={searchBranchs}
                  triggerProps={{
                    autoAlignPopupWidth: false,
                    autoAlignPopupMinWidth: true,
                    position: 'bl',
                  }}
                >
                  选项
                </Select>
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item field="platform">
                <Select
                  placeholder={'端类型'}
                  options={platformList}
                  allowClear
                  maxTagCount={1}
                  mode="multiple"
                >
                  选项
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item field="time">
                <DatePicker.RangePicker
                  showTime
                  placeholder={['任务时间区间', '任务时间区间']}
                  shortcutsPlacementLeft
                  shortcuts={[
                    {
                      text: '今天',
                      value: () => [dayjs().startOf('day'), dayjs().endOf('day')],
                    },
                    {
                      text: '昨天',
                      value: () => [
                        dayjs().subtract(1, 'day').startOf('day'),
                        dayjs().subtract(1, 'day').endOf('day'),
                      ],
                    },
                    {
                      text: '本周',
                      value: () => [
                        dayjs().startOf('week').add(1, 'day'),
                        dayjs().endOf('week').add(1, 'day'),
                      ],
                    },
                    {
                      text: '上周',
                      value: () => [
                        dayjs().subtract(1, 'week').startOf('week').add(1, 'day'),
                        dayjs().subtract(1, 'week').endOf('week').add(1, 'day'),
                      ],
                    },
                    {
                      text: '本月',
                      value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
                    },
                    {
                      text: '上个月',
                      value: () => [
                        dayjs().subtract(1, 'month').startOf('month'),
                        dayjs().subtract(1, 'month').endOf('month'),
                      ],
                    },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={10}>
            <Col span={3}>
              <Form.Item field="module">
                <Select
                  placeholder={'模块'}
                  options={moduleList}
                  allowClear
                  maxTagCount={1}
                  mode="multiple"
                >
                  选项
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item field="qa_owner">
                <Input placeholder={'负责人，输入邮箱前缀全称搜索'}></Input>
                {/* <Select placeholder={'负责人'} options={qaOwnerList} allowClear maxTagCount={1} mode='multiple'>选项</Select> */}
              </Form.Item>
            </Col>

            <Col span={3}>
              <Form.Item field="fail_reason_type">
                <Select
                  placeholder={'失败原因'}
                  options={failReasonTypeList}
                  allowClear
                  maxTagCount={1}
                  mode="multiple"
                >
                  选项
                </Select>
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item field="is_closed">
                <Select
                  placeholder={'是否闭环'}
                  options={isClosedList}
                  allowClear
                  maxTagCount={1}
                  mode="multiple"
                >
                  选项
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table
          style={{ marginTop: '0px' }}
          border={true}
          loading={loading}
          columns={columns}
          data={data}
          pagination={pagination}
          // onChange={() => fetchData(pagination, formData)}
          onChange={onChange}
        ></Table>
      </Card>

      <Modal
        visible={showDialog}
        title={<div style={{ textAlign: 'left' }}>{dialogData.case_id} 用例详情</div>}
        style={{ width: '80%', height: '95%' }}
        onOk={() => handleDeleteQueryByDialogAndCaseId()}
        onCancel={() => handleDeleteQueryByDialogAndCaseId()}
        autoFocus={false}
        focusLock={true}
        footer={null}
      >
        <Row>
          <Col span={24}>
            <Card bordered={false} style={{ backgroundColor: '#EBF5FB' }}>
              <Title heading={6} style={{ marginTop: 0 }}>
                用例基础信息
              </Title>
              <Row>
                <Col span={8}>
                  <span>
                    <b>用例id：</b>
                    {dialogData.case_id}
                  </span>
                </Col>
                <Col span={8}>
                  <span>
                    <b>模块：</b>
                    <Tag color={colorList[dialogData.module % colorList.length]}>
                      {moduleMap.get(dialogData.module)}
                    </Tag>
                  </span>
                </Col>
                <Col span={8}>
                  <span>
                    <b>负责人：</b>
                    <Space>
                      <Image
                        preview={false}
                        style={{ borderRadius: '50%' }}
                        height="20"
                        src={`https://cloud-page.bytedance.net/platform/api/v1/user/avatar/${dialogData.qa_owner}?format=40x40.png`}
                      ></Image>
                      <span>{dialogData.qa_owner}</span>
                      {/* <Tooltip mini content={dialogData.qa_owner}> */}
                      {/* {dialogData.qa_owner.length > 8 ? dialogData.qa_owner.substring(0, 8) + '...' : dialogData.qa_owner} */}
                      {/* </Tooltip> */}
                    </Space>
                  </span>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
        <Row gutter={10} style={{ marginTop: '10px' }}>
          <Col span={12}>
            <Card
              bordered={false}
              style={{
                backgroundColor: '#EBF5FB',
                height: 450,
                maxHeight: 450,
                overflow: 'scroll',
              }}
            >
              <Title heading={6} style={{ marginTop: 0 }}>
                执行基础信息
              </Title>
              <Row>
                <Col span={6}>
                  <span>
                    <b>执行类型：</b>
                    <span>
                      {dialogData.test_platform?.test_task?.pipeline_type?.length > 0 ? (
                        <Tag
                          color={
                            colorList[
                            (dialogData.test_platform?.test_task?.pipeline_type.charCodeAt(0) +
                              1) %
                            colorList.length
                              ]
                          }
                        >
                          {dialogData.test_platform?.test_task?.pipeline_type}
                        </Tag>
                      ) : (
                        <span />
                      )}
                    </span>
                  </span>
                </Col>
                <Col span={8}>
                  <span>
                    <b>分支：</b>
                    {dialogData.test_platform?.test_task?.branch}
                  </span>
                </Col>
                <Col span={6}>
                  <span>
                    <b>端类型：</b>
                    <span>
                      {dialogData.test_platform?.platform?.length > 0 ? (
                        <Tag
                          color={
                            colorList[
                            dialogData.test_platform?.platform.charCodeAt(0) % colorList.length
                              ]
                          }
                        >
                          {dialogData.test_platform?.platform}
                        </Tag>
                      ) : (
                        <span />
                      )}
                    </span>
                  </span>
                </Col>
                <Col span={4}>
                  <span>
                    <b>测试包：</b>
                    <Link href={dialogData.test_platform?.watchcat_demo_url}>链接</Link>
                  </span>
                </Col>
              </Row>
              <Row>
                <Col span={14}>
                  <span>
                    <b>测试时间：</b>
                    {dialogData.start_time?.replace(/-/g, '/').replace(/T/g, ' ')}—
                    {dialogData.end_time?.replace(/-/g, '/').replace(/T/g, ' ')}
                  </span>
                </Col>
                <Col span={10}>
                  <span>
                    <b>执行耗时：</b>
                    {dialogData.cost_time} s
                  </span>
                </Col>
              </Row>
              <Row>
                <Col span={14}>
                  <span>
                    <b>pipeline id：</b>
                    <Link href={dialogData.test_platform?.test_task?.task_url} target="_blank">
                      {dialogData.test_platform?.test_task?.pipeline_id}
                    </Link>
                  </span>
                </Col>
                <Col span={10}>
                  <span>
                    <b>job id：</b>
                    <Link href={dialogData.test_platform?.job_url} target="_blank">
                      {dialogData.test_platform?.job_id}
                    </Link>
                  </span>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <span>
                    <b>设备信息</b>
                  </span>
                </Col>
              </Row>
              <Row>
                <Table
                  style={{ marginTop: '4px', width: '100%' }}
                  columns={deviceTableColumns}
                  data={dialogData.device_info_list != undefined ? dialogData.device_info_list : []}
                ></Table>
              </Row>
            </Card>
          </Col>
          <Col span={12}>
            <Card
              bordered={false}
              style={{
                backgroundColor: '#EBF5FB',
                height: 450,
                maxHeight: 450,
                overflow: 'scroll',
              }}
            >
              <Title heading={6} style={{ marginTop: 0 }}>
                执行结果信息
              </Title>
              <Row>
                <Col span={12}>
                  <span>
                    <b>错误码：</b>
                    {dialogData.result_code > 0 ? (
                      <Tag color="orange">{dialogData.result_code}</Tag>
                    ) : dialogData.result_code < 0 ? (
                      <Tag color="red">{dialogData.result_code}</Tag>
                    ) : (
                      <Tag color="green">{dialogData.result_code}</Tag>
                    )}
                  </span>
                </Col>
                <Col span={12}>
                  <span>
                    <b>错误码描述：</b>
                    {dialogData.result_message.length > 80 ? (
                      <Tooltip mini content={dialogData.result_message}>
                        {dialogData.result_message.substring(0, 80) + '...'}
                      </Tooltip>
                    ) : (
                      <span>{dialogData.result_message}</span>
                    )}
                  </span>
                </Col>
              </Row>
              <Row>
                {/* <Col span={12}><span>预期断言：</span>
                  <Card bordered={false} style={{ maxWidth: '95%', overflow: 'scroll' }}>
                      {dialogData.expect_assert?.map(item => (
                        <div style={{marginBottom:'5px'}}><IconCaretRight />{item}</div>))}
                  </Card>
                </Col>
                <Col span={12}><span>实际断言：</span>
                  <Card bordered={false} style={{ maxWidth: '95%', overflow: 'scroll' }}>
                  {dialogData.actual_assert?.map(item => (
                        item.includes('成功')?<div style={{marginBottom:'5px',color:'green'}}><IconCaretRight />{item}</div>:
                        item.includes('失败')?<div style={{marginBottom:'5px',color:'red'}}><IconCaretRight />{item}</div>:
                        <div style={{marginBottom:'5px'}}><IconCaretRight />{item}</div>))}
                    </Card>
                </Col> */}
                <Col span={24}>
                  <span>
                    <b>预期断言：</b>
                  </span>
                  <Card bordered={false} style={{ maxHeight: 160, overflow: 'scroll' }}>
                    {dialogData.expect_assert?.map((item) => (
                      <div style={{ marginBottom: '5px' }}>
                        <IconCaretRight />
                        {item}
                      </div>
                    ))}
                  </Card>
                </Col>
              </Row>
              <Row style={{ marginTop: '5px' }}>
                <Col span={24}>
                  <span>
                    <b>实际断言：</b>
                  </span>
                  <Card bordered={false} style={{ maxHeight: 160, overflow: 'scroll' }}>
                    {dialogData.actual_assert?.map((item) =>
                      item.includes('成功') ? (
                        <div style={{ marginBottom: '5px', color: 'green' }}>
                          <IconCaretRight />
                          {item}
                        </div>
                      ) : item.includes('失败') ? (
                        <div style={{ marginBottom: '5px', color: 'red' }}>
                          <IconCaretRight />
                          {item}
                        </div>
                      ) : (
                        <div style={{ marginBottom: '5px' }}>
                          <IconCaretRight />
                          {item}
                        </div>
                      ),
                    )}
                  </Card>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
        <Row gutter={10} style={{ marginTop: '10px' }}>
          <Col span={12}>
            <Card
              bordered={false}
              style={{
                backgroundColor: '#EBF5FB',
                height: 150,
                maxHeight: 150,
                overflow: 'scroll',
              }}
            >
              <Tabs defaultActiveTab="1">
                <TabPane key="1" title={'原因分析'}>
                  <Typography.Paragraph>
                    <Form labelCol={{ span: 9 }} labelAlign={'right'} wrapperCol={{ span: 15 }}>
                      <Row>
                        <Col span={10}>
                          <Form.Item label="失败原因分类">
                            <Select
                              placeholder={'失败原因分类'}
                              options={failReasonTypeList}
                              labelInValue={true}
                              onChange={(value) =>
                                onCaseListFailReasonTypeChange(value, dialogData, true)
                              }
                              value={{
                                value: dialogData.fail_reason_type,
                                label: failReasonTypeMap.get(dialogData.fail_reason_type),
                              }}
                              triggerProps={{
                                autoAlignPopupWidth: false,
                                autoAlignPopupMinWidth: true,
                                position: 'bl',
                              }}
                            >
                              选项
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={14}>
                          <Form.Item label="失败原因分析">
                            <div ref={clickInputRef}>
                              {showEditCaseListFailReasonStrMap.get(-1) == true ? (
                                <Input.Search
                                  size={'mini'}
                                  searchButton={<IconCheck />}
                                  onSearch={(value) =>
                                    onCaseListFailReasonStrChange(value, dialogData, -1, true)
                                  }
                                  allowClear
                                  placeholder={'请输入'}
                                  defaultValue={dialogData.fail_reason_str}
                                />
                              ) : (
                                <div>
                                  <Button
                                    type="text"
                                    icon={<IconEdit />}
                                    onClick={(e) => onClickEditCaseListFailReasonStr(e, -1)}
                                  />
                                  <span>{dialogData.fail_reason_str}</span>
                                </div>
                              )}
                            </div>
                            {/* <Input placeholder={'失败原因分析'} defaultValue={dialogData.fail_reason_str}></Input> */}
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={10}>
                          <Form.Item label="是否闭环">
                            <Switch
                              checkedText="是"
                              uncheckedText="否"
                              checked={dialogData.is_closed}
                              onChange={(value) =>
                                onCaseListIsCloesdChange(value, dialogData, true)
                              }
                            />
                          </Form.Item>
                        </Col>
                        <Col span={14}>
                          <Form.Item label="改进措施分析">
                            <div ref={clickInputRef}>
                              {showEditCaseListImprovementMeasureStrMap.get(-1) == true ? (
                                <Input.Search
                                  size={'mini'}
                                  searchButton={<IconCheck />}
                                  onSearch={(value) =>
                                    onCaseListImprovementMeasureStrChange(
                                      value,
                                      dialogData,
                                      -1,
                                      true,
                                    )
                                  }
                                  allowClear
                                  placeholder={'请输入'}
                                  defaultValue={dialogData.improvement_measure_str}
                                />
                              ) : (
                                <div>
                                  <Button
                                    type="text"
                                    icon={<IconEdit />}
                                    onClick={(e) => onClickEditCaseListImprovementMeasureStr(e, -1)}
                                  />
                                  <span>{dialogData.improvement_measure_str}</span>
                                </div>
                              )}
                            </div>
                            {/* <Input placeholder={'改进措施分析'} defaultValue={dialogData.improvement_measure_str}></Input> */}
                          </Form.Item>
                        </Col>
                      </Row>
                      {/* <Row>
                        <Col span={10}>
                          <Form.Item label='缺陷链接'>
                            <Button type='text' onClick={sumbitBugToMeego}>一键提单</Button>
                            <Button type='text' onClick={relateBugToMeego}>关联已有缺陷</Button>
                          </Form.Item>
                        </Col>
                      </Row> */}
                    </Form>
                  </Typography.Paragraph>
                </TabPane>
                <TabPane key="2" title={'操作记录'}>
                  <Typography.Paragraph></Typography.Paragraph>
                </TabPane>
              </Tabs>
            </Card>
          </Col>
          <Col span={12}>
            <Card
              bordered={false}
              style={{
                backgroundColor: '#EBF5FB',
                height: 150,
                maxHeight: 150,
                overflow: 'scroll',
              }}
            >
              <Title heading={6} style={{ marginTop: 0 }}>
                日志文件链接
              </Title>
              {dialogData.test_platform?.case_log_url != undefined &&
              dialogData.test_platform?.case_log_url != '' ? (
                <Link href={dialogData.test_platform?.case_log_url} target="_blank">
                  控制台日志zip：{dialogData.test_platform?.case_log_url}
                </Link>
              ) : (
                <span />
              )}
              {/* <iframe src={dialogData.case_log_url} style={{ backgroundColor: 'white', width: '100%', overflow: 'scroll' }} /> */}
            </Card>
          </Col>
        </Row>
      </Modal>
    </div>
  );
}
