import React from 'react';
import { Grid, Space, Typography } from '@arco-design/web-react';
import { useUser } from '@devsre/react-utils';
import {
  Docs,
  Calendar,
  Announcement,
  Overview,
  ContentPercentage,
  Shortcuts,
  PopularContent,
  Powered,
} from 'src/components';
import styles from 'src/components/style/index.module.less';
const { Row, Col } = Grid;

const gutter = 16;

function Home() {
  const user = useUser();
  return (
    <>
      <div className={styles.wrapper}>
        <Typography.Title heading={5} style={{ margin: '20px 10px 20px 20px' }}>{`Welcome Back, ${
          user.name || user.nickname
        }`}</Typography.Title>
        {/* <Space
          style={{ margin: '20px 10px 20px 20px' }}
          direction="vertical"
          className={styles.
          left}
        >
          <Overview />
          <Row gutter={gutter}>
            <Col span={12}>
              <PopularContent />
            </Col>
            <Col span={12}>
              <ContentPercentage />
            </Col>
          </Row>
        </Space> */}
        {/* <Space
          style={{ margin: '20px 20px 20px 10px' }}
          className={styles.right}
          direction="vertical"
        >
          <Shortcuts />
          <Calendar />
          <Announcement />
          <Docs />
        </Space> */}
      </div>
      {/* <Powered /> */}
    </>
  );
}

export default Home;
