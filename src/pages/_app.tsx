import React from 'react';
import { Layout } from '@devsre/react-utils';
import 'src/components/style/global.less';
import Menu from 'src/menu.json';
import { ReactComponent as Logo } from 'src/logo.svg';

function MyApp({ pageProps }) {
  return (
    <Layout
      title="Global Business 测试平台"
      menus={Menu}
      logo={<Logo />}
      menuConfig={{ theme: 'light' }}
      toolbar={{
        theme: 'light',
        idc: {
          customIdc: [
            { host: 'gstest.sre.bytedance.net', name: 'C<PERSON>', idc: 'lf' },
            { host: 'gstest.sre-us.bytedance.net', name: 'I18N', idc: 'maliva' },
          ],
        },
      }}
      docs={[
        {
          url: 'https://bytedance.larkoffice.com/sheets/W6EusptBnhWtqMthLQtcGgzbnFf',
          title: '反馈建议',
        },
      ]}
      showAppSwitch={false}
      {...pageProps}
    ></Layout>
  );
}

export default MyApp;
