import React from 'react';
import Chart from 'bizcharts/es/components/Chart';
import Line from 'bizcharts/es/geometry/Line';
import Axis from 'bizcharts/es/components/Axis';
import Area from 'bizcharts/es/geometry/Area';
import Tooltip from 'bizcharts/es/components/Tooltip';
import Coordinate from 'bizcharts/es/components/Coordinate';
import Legend from 'bizcharts/es/components/Legend';
import { CustomTooltip } from './customer-tooltip';
import { Spin } from '@arco-design/web-react';

interface AreaPolarProps {
  data: any[];
  loading: boolean;
  fields: string[];
  height: number;
}

function transform(data, fields) {
  const rows = [];
  data.forEach((_) => {
    const item = _.item;
    fields.forEach((filed) => {
      rows.push({
        item,
        category: filed,
        score: _[filed],
      });
    });
  });
  return rows;
}

export function AreaPolar(props: AreaPolarProps) {
  const { data, loading, fields, height } = props;
  const rows = transform(data, fields);

  return (
    <Spin loading={loading} style={{ width: '100%' }}>
      <Chart
        height={height || 400}
        padding={0}
        data={rows}
        autoFit
        scale={{
          score: {
            min: 0,
            max: 80,
          },
        }}
        interactions={['legend-highlight']}
        className={'chart-wrapper'}
      >
        <Coordinate type="polar" radius={0.8} />
        <Tooltip shared>
          {(title, items) => {
            return <CustomTooltip title={title} data={items} />;
          }}
        </Tooltip>
        <Line
          position="item*score"
          size="2"
          color={['category', ['#313CA9', '#21CCFF', '#249EFF']]}
        />
        <Area
          position="item*score"
          tooltip={false}
          color={[
            'category',
            ['rgba(49, 60, 169, 0.4)', 'rgba(33, 204, 255, 0.4)', 'rgba(36, 158, 255, 0.4)'],
          ]}
        />
        <Axis name="score" label={false} />
        <Legend
          position="right"
          marker={(_, index) => {
            return {
              symbol: 'circle',
              style: {
                r: 4,
                lineWidth: 0,
                fill: ['#313CA9', '#21CCFF', '#249EFF'][index],
              },
            };
          }}
          name="category"
        />
      </Chart>
    </Spin>
  );
}
