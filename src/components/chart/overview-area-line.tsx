import React, { useEffect, useMemo, useState } from 'react';
import Chart from 'bizcharts/es/components/Chart';
import Line from 'bizcharts/es/geometry/Line';
import Axis from 'bizcharts/es/components/Axis';
import Area from 'bizcharts/es/geometry/Area';
import Tooltip from 'bizcharts/es/components/Tooltip';
import { Spin, Message } from '@arco-design/web-react';
import dayjs from 'dayjs';
import { CustomTooltip } from './customer-tooltip';
import { Apis } from 'src/utils/apis';

export function OverviewAreaLine({
  name = 'Total content',
  color = '#4080FF',
}: {
  name?: string;
  color?: string;
}) {
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const query = useMemo(() => {
    const from = dayjs().subtract(9, 'month').format('YYYYMMDD');
    const to = dayjs().format('YYYYMMDD');
    return {
      from,
      to,
      unit: 'month',
    };
  }, []);

  const parseDate = (value) => {
    return {
      x: `${value.startDate.slice(0, 4)}-${value.startDate.slice(4, 6)}`,
      y: value.count,
    };
  };

  const fetchData = () => {
    setLoading(true);
    Apis.getBnpmDownPackageBulk({
      payload: query,
    })
      .then((res) => {
        if (res.status_code === 0) {
          const data = res && res.data.buckets.map(parseDate);
          setData(data);
        } else {
          Message.error(`Request failed: ${res.message}`);
        }
      })
      .catch((err) => {
        Message.error(`Request failed: ${err.message}`);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <Spin loading={loading} style={{ width: '100%' }}>
      <Chart
        scale={{ value: { min: 0 } }}
        padding={[10, 20, 50, 40]}
        autoFit
        height={300}
        data={data}
        className={'chart-wrapper'}
      >
        <Axis
          name="y"
          title
          grid={{
            line: {
              style: {
                lineDash: [4, 4],
              },
            },
          }}
          label={{
            formatter(text) {
              return `${Number(text) / 100}k`;
            },
          }}
        />
        <Axis name="x" grid={{ line: { style: { stroke: '#E5E8EF' } } }} />
        <Line
          shape="smooth"
          position="x*y"
          size={3}
          color="l (0) 0:#1EE7FF .57:#249AFF .85:#6F42FB"
        />
        <Area
          position="x*y"
          shape="smooth"
          color="l (90) 0:rgba(17, 126, 255, 0.5)  1:rgba(17, 128, 255, 0)"
        />
        <Tooltip
          showCrosshairs={true}
          showMarkers={true}
          marker={{
            lineWidth: 3,
            stroke: color,
            fill: '#ffffff',
            symbol: 'circle',
            r: 8,
          }}
        >
          {(title, items) => {
            return (
              <CustomTooltip
                title={title}
                data={items}
                color={color}
                name={name}
                formatter={(value) => Number(value).toLocaleString()}
              />
            );
          }}
        </Tooltip>
      </Chart>
    </Spin>
  );
}
