import React, { useState } from 'react';
import { Link, Tag, Tooltip, Modal, Button, Space } from '@arco-design/web-react';
import { IconFile, IconDownload, IconEye } from '@arco-design/web-react/icon';

interface UrlLinksDisplayProps {
  urls: Record<string, string> | null;
  emptyText?: string;
  maxDisplay?: number;
  showFileIcon?: boolean;
  linkStyle?: React.CSSProperties;
  containerStyle?: React.CSSProperties;
  layout?: 'horizontal' | 'vertical';
  showModal?: boolean; // 是否显示弹窗模式
  modalTitle?: string; // 弹窗标题
}

/**
 * URL链接显示组件
 * 用于显示多个文件链接，支持新旧格式兼容
 */
export const UrlLinksDisplay: React.FC<UrlLinksDisplayProps> = ({
  urls,
  emptyText = '无文件',
  maxDisplay = 3,
  showFileIcon = true,
  linkStyle = {},
  containerStyle = {},
  layout = 'vertical',
  showModal = false,
  modalTitle = '文件列表'
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  if (!urls || Object.keys(urls).length === 0) {
    return <span style={{ color: '#86909c', fontSize: '12px' }}>{emptyText}</span>;
  }

  const urlEntries = Object.entries(urls);
  const displayUrls = showModal ? urlEntries.slice(0, maxDisplay) : urlEntries;
  const remainingCount = urlEntries.length - maxDisplay;

  const defaultLinkStyle: React.CSSProperties = {
    fontSize: '12px',
    color: '#4080ff',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    ...linkStyle
  };

  const defaultContainerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: layout === 'vertical' ? 'column' : 'row',
    gap: layout === 'vertical' ? '4px' : '8px',
    flexWrap: layout === 'horizontal' ? 'wrap' : 'nowrap',
    ...containerStyle
  };

  // 获取文件扩展名来判断文件类型
  const getFileType = (filename: string) => {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'log':
        return { color: '#ff7875', text: 'LOG' };
      case 'json':
        return { color: '#52c41a', text: 'JSON' };
      case 'zip':
        return { color: '#722ed1', text: 'ZIP' };
      case 'txt':
        return { color: '#1890ff', text: 'TXT' };
      default:
        return { color: '#86909c', text: 'FILE' };
    }
  };

  // 简化文件名显示
  const getDisplayName = (filename: string) => {
    // 如果文件名太长，只显示前面部分和扩展名
    if (filename.length > 20) {
      const parts = filename.split('.');
      if (parts.length > 1) {
        const ext = parts.pop();
        const name = parts.join('.');
        return `${name.substring(0, 15)}...${ext}`;
      }
      return `${filename.substring(0, 18)}...`;
    }
    return filename;
  };

  // 如果启用弹窗模式
  if (showModal) {
    return (
      <>
        <Button
          type="text"
          size="small"
          icon={<IconEye />}
          onClick={() => setIsModalVisible(true)}
          style={{ fontSize: '12px', padding: '4px 8px' }}
        >
          查看文件 ({urlEntries.length})
        </Button>

        <Modal
          title={modalTitle}
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          footer={null}
          style={{ width: '600px' }}
        >
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            <Space direction="vertical" size="medium" style={{ width: '100%' }}>
              {urlEntries.map(([filename, url]) => {
                const fileType = getFileType(filename);

                return (
                  <div key={filename} style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '8px 12px',
                    border: '1px solid #f0f0f0',
                    borderRadius: '6px',
                    backgroundColor: '#fafafa'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flex: 1 }}>
                      {showFileIcon && (
                        <IconFile style={{ fontSize: '16px', color: fileType.color }} />
                      )}
                      <span style={{ fontSize: '14px', wordBreak: 'break-all' }}>{filename}</span>
                      <Tag
                        size="small"
                        style={{
                          fontSize: '10px',
                          padding: '2px 6px',
                          backgroundColor: fileType.color,
                          color: '#fff',
                          border: 'none',
                          borderRadius: '3px'
                        }}
                      >
                        {fileType.text}
                      </Tag>
                    </div>
                    <Button
                      type="primary"
                      size="small"
                      icon={<IconDownload />}
                      onClick={() => window.open(url, '_blank')}
                    >
                      下载
                    </Button>
                  </div>
                );
              })}
            </Space>
          </div>
        </Modal>
      </>
    );
  }

  // 默认模式：直接显示链接
  return (
    <div style={defaultContainerStyle}>
      {displayUrls.map(([filename, url]) => {
        const fileType = getFileType(filename);
        const displayName = getDisplayName(filename);

        return (
          <Tooltip key={filename} content={`点击下载: ${filename}`}>
            <Link
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              style={defaultLinkStyle}
            >
              {showFileIcon && (
                <IconFile style={{ fontSize: '14px', color: fileType.color }} />
              )}
              <span>{displayName}</span>
              <Tag
                size="small"
                style={{
                  fontSize: '10px',
                  padding: '1px 4px',
                  backgroundColor: fileType.color,
                  color: '#fff',
                  border: 'none',
                  borderRadius: '2px',
                  marginLeft: '4px'
                }}
              >
                {fileType.text}
              </Tag>
            </Link>
          </Tooltip>
        );
      })}

      {remainingCount > 0 && (
        <span style={{
          fontSize: '11px',
          color: '#86909c',
          fontStyle: 'italic'
        }}>
          +{remainingCount} 个文件...
        </span>
      )}
    </div>
  );
};

/**
 * 日志链接显示组件
 */
export const LogLinksDisplay: React.FC<Omit<UrlLinksDisplayProps, 'emptyText' | 'modalTitle'>> = (props) => (
  <UrlLinksDisplay {...props} emptyText="无日志" showModal={true} modalTitle="执行日志文件" />
);

/**
 * 性能数据链接显示组件
 */
export const PerfDataLinksDisplay: React.FC<Omit<UrlLinksDisplayProps, 'emptyText' | 'modalTitle'>> = (props) => (
  <UrlLinksDisplay {...props} emptyText="无性能数据" showModal={true} modalTitle="性能数据文件" />
);

export default UrlLinksDisplay;
