import React from 'react';
import { Link, Card, Message, Typography } from '@arco-design/web-react';
import {
  IconFile,
  IconStorage,
  IconSettings,
  IconMobile,
  IconFire,
} from '@arco-design/web-react/icon';
import styles from './style/shortcuts.module.less';

export function Shortcuts() {
  const shortcuts = [
    {
      title: 'Management',
      key: 'Content Management',
      icon: <IconFile />,
    },
    {
      title: 'Statistic',
      key: 'Content Statistic',
      icon: <IconStorage />,
    },
    {
      title: 'Advance',
      key: 'Advanced Management',
      icon: <IconSettings />,
    },
    {
      title: 'Promotion',
      key: 'Online Promotion',
      icon: <IconMobile />,
    },
    {
      title: 'Marketing',
      key: 'Marketing',
      icon: <IconFire />,
    },
  ];

  function onClickShortcut(key) {
    Message.info({
      content: (
        <span>
          You clicked <b>{key}</b>
        </span>
      ),
    });
  }

  return (
    <Card style={{ marginBottom: '12px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography.Title heading={6} style={{ marginTop: 0 }}>
          Shortcuts
        </Typography.Title>
        <Link>See More</Link>
      </div>
      <div className={styles.shortcuts}>
        {shortcuts.map((shortcut) => (
          <div
            className={styles.item}
            key={shortcut.key}
            onClick={() => onClickShortcut(shortcut.key)}
          >
            <div className={styles.icon}>{shortcut.icon}</div>
            <div className={styles.title}>{shortcut.title}</div>
          </div>
        ))}
      </div>
    </Card>
  );
}
