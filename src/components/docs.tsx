import React from 'react';
import { <PERSON>, <PERSON>, Typography } from '@arco-design/web-react';
import styles from './style/docs.module.less';

export function Docs() {
  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography.Title heading={6} style={{ marginTop: 0 }}>
          Document
        </Typography.Title>
        <Link>See More</Link>
      </div>
      <div className={styles.docs}>
        <Link className={styles.link} href="https://sre.bytedance.net" target="_blank">
          DevSRE
        </Link>
        <Link className={styles.link} href="https://arco.bytedance.net" target="_blank">
          Management
        </Link>
        <Link className={styles.link} href="https://arco.bytedance.net" target="_blank">
          UI Components
        </Link>
      </div>
    </Card>
  );
}
