.card {
  .statistic {
    display: flex;
  }

  .title {
    margin: 0;
  }

  :global(.arco-statistic-content) {
    margin-top: 12px;
    margin-bottom: 4px;
    display: flex;

    :global(.arco-statistic-value) {
      font-size: 24px;
      line-height: 28px;
    }
  }

  .diff {
    margin-left: 12px;
    line-height: 20px;
    color: rgb(var(--red-6));
  }

  .diff-increment {
    color: rgb(var(--green-6));
  }

  .tooltip {
    color: var(--color-text-1);
    padding: 10px 0px;
    background: var(--color-bg-5);
  }

  :global(.bizcharts-tooltip) {
    background: var(--color-bg-5) !important;
    color: var(--color-text-1) !important;
    box-shadow: 2px 2px 5px rgba(19, 78, 196, 0.1) !important;
    opacity: 1 !important;
  }
}
