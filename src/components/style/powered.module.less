.wrapper {
  padding: 50px 0 30px 0;
  margin: 0 auto;
}

.wrapper > a {
  position: relative;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  height: 28px;
  width: 182px;
  background: var(--color-bg-2);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  text-decoration: none;
  outline: none;
  color: var(--color-text-2);
  font-size: 12px;
  box-sizing: border-box;
  transition: all .2s;
  cursor: pointer;
}

.wrapper > a:hover {
  color: rgb(var(--arcoblue-6));

  .icon-gray {
    opacity: 0;
  }
  .icon-highlight {
    opacity: 1;
  }
}

.content {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.icon-gray {
  opacity: 1;
  position: absolute;
  line-height: 1px;
}

.icon-highlight {
  opacity: 0;
  position: absolute;
  line-height: 1px;
}