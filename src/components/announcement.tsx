import React, { useState, useEffect } from 'react';
import { Link, Card, Skeleton, Tag, Typography } from '@arco-design/web-react';
import { Apis } from 'src/utils/apis';
import styles from './style/announcement.module.less';

export function Announcement() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    setLoading(true);
    const { data } = await Apis.getAnnouncement().finally(() => {
      setLoading(false);
    });
    setData(data);
  };

  useEffect(() => {
    fetchData();
  }, []);

  function getTagColor(type) {
    switch (type) {
      case 'activity':
        return 'orangered';
      case 'info':
        return 'cyan';
      case 'notice':
        return 'arcoblue';
      default:
        return 'arcoblue';
    }
  }

  return (
    <Card style={{ marginBottom: '12px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography.Title heading={6} style={{ marginTop: 0 }}>
          Announcement
        </Typography.Title>
        <Link>See More</Link>
      </div>
      <Skeleton loading={loading} text={{ rows: 5, width: '100%' }} animation>
        <div>
          {data.map((d, index) => (
            <div key={index} className={styles.item}>
              <Tag color={getTagColor(d.type)} size="small">
                {d.tag}
              </Tag>
              <span className={styles.link}>{d.content}</span>
            </div>
          ))}
        </div>
      </Skeleton>
    </Card>
  );
}
