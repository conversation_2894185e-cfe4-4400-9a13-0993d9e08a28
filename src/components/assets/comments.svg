<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_161_33236)">
<path d="M20.4884 3.5143C21.5946 4.62323 22.4625 5.9143 23.0679 7.3527C23.692 8.84199 24.008 10.425 24.008 12.0536C24 13.6768 23.6786 15.2491 23.0464 16.7277C22.4384 18.1554 21.5679 19.4358 20.4616 20.5313C19.358 21.6268 18.0696 22.484 16.6366 23.0813C15.1714 23.6893 13.6152 24 12.0107 24H11.9544C10.9717 23.9958 10.0284 23.677 9.11446 23.2543C7.52892 22.5211 5.79636 22.1752 4.07277 22.4593L3.19059 22.6048C2.74817 22.6777 2.26797 22.7683 1.88519 22.5348C1.71473 22.4308 1.57149 22.2866 1.46868 22.1153C1.2409 21.7359 1.32941 21.2627 1.40047 20.8259L1.54851 19.9159C1.82774 18.1994 1.48269 16.4748 0.753006 14.8962C0.330661 13.9826 0.0122489 13.0404 0.00799498 12.0563C-4.07491e-05 10.4357 0.310674 8.85806 0.926747 7.37413C1.51871 5.94109 2.38121 4.65537 3.47407 3.54912C4.56961 2.44287 5.84997 1.57501 7.27765 0.964294C8.75622 0.33215 10.3285 0.0107213 11.9518 0.00268555H12.0053C13.6152 0.00268555 15.1795 0.316079 16.6527 0.93483C18.0911 1.53751 19.3821 2.40805 20.4884 3.5143Z" fill="#FDA979"/>
</g>
<g filter="url(#filter1_dii_161_33236)">
<path d="M18.2016 13.5312C17.3786 13.5312 16.7141 12.8477 16.7141 12C16.7141 11.1523 17.3786 10.4688 18.2016 10.4688C19.0247 10.4688 19.6891 11.1523 19.6891 12C19.6919 12.8477 19.0247 13.5312 18.2016 13.5312ZM12.0028 13.5312C11.1797 13.5312 10.5153 12.8477 10.5153 12C10.5153 11.1523 11.1797 10.4688 12.0028 10.4688C12.8258 10.4688 13.4903 11.1523 13.4903 12C13.4876 12.8477 12.8231 13.5312 12.0028 13.5312Z" fill="white"/>
</g>
<g filter="url(#filter2_dii_161_33236)">
<path d="M4.31378 12C4.31378 12.2011 4.35226 12.4002 4.42701 12.586C4.50177 12.7718 4.61133 12.9406 4.74946 13.0828C4.88759 13.2249 5.05157 13.3377 5.23204 13.4147C5.41251 13.4916 5.60594 13.5312 5.80128 13.5312C5.99662 13.5312 6.19005 13.4916 6.37052 13.4147C6.551 13.3377 6.71498 13.2249 6.8531 13.0828C6.99123 12.9406 7.1008 12.7718 7.17555 12.586C7.25031 12.4002 7.28878 12.2011 7.28878 12C7.28878 11.5939 7.13206 11.2044 6.8531 10.9172C6.57414 10.6301 6.19579 10.4688 5.80128 10.4688C5.40677 10.4688 5.02842 10.6301 4.74946 10.9172C4.4705 11.2044 4.31378 11.5939 4.31378 12Z" fill="white"/>
</g>
<defs>
<filter id="filter0_ii_161_33236" x="0.00784302" y="-2.95135" width="24.0002" height="28.0583" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.95403"/>
<feGaussianBlur stdDeviation="1.84627"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.78 0 0 0 0 0 0 0 0 0.29 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_161_33236"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.10694"/>
<feGaussianBlur stdDeviation="1.05495"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_161_33236" result="effect2_innerShadow_161_33236"/>
</filter>
<filter id="filter1_dii_161_33236" x="6.74583" y="8.75536" width="16.7127" height="10.6014" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.05607"/>
<feGaussianBlur stdDeviation="1.88473"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8625 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33236"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33236" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.37071"/>
<feGaussianBlur stdDeviation="0.856696"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.925 0 0 0 0 0.320667 0 0 0 0 0.0616667 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33236"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.514017"/>
<feGaussianBlur stdDeviation="0.342678"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33236" result="effect3_innerShadow_161_33236"/>
</filter>
<filter id="filter2_dii_161_33236" x="0.544321" y="8.75536" width="10.5139" height="10.6014" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.05607"/>
<feGaussianBlur stdDeviation="1.88473"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8625 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_161_33236"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_161_33236" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.37071"/>
<feGaussianBlur stdDeviation="0.856696"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.925 0 0 0 0 0.320667 0 0 0 0 0.0616667 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_161_33236"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.514017"/>
<feGaussianBlur stdDeviation="0.342678"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_161_33236" result="effect3_innerShadow_161_33236"/>
</filter>
</defs>
</svg>
