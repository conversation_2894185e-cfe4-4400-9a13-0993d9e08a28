import React, { useEffect, useState, useMemo } from 'react';
import { Card, Typography, Grid, Statistic, Skeleton } from '@arco-design/web-react';
import { IconUser, IconEdit, IconHeart, IconThumbUp } from '@arco-design/web-react/icon';
import styles from './style/analysis-data-overview.module.less';
import { MultiAreaLine } from 'src/components/chart/multi-area-line';
import { Apis } from 'src/utils/apis';

const { Title } = Typography;

export function AnalysisDataOverview() {
  const [overview, setOverview] = useState([]);
  const [lineData, setLineData] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    setLoading(true);
    const { data } = await Apis.getChartOverview().finally(() => {
      setLoading(false);
    });
    setLineData(data?.chartData);
    setOverview(data?.overviewData);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatedData = useMemo(() => {
    return [
      {
        title: 'Content production',
        icon: <IconEdit />,
        value: overview[0],
        background: 'rgb(var(--orange-2))',
        color: 'rgb(var(--orange-6))',
      },
      {
        title: 'Content clicks',
        icon: <IconThumbUp />,
        value: overview[1],
        background: 'rgb(var(--cyan-2))',
        color: 'rgb(var(--cyan-6))',
      },
      {
        title: 'Content exposure',
        value: overview[2],
        icon: <IconHeart />,
        background: 'rgb(var(--arcoblue-1))',
        color: 'rgb(var(--arcoblue-6))',
      },
      {
        title: 'Active users',
        value: overview[3],
        icon: <IconUser />,
        background: 'rgb(var(--purple-1))',
        color: 'rgb(var(--purple-6))',
      },
    ];
  }, [overview]);

  return (
    <Grid.Row justify="space-between">
      {formatedData.map((item, index) => (
        <Grid.Col span={24 / formatedData.length} key={`${index}`}>
          <Card className={styles.card} title={null} bordered={false}>
            <Title heading={6}>{item.title}</Title>
            <div className={styles.content}>
              <div
                style={{ backgroundColor: item.background, color: item.color }}
                className={styles.contentIcon}
              >
                {item.icon}
              </div>
              {loading ? (
                <Skeleton
                  animation
                  text={{ rows: 1, className: styles.skeleton }}
                  style={{ width: '120px' }}
                />
              ) : (
                <Statistic value={item.value} groupSeparator />
              )}
            </div>
          </Card>
        </Grid.Col>
      ))}
      <Grid.Col span={24}>
        <MultiAreaLine data={lineData} loading={loading} />
      </Grid.Col>
    </Grid.Row>
  );
}
