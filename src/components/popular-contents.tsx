import React, { useState, useEffect } from 'react';
import { Link, Card, Radio, Table, Typography } from '@arco-design/web-react';
import { IconCaretDown, IconCaretUp } from '@arco-design/web-react/icon';
import { Apis } from '../utils/apis';
import styles from './style/popular-contents.module.less';

export function PopularContent() {
  const [type, setType] = useState(0);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);

  const fetchData = async () => {
    setLoading(true);
    const { data = [] } = await Apis.getProjectList({ payload: {} }).finally(() => {
      setLoading(false);
    });
    setData(data);
  };

  useEffect(() => {
    fetchData();
  }, [page, type]);

  const columns = [
    {
      title: 'Rank',
      key: 0,
      dataIndex: 'rank',
      width: 65,
    },
    {
      title: 'Title',
      key: 1,
      dataIndex: 'title',
      render: (x) => (
        <Typography.Paragraph style={{ margin: 0 }} ellipsis>
          {x}
        </Typography.Paragraph>
      ),
    },
    {
      title: 'PV',
      dataIndex: 'pv',
      key: 2,
      width: 100,
      render: (text) => {
        return `${text / 1000}k`;
      },
    },
    {
      title: 'Increase',
      dataIndex: 'increase',
      key: 3,
      width: 110,
      render: (text) => {
        return (
          <span>
            {Number(text).toFixed(2)}%
            <span className={styles.symbol}>
              {text < 0 ? (
                <IconCaretUp style={{ color: 'rgb(var(--green-6))' }} />
              ) : (
                <IconCaretDown style={{ color: 'rgb(var(--red-6))' }} />
              )}
            </span>
          </span>
        );
      },
    },
  ];

  return (
    <Card style={{ marginRight: 2 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography.Title heading={6} style={{ marginTop: 0 }}>
          Popular Contents
        </Typography.Title>
        <Link>See More</Link>
      </div>
      <Radio.Group
        type="button"
        value={type}
        onChange={setType}
        options={[
          { label: 'Text', value: 0 },
          { label: 'Image', value: 1 },
          { label: 'Video', value: 2 },
        ]}
        style={{ marginBottom: 16 }}
      />
      <Table
        rowKey={(record) => record.key}
        columns={columns}
        data={data}
        loading={loading}
        tableLayoutFixed
        onChange={(pagination) => {
          setPage(pagination.current);
        }}
        pagination={{ total: 1, current: page, pageSize: 5, simple: true }}
      />
    </Card>
  );
}
