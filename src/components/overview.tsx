import React, { useState, useEffect, ReactNode } from 'react';
import { Grid, Card, Typography, Divider, Skeleton, Link } from '@arco-design/web-react';
import { useUser } from '@devsre/react-utils';
import { IconCaretUp } from '@arco-design/web-react/icon';
import { OverviewAreaLine } from './chart/overview-area-line';
import styles from './style/overview.module.less';
import { ReactComponent as IconCalendar } from './assets/calendar.svg';
import { ReactComponent as IconComments } from './assets/comments.svg';
import { ReactComponent as IconContent } from './assets/content.svg';
import { ReactComponent as IconIncrease } from './assets/increase.svg';
import { Apis } from '../utils/apis';

const { Row, Col } = Grid;

type StatisticItemType = {
  icon?: ReactNode;
  title?: ReactNode;
  count?: ReactNode;
  loading?: boolean;
  unit?: ReactNode;
};

function StatisticItem(props: StatisticItemType) {
  const { icon, title, count, loading, unit } = props;
  return (
    <div className={styles.item}>
      <div className={styles.icon}>{icon}</div>
      <div>
        <Skeleton loading={loading} text={{ rows: 2, width: 60 }} animation>
          <div className={styles.title}>{title}</div>
          <div className={styles.count}>
            {count}
            <span className={styles.unit}>{unit}</span>
          </div>
        </Skeleton>
      </div>
    </div>
  );
}

type DataType = {
  all?: string;
  iteration?: string;
  lastDay?: string;
  lastMonth?: string;
  lastWeek?: { count?: number; date?: string }[];
  thisMonth?: string;
  today?: string;
};

export function Overview() {
  const [data, setData] = useState<DataType>({});
  const [loading, setLoading] = useState(true);
  const user = useUser();

  const fetchData = async () => {
    setLoading(true);
    const { data } = await Apis.getBnpmDownPackage({ payload: {} }).finally(() => {
      setLoading(false);
    });
    setData(data);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <Card style={{ marginBottom: '12px' }}>
      <Typography.Title heading={5} style={{ marginTop: 0 }}>{`Welcome Back, ${
        user.name || user.nickname
      }`}</Typography.Title>
      <Divider />
      <Row>
        <Col flex={1}>
          <StatisticItem
            icon={<IconCalendar />}
            title="Total online data"
            count={data?.all}
            loading={loading}
            unit="pecs"
          />
        </Col>
        <Divider type="vertical" className={styles.divider} />
        <Col flex={1}>
          <StatisticItem
            icon={<IconContent />}
            title="This month"
            count={data?.thisMonth}
            loading={loading}
            unit="pecs"
          />
        </Col>
        <Divider type="vertical" className={styles.divider} />
        <Col flex={1}>
          <StatisticItem
            icon={<IconComments />}
            title="Last month"
            count={data?.lastMonth}
            loading={loading}
            unit="pecs"
          />
        </Col>
        <Divider type="vertical" className={styles.divider} />
        <Col flex={1}>
          <StatisticItem
            icon={<IconIncrease />}
            title="Growth"
            count={
              <span>
                {data?.iteration}%
                <IconCaretUp style={{ fontSize: 18, color: 'rgb(var(--green-6))' }} />
              </span>
            }
            loading={loading}
          />
        </Col>
      </Row>
      <Divider />
      <div>
        <div className={styles.ctw}>
          <Typography.Paragraph className={styles.chartTitle} style={{ marginBottom: 0 }}>
            Content Data
            <span className={styles.chartSubTitle}>({'Nearly 1 Year'})</span>
          </Typography.Paragraph>
          <Link>See More</Link>
        </div>
        <OverviewAreaLine />
      </div>
    </Card>
  );
}
