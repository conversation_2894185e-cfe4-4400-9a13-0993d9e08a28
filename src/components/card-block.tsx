import React, { useEffect, useState } from 'react';
import cs from 'classnames';
import {
  Button,
  Switch,
  Tag,
  Card,
  Descriptions,
  Typography,
  Dropdown,
  Menu,
  Skeleton,
} from '@arco-design/web-react';
import {
  IconStarFill,
  IconThumbUpFill,
  IconSunFill,
  IconFaceSmileFill,
  IconPenFill,
  IconCheckCircleFill,
  IconCloseCircleFill,
  IconMore,
} from '@arco-design/web-react/icon';
import styles from './style/card.module.less';

export interface QualityInspection {
  title?: string;
  time?: string;
  qualityCount?: number;
  randomCount?: number;
  duration?: number;
}

export interface BasicCard {
  icon?: number;
  status?: 0 | 1 | 2;
  description?: string;
}

interface CardBlockType {
  type: 'quality' | 'service' | 'rules';
  card: QualityInspection & BasicCard;
  loading?: boolean;
}

const IconList = [IconStarFill, IconThumbUpFill, IconSunFill, IconFaceSmileFill, IconPenFill].map(
  (Tag, index) => <Tag key={index} />
);

const { Paragraph } = Typography;

export function CardBlock(props: CardBlockType) {
  const { type, card = {} } = props;
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState(card.status);
  const [loading, setLoading] = useState(props.loading);

  const changeStatus = async () => {
    setLoading(true);
    await new Promise((resolve) =>
      setTimeout(() => {
        setStatus(status !== 1 ? 1 : 0);
        resolve(null);
      }, 1000)
    ).finally(() => setLoading(false));
  };

  useEffect(() => {
    setLoading(props.loading);
  }, [props.loading]);

  useEffect(() => {
    if (card.status !== status) {
      setStatus(card.status);
    }
  }, [card.status]);

  const getTitleIcon = () => {
    if (type === 'service' && typeof card.icon === 'number') {
      return <div className={styles.icon}>{IconList[card.icon % IconList.length]}</div>;
    }
    return null;
  };

  const getButtonGroup = () => {
    if (type === 'quality') {
      return (
        <>
          <Button type="primary" style={{ marginLeft: '12px' }} loading={loading}>
            Quality inspection
          </Button>
          <Button loading={loading}>Remove</Button>
        </>
      );
    }

    if (type === 'service') {
      return (
        <>
          {status === 1 ? (
            <Button loading={loading} onClick={changeStatus}>
              Cancel
            </Button>
          ) : (
            <Button type="outline" loading={loading} onClick={changeStatus}>
              {status === 0 ? 'Subscribe' : 'Renewal'}
            </Button>
          )}
        </>
      );
    }

    return <Switch checked={!!status} loading={loading} onChange={changeStatus} />;
  };

  const getStatus = () => {
    if (type === 'rules' && status) {
      return (
        <Tag color="green" icon={<IconCheckCircleFill />} className={styles.status} size="small">
          Activated
        </Tag>
      );
    }
    switch (status) {
      case 1:
        return (
          <Tag color="green" icon={<IconCheckCircleFill />} className={styles.status} size="small">
            Opened
          </Tag>
        );
      case 2:
        return (
          <Tag color="red" icon={<IconCloseCircleFill />} className={styles.status} size="small">
            Expired
          </Tag>
        );
      default:
        return null;
    }
  };

  const getContent = () => {
    if (loading) {
      return (
        <Skeleton
          text={{ rows: type !== 'quality' ? 3 : 2 }}
          animation
          className={styles.cardClockSkeleton}
        />
      );
    }
    if (type !== 'quality') {
      return <Paragraph>{card.description}</Paragraph>;
    }
    return (
      <Descriptions
        column={2}
        data={[
          { label: 'Inspected', value: card.qualityCount },
          { label: 'Backlog time', value: `${card.duration}s` },
          { label: 'Sampling number', value: card.randomCount },
        ]}
      />
    );
  };

  return (
    <Card
      bordered={true}
      className={[styles.cardBlock, styles[`${type}Card`]]}
      size="small"
      title={
        loading ? (
          <Skeleton
            animation
            text={{ rows: 1, width: ['100%'] }}
            style={{ width: '120px', height: '24px' }}
            className={styles.cardBlockSkeleton}
          />
        ) : (
          <>
            <div
              className={cs(styles.title, {
                [styles.titleMore]: visible,
              })}
            >
              {getTitleIcon()}
              {card.title}
              {getStatus()}
              <Dropdown
                droplist={
                  <Menu>
                    {['Operation 1', 'Operation 2'].map((item, key) => (
                      <Menu.Item key={key.toString()}>{item}</Menu.Item>
                    ))}
                  </Menu>
                }
                trigger="click"
                onVisibleChange={setVisible}
                popupVisible={visible}
              >
                <div className={styles.more}>
                  <IconMore />
                </div>
              </Dropdown>
            </div>
            <div className={styles.time}>{card.time}</div>
          </>
        )
      }
    >
      <div className={styles.content}>{getContent()}</div>
      <div className={styles.extra}>{getButtonGroup()}</div>
    </Card>
  );
}
