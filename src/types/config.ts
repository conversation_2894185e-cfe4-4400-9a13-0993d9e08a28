// 配置相关的TypeScript类型定义

// 指标配置项
export interface PerfMetricConfigItem {
  metric_id: number;             // 指标ID
  metric_key: string;            // 指标key
  metric_name: string;           // 指标名称
  metric_type: number;           // 指标类型 1-common 2-android 3-ios
  metric_category: number;       // 指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-VQoS VQoS Trace指标 4-ByteIO指标
  metric_unit?: string;          // 指标单位
  metric_desc?: string;          // 指标描述
  is_important: boolean;         // 是否重要指标
  comparison_type?: number;      // 比对标准类型 1-增加 2-减少
  value_type?: number;           // 值类型 1-百分比 2-绝对值
  threshold_value?: number;      // 阈值
}

// 配置项
export interface PerfConfigItem {
  id: number;                    // 配置ID
  business_id: number;           // 业务线ID
  config_name: string;           // 配置名称
  metrics: PerfMetricConfigItem[]; // 性能指标列表
  creator: string;               // 创建者
  create_time: string;           // 创建时间 (ISO 8601)
  update_time: string;           // 更新时间 (ISO 8601)
}

// 配置列表响应
export interface PerfConfigListResponse {
  page: number;                  // 页码
  page_size: number;             // 每页数量
  total: number;                 // 总记录数
  items: PerfConfigItem[];       // 配置列表
}

// 配置创建请求参数
export interface CreateConfigRequest {
  business_id: number;
  config_name: string;
  metrics: Array<{
    metric_id: number;
    is_important: boolean;
    comparison_type?: number;
    value_type?: number;
    threshold_value?: number;
  }>;
  creator: string;
}

// 配置更新请求参数
export interface UpdateConfigRequest {
  id: number;
  business_id?: number;
  config_name?: string;
  metrics?: Array<{
    metric_id: number;
    is_important: boolean;
    comparison_type?: number;
    value_type?: number;
    threshold_value?: number;
  }>;
}

// 配置列表查询参数
export interface ConfigListQueryParams {
  business_id?: number;
  config_name?: string;
  creator?: string;
  start_time?: number;
  end_time?: number;
  page?: number;
  page_size?: number;
}
