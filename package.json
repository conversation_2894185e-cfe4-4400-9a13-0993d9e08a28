{"name": "test_platform_frontend_devsre", "version": "0.0.1", "dependencies": {"@arco-design/web-react": "^2.38.1", "@babel/eslint-parser": "^7.19.1", "@bytecloud/common-lib": "^7.25.1", "@devsre-plugin/byte-cloud-provider": "^3.0.12", "@devsre/builder": "^0.1.1-alpha-20230808095939", "@devsre/react-utils": "^3.3.4", "@devsre/request": "^3.0.12", "@visactor/react-vchart": "^1.12.6", "@visactor/vchart-arco-theme": "^1.12.1", "bizcharts": "^4.1.20", "classnames": "^2.3.1", "dayjs": "^1.11.4", "lodash": "^4.17.21", "react": "^18", "react-dom": "^18", "react-router-dom": "^6.8.2"}, "devDependencies": {"@devsre/eslint-config": "^1.0.0", "@devsre/preset-react": "^2.2.4", "@devsre/preset-service": "^1.1.17", "@devsre/scripts": "^3.6.7", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-router-dom": "^5.3.3", "eslint": "^8.21.0", "prettier": "^2.7.1", "typescript": "^4.7.4"}, "files": ["public/*", "src/**/*", ".eslintrc.js", ".prettier<PERSON>", ".giti<PERSON>re", ".npmrc", "tsconfig.json", "package-lock.json", "yarn.lock", "pnpm-lock.yaml", "scm_build.sh"], "scripts": {"start": "devsre-scripts start", "build": "devsre-scripts build", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint --fix src --ext .ts,.tsx,.js,.jsx"}, "businessMeta": "devsre", "private": true, "repository": {"type": "git", "url": "https://code.byted.org/maweijia.1211/test_platform_frontend_devsre"}}